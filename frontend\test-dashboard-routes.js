// Dashboard Routes Testing Script
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:8000';
const FRONTEND_URL = 'http://localhost:3000';

const TEST_USERS = {
  user: { username: 'testuser', password: 'testpass123', expectedDashboard: '/dashboard' },
  mentor: { username: 'testmentor', password: 'testpass123', expectedDashboard: '/dashboard/mentorship' },
  investor: { username: 'testinvestor', password: 'testpass123', expectedDashboard: '/dashboard/investments' },
  moderator: { username: 'testmoderator', password: 'testpass123', expectedDashboard: '/dashboard/moderation' },
  admin: { username: 'testadmin', password: 'testpass123', expectedDashboard: '/admin' },
  superadmin: { username: 'testsuperadmin', password: 'testpass123', expectedDashboard: '/super_admin' }
};

let testResults = {
  passed: 0,
  failed: 0,
  details: []
};

async function loginUser(userType, userData) {
  try {
    const response = await fetch(`${BASE_URL}/api/auth/token/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: userData.username,
        password: userData.password
      })
    });

    if (!response.ok) {
      throw new Error(`Login failed with status ${response.status}`);
    }

    const data = await response.json();
    return {
      token: data.access,
      user: data.user
    };
  } catch (error) {
    console.error(`❌ Login failed for ${userType}: ${error.message}`);
    return null;
  }
}

async function testFrontendRoute(route, token, userType) {
  try {
    // Test if the frontend route is accessible
    const response = await fetch(`${FRONTEND_URL}${route}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
      }
    });

    return {
      status: response.status,
      ok: response.ok,
      route: route,
      userType: userType
    };
  } catch (error) {
    return {
      status: 0,
      ok: false,
      error: error.message,
      route: route,
      userType: userType
    };
  }
}

async function testDashboardAPI(token, userType) {
  console.log(`   🔍 Testing dashboard API access for ${userType}...`);
  
  const apiEndpoints = [
    '/api/dashboard/stats/',
    '/api/admin/dashboard/',
    '/api/users/profile/',
    '/api/incubator/dashboard/',
  ];

  const results = [];

  for (const endpoint of apiEndpoints) {
    try {
      const response = await fetch(`${BASE_URL}${endpoint}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      });

      results.push({
        endpoint,
        status: response.status,
        ok: response.ok,
        accessible: response.ok
      });

      if (response.ok) {
        console.log(`   ✅ ${userType} can access ${endpoint}`);
      } else if (response.status === 403 || response.status === 401) {
        console.log(`   🔒 ${userType} properly denied access to ${endpoint}`);
      } else {
        console.log(`   ⚠️  ${endpoint} returned ${response.status} for ${userType}`);
      }

    } catch (error) {
      console.log(`   ❌ Error testing ${endpoint} for ${userType}: ${error.message}`);
      results.push({
        endpoint,
        status: 0,
        ok: false,
        accessible: false,
        error: error.message
      });
    }
  }

  return results;
}

async function testRoleDashboardFunctionality(userType, userData) {
  console.log(`\n🧪 Testing dashboard functionality for ${userType}...`);
  
  try {
    // Login first
    const loginResult = await loginUser(userType, userData);
    if (!loginResult) {
      throw new Error('Login failed');
    }

    const { token, user } = loginResult;
    console.log(`✅ ${userType} logged in successfully`);

    // Test API access
    const apiResults = await testDashboardAPI(token, userType);

    // Test expected dashboard route accessibility
    const expectedRoute = userData.expectedDashboard;
    console.log(`   🔍 Testing expected dashboard route: ${expectedRoute}`);
    
    const routeResult = await testFrontendRoute(expectedRoute, token, userType);
    
    if (routeResult.ok || routeResult.status === 200) {
      console.log(`   ✅ ${userType} can access expected dashboard route: ${expectedRoute}`);
    } else {
      console.log(`   ⚠️  ${userType} dashboard route ${expectedRoute} returned status: ${routeResult.status}`);
    }

    // Test common routes that should be accessible to all authenticated users
    const commonRoutes = ['/dashboard', '/profile', '/settings'];
    
    for (const route of commonRoutes) {
      const result = await testFrontendRoute(route, token, userType);
      if (result.ok || result.status === 200) {
        console.log(`   ✅ ${userType} can access common route: ${route}`);
      } else {
        console.log(`   ⚠️  ${userType} common route ${route} returned status: ${result.status}`);
      }
    }

    // Test role-specific routes
    const roleSpecificRoutes = {
      admin: ['/admin', '/admin/users', '/admin/dashboard'],
      superadmin: ['/super_admin', '/admin', '/admin/users'],
      mentor: ['/dashboard/mentorship', '/dashboard/mentorship/mentees'],
      investor: ['/dashboard/investments', '/dashboard/investments/opportunities'],
      moderator: ['/dashboard/moderation', '/dashboard/moderation/reports']
    };

    if (roleSpecificRoutes[userType]) {
      console.log(`   🔍 Testing role-specific routes for ${userType}...`);
      for (const route of roleSpecificRoutes[userType]) {
        const result = await testFrontendRoute(route, token, userType);
        if (result.ok || result.status === 200) {
          console.log(`   ✅ ${userType} can access role-specific route: ${route}`);
        } else {
          console.log(`   ⚠️  ${userType} role-specific route ${route} returned status: ${result.status}`);
        }
      }
    }

    testResults.passed++;
    testResults.details.push({
      userType,
      status: 'PASSED',
      role: user.user_role,
      apiResults,
      message: 'Dashboard functionality tests completed'
    });

  } catch (error) {
    console.error(`❌ Dashboard test failed for ${userType}: ${error.message}`);
    testResults.failed++;
    testResults.details.push({
      userType,
      status: 'FAILED',
      error: error.message
    });
  }
}

async function testUnauthorizedAccess() {
  console.log('\n🧪 Testing unauthorized access to protected routes...');
  
  const protectedRoutes = [
    '/admin',
    '/super_admin',
    '/dashboard',
    '/dashboard/mentorship',
    '/dashboard/investments'
  ];

  for (const route of protectedRoutes) {
    try {
      const response = await fetch(`${FRONTEND_URL}${route}`, {
        method: 'GET',
        headers: {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
        }
      });

      // Should redirect to login or return 401/403
      if (response.status === 401 || response.status === 403 || 
          response.url.includes('/login') || response.status === 302) {
        console.log(`✅ Unauthorized access to ${route} properly blocked`);
      } else {
        console.log(`⚠️  Unauthorized access to ${route} returned status: ${response.status}`);
      }

    } catch (error) {
      console.log(`⚠️  Error testing unauthorized access to ${route}: ${error.message}`);
    }
  }
}

async function runDashboardTests() {
  console.log('🚀 Starting Dashboard Functionality Tests');
  console.log('=' * 50);
  
  // Test each user role's dashboard functionality
  for (const [userType, userData] of Object.entries(TEST_USERS)) {
    await testRoleDashboardFunctionality(userType, userData);
  }
  
  // Test unauthorized access
  await testUnauthorizedAccess();
  
  // Print results
  console.log('\n📊 Dashboard Test Results');
  console.log('=' * 30);
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
  
  console.log('\n📋 Dashboard Test Summary:');
  testResults.details.forEach(detail => {
    const status = detail.status === 'PASSED' ? '✅' : '❌';
    console.log(`\n   ${status} ${detail.userType.toUpperCase()}`);
    if (detail.status === 'PASSED') {
      console.log(`      Role: ${detail.role}`);
      console.log(`      API Access: ${detail.apiResults?.filter(r => r.accessible).length || 0} endpoints accessible`);
      console.log(`      Status: ${detail.message}`);
    } else {
      console.log(`      Error: ${detail.error}`);
    }
  });
  
  console.log('\n🎯 Dashboard System Assessment:');
  if (testResults.failed === 0) {
    console.log('✅ All dashboard tests passed! Role-based access is working correctly.');
  } else {
    console.log('⚠️  Some dashboard tests failed. Review the errors above.');
  }
  
  return testResults;
}

// Run the tests
runDashboardTests().catch(console.error);
