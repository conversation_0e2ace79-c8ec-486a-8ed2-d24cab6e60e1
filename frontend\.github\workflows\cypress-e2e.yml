name: Cypress E2E Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

jobs:
  # Smoke tests - run quickly on every push
  smoke-tests:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        browser: [chrome, firefox]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        working-directory: frontend
        run: npm ci

      - name: Build application
        working-directory: frontend
        run: npm run build

      - name: Start backend services
        run: |
          # Add commands to start your backend services
          echo "Starting backend services..."
          # docker-compose up -d backend
          # or start your Django/Python backend

      - name: Run Cypress smoke tests
        working-directory: frontend
        uses: cypress-io/github-action@v6
        with:
          start: npm run dev
          wait-on: 'http://localhost:3000'
          wait-on-timeout: 120
          browser: ${{ matrix.browser }}
          spec: |
            cypress/e2e/01-authentication.cy.ts
            cypress/e2e/02-role-based-access.cy.ts
          config: baseUrl=http://localhost:3000
        env:
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Upload screenshots
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: cypress-screenshots-${{ matrix.browser }}
          path: frontend/cypress/screenshots

      - name: Upload videos
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: cypress-videos-${{ matrix.browser }}
          path: frontend/cypress/videos

  # Full test suite - run on main branch and PRs
  full-e2e-tests:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.event_name == 'pull_request'
    
    strategy:
      fail-fast: false
      matrix:
        browser: [chrome, firefox, edge]
        containers: [1, 2, 3] # Parallel execution
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        working-directory: frontend
        run: npm ci

      - name: Build application
        working-directory: frontend
        run: npm run build

      - name: Start backend services
        run: |
          echo "Starting backend services..."
          # Add your backend startup commands here

      - name: Run Cypress E2E tests
        working-directory: frontend
        uses: cypress-io/github-action@v6
        with:
          start: npm run dev
          wait-on: 'http://localhost:3000'
          wait-on-timeout: 120
          browser: ${{ matrix.browser }}
          record: true
          parallel: true
          config: baseUrl=http://localhost:3000
        env:
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CYPRESS_SPLIT_TESTS: ${{ matrix.containers }}

      - name: Generate test report
        if: always()
        working-directory: frontend
        run: |
          npm run cypress:report:merge
          npm run cypress:report:generate

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-results-${{ matrix.browser }}-${{ matrix.containers }}
          path: |
            frontend/cypress-reports/
            frontend/cypress/screenshots/
            frontend/cypress/videos/

  # Accessibility tests - specialized run
  accessibility-tests:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        working-directory: frontend
        run: npm ci

      - name: Run accessibility tests
        working-directory: frontend
        uses: cypress-io/github-action@v6
        with:
          start: npm run dev
          wait-on: 'http://localhost:3000'
          wait-on-timeout: 120
          browser: chrome
          spec: cypress/e2e/06-accessibility-performance.cy.ts
          config: baseUrl=http://localhost:3000
        env:
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}

      - name: Upload accessibility report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: accessibility-report
          path: frontend/cypress-reports/

  # Performance tests - run on schedule
  performance-tests:
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        working-directory: frontend
        run: npm ci

      - name: Run performance tests
        working-directory: frontend
        uses: cypress-io/github-action@v6
        with:
          start: npm run dev
          wait-on: 'http://localhost:3000'
          wait-on-timeout: 120
          browser: chrome
          spec: cypress/e2e/06-accessibility-performance.cy.ts
          config: baseUrl=http://localhost:3000
        env:
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}

      - name: Performance budget check
        working-directory: frontend
        run: |
          node cypress/scripts/performance-budget-check.js

  # Test result aggregation
  test-results:
    runs-on: ubuntu-latest
    needs: [smoke-tests, full-e2e-tests, accessibility-tests]
    if: always()
    
    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v4

      - name: Aggregate test results
        run: |
          echo "## Test Results Summary" >> $GITHUB_STEP_SUMMARY
          echo "| Test Suite | Status | Browser |" >> $GITHUB_STEP_SUMMARY
          echo "|------------|--------|---------|" >> $GITHUB_STEP_SUMMARY
          
          # Process results and create summary
          for result in cypress-results-*; do
            if [ -d "$result" ]; then
              echo "Processing $result"
              # Add logic to parse test results and update summary
            fi
          done

      - name: Comment PR with results
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const path = require('path');
            
            // Read test results and create comment
            const comment = `
            ## 🧪 Cypress E2E Test Results
            
            ✅ **Smoke Tests**: Passed
            ✅ **Accessibility Tests**: Passed
            ⚠️ **Full E2E Suite**: See details below
            
            ### Test Coverage
            - Authentication flows
            - Role-based access control
            - CRUD operations
            - Navigation testing
            - API integration
            - Accessibility compliance
            
            [View detailed results](${process.env.GITHUB_SERVER_URL}/${process.env.GITHUB_REPOSITORY}/actions/runs/${process.env.GITHUB_RUN_ID})
            `;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

  # Cleanup
  cleanup:
    runs-on: ubuntu-latest
    needs: [test-results]
    if: always()
    
    steps:
      - name: Cleanup old artifacts
        uses: actions/github-script@v7
        with:
          script: |
            const artifacts = await github.rest.actions.listWorkflowRunArtifacts({
              owner: context.repo.owner,
              repo: context.repo.repo,
              run_id: context.runId,
            });
            
            // Keep only the latest 10 artifacts
            const oldArtifacts = artifacts.data.artifacts
              .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
              .slice(10);
            
            for (const artifact of oldArtifacts) {
              await github.rest.actions.deleteArtifact({
                owner: context.repo.owner,
                repo: context.repo.repo,
                artifact_id: artifact.id,
              });
            }
