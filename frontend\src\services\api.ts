// Simple API service
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

// API Error class for structured error handling
export class ApiError extends Error {
  public status: number;
  public data?: any;

  constructor(message: string, status: number, data?: any) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
  }
}

// Extract error message from API response
export function extractErrorMessage(error: any): string {
  if (typeof error === 'string') {
    return error;
  }
  if (error?.message) {
    return error.message;
  }
  if (error?.detail) {
    return error.detail;
  }
  if (error?.error) {
    return error.error;
  }
  return 'An unknown error occurred';
}

// Get CSRF token from cookies or meta tag
export function getCsrfToken(): string | null {
  // Try to get from cookie first
  const cookies = document.cookie.split(';');
  for (let cookie of cookies) {
    const [name, value] = cookie.trim().split('=');
    if (name === 'csrftoken') {
      return value;
    }
  }

  // Try to get from meta tag
  const metaTag = document.querySelector('meta[name="csrf-token"]') as HTMLMetaElement;
  if (metaTag) {
    return metaTag.content;
  }

  return null;
}

// Refresh authentication token
export async function refreshToken(): Promise<string | null> {
  const refreshTokenValue = localStorage.getItem('refresh_token');
  if (!refreshTokenValue) {
    return null;
  }

  try {
    const response = await fetch(`${API_URL}/auth/token/refresh/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refresh: refreshTokenValue }),
    });

    if (!response.ok) {
      throw new Error('Token refresh failed');
    }

    const data = await response.json();
    const newAccessToken = data.access;

    if (newAccessToken) {
      localStorage.setItem('access_token', newAccessToken);
      return newAccessToken;
    }

    return null;
  } catch (error) {
    console.error('Token refresh error:', error);
    clearAuthTokens();
    return null;
  }
}

// Token keys
const TOKEN_KEY = 'yasmeen_auth_token';
const REFRESH_TOKEN_KEY = 'yasmeen_refresh_token';

// User interface
export interface User {
  id: number;
  username: string;
  email: string;
  first_name?: string;
  last_name?: string;
  role?: string;
  profile?: {
    role?: string;
    bio?: string;
    location?: string;
    website?: string;
    avatar?: string;
    active_roles?: Array<{ name: string; permission_level: string; display_name: string }>;
    primary_role?: { name: string; permission_level: string; display_name: string };
    highest_permission_level?: string;
  };
  is_admin?: boolean;
  is_staff?: boolean;
  is_superuser?: boolean;
  // New fields from backend UserSerializer
  user_role?: string;
  role_permissions?: string[];
}

// Event interface
export interface Event {
  id: number;
  title: string;
  description: string;
  date: string;
  location: string;
  organizer: string;
  attendees?: number;
  max_attendees?: number;
  is_attending?: boolean;
}

// Create event data interface
export interface CreateEventData {
  title: string;
  description: string;
  date: string;
  location: string;
}

// User profile interface
export interface UserProfile {
  id?: number;
  user?: number;
  bio?: string;
  location?: string;
  website?: string;
  avatar?: string;
  phone?: string;
  date_of_birth?: string;
  primary_role?: any;
  active_roles?: any[];
}

// Dashboard stats interfaces
export interface DashboardStats {
  users: {
    total_users: number;
    active_users: number;
    new_users: number;
  };
  events: {
    total_events: number;
    upcoming_events: number;
    new_events: number;
  };
  business_plans: {
    total_plans: number;
    active_plans: number;
    new_plans: number;
  };
}

// Recent activity interface
export interface RecentActivity {
  id: number;
  type: string;
  description: string;
  timestamp: string;
  user?: string;
}

// Membership application interface
export interface MembershipApplication {
  id?: number;
  full_name: string;
  email: string;
  phone: string;
  country: string;
  state: string;
  location: string;
  expertise_areas: string;
  expertise_level: string;
  background: string;
  motivation: string;
  linkedin_profile: string;
  github_profile: string;
  portfolio_url: string;
  status?: string;
  created_at?: string;
}

// Simple token functions
export function getAuthToken(): string | null {
  return localStorage.getItem(TOKEN_KEY);
}

export function getRefreshToken(): string | null {
  return localStorage.getItem(REFRESH_TOKEN_KEY);
}

export function setAuthTokens(accessToken: string, refreshToken: string): void {
  localStorage.setItem(TOKEN_KEY, accessToken);
  localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken);
}

export function clearAuthTokens(): void {
  localStorage.removeItem(TOKEN_KEY);
  localStorage.removeItem(REFRESH_TOKEN_KEY);
}

export function getTokenExpiry(): number | null {
  const token = getAuthToken();
  if (!token) return null;
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.exp * 1000; // Convert to milliseconds
  } catch {
    return null;
  }
}

export function isTokenValid(): boolean {
  const token = getAuthToken();
  if (!token) return false;
  
  const expiry = getTokenExpiry();
  if (!expiry) return false;
  
  return Date.now() < expiry;
}

// Simple API request function - exported for use by other API services
export async function apiRequest<T>(endpoint: string, method: string = 'GET', data?: any): Promise<T> {
  const token = getAuthToken();
  
  const config: RequestInit = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    },
  };

  if (data && method !== 'GET') {
    config.body = JSON.stringify(data);
  }

  const response = await fetch(`${API_URL}${endpoint}`, config);

  if (!response.ok) {
    let errorData;
    let errorText;

    try {
      // Clone the response to avoid "body stream already read" error
      const responseClone = response.clone();
      errorText = await responseClone.text();

      // Try to parse as JSON first
      if (errorText) {
        try {
          errorData = JSON.parse(errorText);
        } catch {
          errorData = errorText;
        }
      } else {
        errorData = `HTTP ${response.status}`;
      }
    } catch {
      errorData = `HTTP ${response.status}`;
    }

    // Handle authentication errors
    if (response.status === 401) {
      console.error('Authentication failed, clearing tokens');
      clearAuthTokens();

      // Dispatch Redux action to clear auth state
      if (typeof window !== 'undefined' && (window as any).__REDUX_STORE__) {
        const store = (window as any).__REDUX_STORE__;
        store.dispatch({ type: 'auth/clearAuth' });
      }

      // Don't redirect automatically, let the component handle it
      // But dispatch a custom event for components to listen to
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('auth-error', {
          detail: { status: 401, message: 'Authentication required' }
        }));
      }
    }

    const errorMessage = typeof errorData === 'string'
      ? errorData
      : errorData?.message || errorData?.detail || errorData?.error || `HTTP ${response.status}`;

    throw new ApiError(errorMessage, response.status, errorData);
  }

  // For successful responses, parse JSON
  try {
    return await response.json();
  } catch (e) {
    // If JSON parsing fails, return empty object for successful responses
    return {};
  }
}

// Auth API
export const authAPI = {
  async login(username: string, password: string): Promise<User> {
    const response = await apiRequest<{ access: string; refresh: string; user: User }>('/auth/token/', 'POST', {
      username,
      password,
    });

    console.log('🔍 authAPI.login response:', response);
    console.log('🔍 authAPI.login response.user:', response.user);

    setAuthTokens(response.access, response.refresh);
    return response.user;
  },

  async logout(): Promise<void> {
    try {
      await apiRequest('/auth/logout/', 'POST');
    } finally {
      clearAuthTokens();
    }
  },

  async register(userData: {
    username: string;
    email: string;
    password: string;
    first_name?: string;
    last_name?: string;
  }): Promise<User> {
    const response = await apiRequest<{ access: string; refresh: string; user: User }>('/auth/register/', 'POST', userData);
    
    setAuthTokens(response.access, response.refresh);
    return response.user;
  },

  async getCurrentUser(): Promise<User> {
    return apiRequest<User>('/auth/user/');
  },

  async refreshToken(): Promise<boolean> {
    const refreshToken = getRefreshToken();
    if (!refreshToken) {
      console.log('❌ No refresh token available');
      return false;
    }

    try {
      console.log('🔄 Attempting token refresh...');
      const response = await apiRequest<{ access: string }>('/auth/refresh/', 'POST', {
        refresh: refreshToken,
      });

      if (response.access) {
        setAuthTokens(response.access, refreshToken);
        console.log('✅ Token refreshed successfully');
        return true;
      } else {
        console.log('❌ Token refresh failed: No access token in response');
        clearAuthTokens();
        return false;
      }
    } catch (error) {
      console.log('❌ Token refresh failed:', error);
      clearAuthTokens();
      return false;
    }
  },
};

// Events API
export const eventsAPI = {
  async getEvents(): Promise<Event[]> {
    return apiRequest<Event[]>('/events/');
  },

  async createEvent(eventData: CreateEventData): Promise<Event> {
    return apiRequest<Event>('/events/', 'POST', eventData);
  },

  async attendEvent(eventId: number): Promise<void> {
    return apiRequest<void>(`/events/${eventId}/attend/`, 'POST');
  },

  async unattendEvent(eventId: number): Promise<void> {
    return apiRequest<void>(`/events/${eventId}/unattend/`, 'POST');
  },
};

// Admin API
export const adminAPI = {
  async getAllStats(): Promise<DashboardStats> {
    return apiRequest<DashboardStats>('/admin/stats/');
  },

  async getUsers(): Promise<User[]> {
    return apiRequest<User[]>('/admin/users/');
  },

  async createUser(userData: Partial<User>): Promise<User> {
    return apiRequest<User>('/admin/users/', 'POST', userData);
  },

  async updateUser(userId: number, userData: Partial<User>): Promise<User> {
    return apiRequest<User>(`/admin/users/${userId}/`, 'PATCH', userData);
  },

  async updateUserProfile(userId: number, profileData: Partial<UserProfile>): Promise<UserProfile> {
    return apiRequest<UserProfile>(`/admin/users/${userId}/profile/`, 'PATCH', profileData);
  },

  async deleteUser(userId: number): Promise<void> {
    return apiRequest<void>(`/admin/users/${userId}/`, 'DELETE');
  },

  async getRecentActivity(): Promise<RecentActivity[]> {
    return apiRequest<RecentActivity[]>('/admin/activity/');
  },

  // Individual stats methods for backward compatibility
  async getUserStats(): Promise<any> {
    const stats = await this.getAllStats();
    return stats.users;
  },

  async getEventStats(): Promise<any> {
    const stats = await this.getAllStats();
    return stats.events;
  },

  async getPostStats(): Promise<any> {
    const stats = await this.getAllStats();
    return stats.posts;
  },

  async getResourceStats(): Promise<any> {
    const stats = await this.getAllStats();
    return stats.resources;
  },
};

// User Activity interface
export interface UserActivity {
  post_count: number;
  comment_count: number;
  event_count: number;
  resource_count: number;
  likes_received: number;
  engagement_score: number;
  posts_by_day: Record<string, number>;
  comments_by_day: Record<string, number>;
  recent_posts: any[];
  recent_comments: any[];
  recent_events: any[];
}

// User API
export const userAPI = {
  async getUserProfile(userId: string): Promise<User> {
    return apiRequest<User>(`/users/users/${userId}/`);
  },

  async getUserActivity(): Promise<UserActivity> {
    return apiRequest<UserActivity>('/users/users/user_activity/');
  },

  async getForumActivity(): Promise<any> {
    return apiRequest<any>('/users/users/forum_activity/');
  },

  async updateProfile(profileData: any): Promise<User> {
    return apiRequest<User>('/users/users/me/', 'PATCH', profileData);
  },
};

// Membership API
export const membershipAPI = {
  async createApplication(applicationData: Omit<MembershipApplication, 'id' | 'status' | 'created_at'>): Promise<MembershipApplication> {
    return apiRequest<MembershipApplication>('/membership/applications/', 'POST', applicationData);
  },

  async getApplications(): Promise<MembershipApplication[]> {
    return apiRequest<MembershipApplication[]>('/membership/applications/');
  },

  async getApplication(id: number): Promise<MembershipApplication> {
    return apiRequest<MembershipApplication>(`/membership/applications/${id}/`);
  },

  async updateApplicationStatus(id: number, status: string): Promise<MembershipApplication> {
    return apiRequest<MembershipApplication>(`/membership/applications/${id}/`, 'PATCH', { status });
  },
};

// Posts API
export const postsAPI = {
  async getPosts(): Promise<any[]> {
    return apiRequest<any[]>('/posts/');
  },

  async getPost(id: string): Promise<any> {
    return apiRequest<any>(`/posts/${id}/`);
  },

  async createPost(postData: any): Promise<any> {
    return apiRequest<any>('/posts/', 'POST', postData);
  },

  async updatePost(id: string, postData: any): Promise<any> {
    return apiRequest<any>(`/posts/${id}/`, 'PUT', postData);
  },

  async deletePost(id: string): Promise<void> {
    return apiRequest<void>(`/posts/${id}/`, 'DELETE');
  },
};

// Search interfaces
export interface SearchResult {
  id: string;
  title: string;
  description: string;
  type: 'business_idea' | 'business_plan' | 'template' | 'post' | 'user';
  url?: string;
  score?: number;
  metadata?: Record<string, any>;
}

export interface SearchFilters {
  type?: string[];
  category?: string[];
  dateRange?: {
    start?: string;
    end?: string;
  };
  tags?: string[];
  author?: string;
  status?: string[];
}

export interface SearchResponse {
  results: SearchResult[];
  total: number;
  page: number;
  pageSize: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

// Resource interface
export interface Resource {
  id: number;
  title: string;
  description: string;
  type: 'document' | 'video' | 'link' | 'template' | 'guide';
  category: string;
  url?: string;
  file_url?: string;
  tags: string[];
  author: string;
  created_at: string;
  updated_at: string;
  downloads: number;
  rating: number;
  is_featured: boolean;
  is_premium: boolean;
}

// Resources API
export const resourcesAPI = {
  async getResources(): Promise<Resource[]> {
    try {
      return apiRequest<Resource[]>('/resources/');
    } catch (error) {
      // Return mock data if API is not available
      console.warn('Resources API not available, returning mock data');
      return [
        {
          id: 1,
          title: 'Business Plan Template',
          description: 'Comprehensive business plan template for startups',
          type: 'template',
          category: 'business-planning',
          file_url: '/templates/business-plan.pdf',
          tags: ['business', 'planning', 'startup'],
          author: 'Admin',
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:00:00Z',
          downloads: 150,
          rating: 4.8,
          is_featured: true,
          is_premium: false
        },
        {
          id: 2,
          title: 'Market Research Guide',
          description: 'Step-by-step guide for conducting market research',
          type: 'guide',
          category: 'research',
          url: 'https://example.com/market-research-guide',
          tags: ['market', 'research', 'analysis'],
          author: 'Expert',
          created_at: '2024-01-10T14:30:00Z',
          updated_at: '2024-01-10T14:30:00Z',
          downloads: 89,
          rating: 4.5,
          is_featured: false,
          is_premium: true
        }
      ];
    }
  },

  async getResource(id: number): Promise<Resource> {
    try {
      return apiRequest<Resource>(`/resources/${id}/`);
    } catch (error) {
      throw new ApiError('Resource not found', 404);
    }
  },

  async createResource(resourceData: Omit<Resource, 'id' | 'created_at' | 'updated_at' | 'downloads' | 'rating'>): Promise<Resource> {
    return apiRequest<Resource>('/resources/', 'POST', resourceData);
  },

  async updateResource(id: number, resourceData: Partial<Resource>): Promise<Resource> {
    return apiRequest<Resource>(`/resources/${id}/`, 'PATCH', resourceData);
  },

  async deleteResource(id: number): Promise<void> {
    return apiRequest<void>(`/resources/${id}/`, 'DELETE');
  },

  async downloadResource(id: number): Promise<void> {
    try {
      const response = await fetch(`${API_URL}/resources/${id}/download/`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new ApiError('Download failed', response.status);
      }

      // Trigger download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `resource-${id}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.warn('Download not available:', error);
    }
  }
};

// Search API
export const searchAPI = {
  async universalSearch(
    query: string,
    filters: SearchFilters = {},
    limit: number = 20
  ): Promise<SearchResponse> {
    try {
      const params = new URLSearchParams({
        q: query,
        limit: limit.toString(),
        ...Object.entries(filters).reduce((acc, [key, value]) => {
          if (value !== undefined && value !== null) {
            acc[key] = Array.isArray(value) ? value.join(',') : String(value);
          }
          return acc;
        }, {} as Record<string, string>)
      });

      return apiRequest<SearchResponse>(`/search/?${params.toString()}`);
    } catch (error) {
      // Return mock data if API is not available
      console.warn('Search API not available, returning mock data');
      return {
        results: [
          {
            id: '1',
            title: 'Sample Business Idea',
            description: 'A sample business idea for testing',
            type: 'business_idea',
            score: 0.95
          },
          {
            id: '2',
            title: 'Tech Startup Template',
            description: 'Template for technology startups',
            type: 'template',
            score: 0.87
          }
        ],
        total: 2,
        page: 1,
        pageSize: limit,
        hasNext: false,
        hasPrevious: false
      };
    }
  },

  async searchBusinessIdeas(query: string, filters: SearchFilters = {}): Promise<SearchResult[]> {
    const response = await this.universalSearch(query, { ...filters, type: ['business_idea'] });
    return response.results;
  },

  async searchTemplates(query: string, filters: SearchFilters = {}): Promise<SearchResult[]> {
    const response = await this.universalSearch(query, { ...filters, type: ['template'] });
    return response.results;
  },

  async searchPosts(query: string, filters: SearchFilters = {}): Promise<SearchResult[]> {
    const response = await this.universalSearch(query, { ...filters, type: ['post'] });
    return response.results;
  }
};



// Generic API functions
export const api = {
  get: <T>(endpoint: string) => apiRequest<T>(endpoint, 'GET'),
  post: <T>(endpoint: string, data?: any) => apiRequest<T>(endpoint, 'POST', data),
  put: <T>(endpoint: string, data?: any) => apiRequest<T>(endpoint, 'PUT', data),
  patch: <T>(endpoint: string, data?: any) => apiRequest<T>(endpoint, 'PATCH', data),
  delete: <T>(endpoint: string) => apiRequest<T>(endpoint, 'DELETE'),
};

export default api;