/**
 * ⚠️ DEPRECATED - DO NOT USE ⚠️
 * This file contains old role checking logic that conflicts with the unified role manager.
 * Use '../utils/unifiedRoleManager' instead.
 *
 * @deprecated Use unifiedRoleManager.ts instead
 */

console.warn('⚠️ DEPRECATED: roleBasedRouting.ts is deprecated. Use unifiedRoleManager.ts instead.');

// Re-export functions from unified role manager to maintain compatibility
// while migrating existing code
export {
  getUserRole as getUserType,
  getUserRoles,
  hasRole,
  hasAnyRole,
  canAccessRoute,
  isSuperAdmin,
  isAdmin,
  getDashboardRoute,
  UserRole,
  PermissionLevel
} from './unifiedRoleManager';

export type DashboardType = 'super_admin' | 'admin' | 'mentor' | 'investor' | 'moderator' | 'user';
