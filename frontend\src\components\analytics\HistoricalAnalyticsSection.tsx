import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  History,
  ChevronDown,
  ChevronUp,
  TrendingUp,
  Target,
  Award,
  Percent
} from 'lucide-react';
import { AnalyticsSnapshot } from '../../services/analyticsApi';
import { formatPercentage } from '../../utils/exportUtils';
import {
  LineChart, Line, XAxis, YAxis, CartesianGrid,
  Tooltip, Legend, ResponsiveContainer
} from 'recharts';

interface HistoricalAnalyticsSectionProps {
  historicalSnapshots: AnalyticsSnapshot[];
  expanded: boolean;
  onToggle: () => void;
}

const HistoricalAnalyticsSection: React.FC<HistoricalAnalyticsSectionProps> = ({
  historicalSnapshots,
  expanded,
  onToggle
}) => {
  const { t } = useTranslation();

  if (!historicalSnapshots || historicalSnapshots.length === 0) {
    return null;
  }

  // Prepare historical data for chart
  const historicalData = historicalSnapshots.map(snapshot => ({
    date: new Date(snapshot.snapshot_date).toLocaleDateString(),
    progressRate: snapshot.progress_rate,
    milestoneCompletion: snapshot.milestone_completion_rate,
    goalAchievement: snapshot.goal_achievement_rate,
    successProbability: snapshot.success_probability
  })).reverse();

  // Calculate trends
  const calculateTrend = (metric: string) => {
    if (historicalSnapshots.length < 2) return { trend: 'neutral', change: 0 };

    const latest = historicalSnapshots[0];
    const previous = historicalSnapshots[1];

    let currentValue = 0;
    let previousValue = 0;

    switch (metric) {
      case 'progress_rate':
        currentValue = latest.progress_rate;
        previousValue = previous.progress_rate;
        break;
      case 'milestone_completion_rate':
        currentValue = latest.milestone_completion_rate;
        previousValue = previous.milestone_completion_rate;
        break;
      case 'goal_achievement_rate':
        currentValue = latest.goal_achievement_rate;
        previousValue = previous.goal_achievement_rate;
        break;
      case 'success_probability':
        currentValue = latest.success_probability;
        previousValue = previous.success_probability;
        break;
      default:
        return { trend: 'neutral', change: 0 };
    }

    const change = currentValue - previousValue;
    const percentChange = previousValue !== 0 ? (change / previousValue) * 100 : 0;

    return {
      trend: change > 0 ? 'up' : change < 0 ? 'down' : 'neutral',
      change: percentChange
    };
  };

  const progressTrend = calculateTrend('progress_rate');
  const milestoneTrend = calculateTrend('milestone_completion_rate');
  const goalTrend = calculateTrend('goal_achievement_rate');
  const successTrend = calculateTrend('success_probability');

  return (
    <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
      <div
        className="flex items-center justify-between cursor-pointer"
        onClick={onToggle}
      >
        <h2 className="text-lg font-bold flex items-center">
          <History size={18} className="text-purple-400 mr-2" />
          {t('incubator.analytics.historical.title')}
        </h2>

        {expanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
      </div>

      {expanded && (
        <div className="mt-4 space-y-6">
          {/* Trend Summary */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Progress Rate Trend */}
            <div className="bg-indigo-950/50 rounded-lg p-4 border border-indigo-800/50">
              <div className="flex justify-between items-center mb-2">
                <div className="flex items-center">
                  <TrendingUp size={16} className="text-purple-400 mr-2" />
                  <h3 className="text-sm font-medium">{t('incubator.analytics.historical.progressRate')}</h3>
                </div>
                <div className={`flex items-center ${
                  progressTrend.trend === 'up' ? 'text-green-400' :
                  progressTrend.trend === 'down' ? 'text-red-400' :
                  'text-gray-400'
                }`}>
                  {progressTrend.trend === 'up' ? (
                    <TrendingUp size={14} className="mr-1" />
                  ) : progressTrend.trend === 'down' ? (
                    <TrendingUp size={14} className="mr-1 transform rotate-180" />
                  ) : null}
                  <span className="text-xs">
                    {progressTrend.trend !== 'neutral' ? `${Math.abs(progressTrend.change).toFixed(1)}%` : 'No change'}
                  </span>
                </div>
              </div>
              <div className="flex items-end">
                <span className="text-2xl font-bold">{historicalSnapshots[0].progress_rate.toFixed(1)}</span>
                <span className="text-sm text-gray-400 ml-1 mb-1">updates/month</span>
              </div>
            </div>

            {/* Milestone Completion Trend */}
            <div className="bg-indigo-950/50 rounded-lg p-4 border border-indigo-800/50">
              <div className="flex justify-between items-center mb-2">
                <div className="flex items-center">
                  <Target size={16} className="text-pink-400 mr-2" />
                  <h3 className="text-sm font-medium">{t('incubator.analytics.historical.milestoneCompletion')}</h3>
                </div>
                <div className={`flex items-center ${
                  milestoneTrend.trend === 'up' ? 'text-green-400' :
                  milestoneTrend.trend === 'down' ? 'text-red-400' :
                  'text-gray-400'
                }`}>
                  {milestoneTrend.trend === 'up' ? (
                    <TrendingUp size={14} className="mr-1" />
                  ) : milestoneTrend.trend === 'down' ? (
                    <TrendingUp size={14} className="mr-1 transform rotate-180" />
                  ) : null}
                  <span className="text-xs">
                    {milestoneTrend.trend !== 'neutral' ? `${Math.abs(milestoneTrend.change).toFixed(1)}%` : 'No change'}
                  </span>
                </div>
              </div>
              <div className="flex items-end">
                <span className="text-2xl font-bold">{formatPercentage(historicalSnapshots[0].milestone_completion_rate, 0)}</span>
              </div>
            </div>

            {/* Goal Achievement Trend */}
            <div className="bg-indigo-950/50 rounded-lg p-4 border border-indigo-800/50">
              <div className="flex justify-between items-center mb-2">
                <div className="flex items-center">
                  <Award size={16} className="text-blue-400 mr-2" />
                  <h3 className="text-sm font-medium">{t('incubator.analytics.historical.goalAchievement')}</h3>
                </div>
                <div className={`flex items-center ${
                  goalTrend.trend === 'up' ? 'text-green-400' :
                  goalTrend.trend === 'down' ? 'text-red-400' :
                  'text-gray-400'
                }`}>
                  {goalTrend.trend === 'up' ? (
                    <TrendingUp size={14} className="mr-1" />
                  ) : goalTrend.trend === 'down' ? (
                    <TrendingUp size={14} className="mr-1 transform rotate-180" />
                  ) : null}
                  <span className="text-xs">
                    {goalTrend.trend !== 'neutral' ? `${Math.abs(goalTrend.change).toFixed(1)}%` : 'No change'}
                  </span>
                </div>
              </div>
              <div className="flex items-end">
                <span className="text-2xl font-bold">{formatPercentage(historicalSnapshots[0].goal_achievement_rate, 0)}</span>
              </div>
            </div>

            {/* Success Probability Trend */}
            <div className="bg-indigo-950/50 rounded-lg p-4 border border-indigo-800/50">
              <div className="flex justify-between items-center mb-2">
                <div className="flex items-center">
                  <Percent size={16} className="text-green-400 mr-2" />
                  <h3 className="text-sm font-medium">{t('incubator.analytics.predictive.successProbability')}</h3>
                </div>
                <div className={`flex items-center ${
                  successTrend.trend === 'up' ? 'text-green-400' :
                  successTrend.trend === 'down' ? 'text-red-400' :
                  'text-gray-400'
                }`}>
                  {successTrend.trend === 'up' ? (
                    <TrendingUp size={14} className="mr-1" />
                  ) : successTrend.trend === 'down' ? (
                    <TrendingUp size={14} className="mr-1 transform rotate-180" />
                  ) : null}
                  <span className="text-xs">
                    {successTrend.trend !== 'neutral' ? `${Math.abs(successTrend.change).toFixed(1)}%` : 'No change'}
                  </span>
                </div>
              </div>
              <div className="flex items-end">
                <span className="text-2xl font-bold">{formatPercentage(historicalSnapshots[0].success_probability, 0)}</span>
              </div>
            </div>
          </div>

          {/* Historical Chart */}
          <div className="bg-indigo-950/50 rounded-lg p-4 border border-indigo-800/50">
            <h3 className="text-md font-medium mb-3">{t('incubator.analytics.historical.historicalTrends')}</h3>

            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={historicalData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#334155" />
                  <XAxis
                    dataKey="date"
                    stroke="#94a3b8"
                    tick={{ fill: '#94a3b8' }}
                  />
                  <YAxis
                    stroke="#94a3b8"
                    tick={{ fill: '#94a3b8' }}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#1e293b',
                      borderColor: '#475569',
                      color: '#f8fafc'
                    }}
                    labelStyle={{ color: '#f8fafc' }}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="progressRate"
                    name={t('incubator.analytics.historical.progressRate')}
                    stroke="#8b5cf6"
                    activeDot={{ r: 8 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="milestoneCompletion"
                    name={t('incubator.analytics.historical.milestoneCompletion')}
                    stroke="#ec4899"
                  />
                  <Line
                    type="monotone"
                    dataKey="goalAchievement"
                    name={t('incubator.analytics.historical.goalAchievement')}
                    stroke="#3b82f6"
                  />
                  <Line
                    type="monotone"
                    dataKey="successProbability"
                    name={t('incubator.analytics.predictive.successProbability')}
                    stroke="#10b981"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>

            <div className="mt-2 text-sm text-gray-400">
              <div>
                This chart shows how your key metrics have changed over time. Use this to identify trends and patterns in your business performance.
              </div>
            </div>
          </div>

          {/* Historical Data Table */}
          <div className="bg-indigo-950/50 rounded-lg p-4 border border-indigo-800/50">
            <h3 className="text-md font-medium mb-3">{t('incubator.analytics.historical.historicalTrends')}</h3>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-indigo-800/50">
                <thead>
                  <tr>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">{t('incubator.analytics.historical.date')}</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">{t('incubator.analytics.historical.progressRate')}</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">{t('incubator.analytics.historical.milestoneCompletion')}</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">{t('incubator.analytics.historical.goalAchievement')}</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">{t('incubator.analytics.predictive.successProbability')}</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-indigo-800/30">
                  {historicalSnapshots.map((snapshot) => (
                    <tr key={snapshot.id}>
                      <td className="px-4 py-2 whitespace-nowrap text-sm">
                        {new Date(snapshot.snapshot_date).toLocaleDateString()}
                      </td>
                      <td className="px-4 py-2 whitespace-nowrap text-sm">{snapshot.progress_rate.toFixed(1)}</td>
                      <td className="px-4 py-2 whitespace-nowrap text-sm">{formatPercentage(snapshot.milestone_completion_rate)}</td>
                      <td className="px-4 py-2 whitespace-nowrap text-sm">{formatPercentage(snapshot.goal_achievement_rate)}</td>
                      <td className="px-4 py-2 whitespace-nowrap text-sm">{formatPercentage(snapshot.success_probability)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HistoricalAnalyticsSection;
