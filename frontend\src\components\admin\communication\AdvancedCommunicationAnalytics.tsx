import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Mail, MessageSquare, Bell, TrendingUp, TrendingDown,
  Users, Eye, MousePointer, XCircle, CheckCircle,
  BarChart3, PieChart, Calendar, Globe, Smartphone,
  Monitor, RefreshCw, Download, Filter, ArrowUp, ArrowDown
} from 'lucide-react';
// DashboardLayout removed - handled by routing system with AuthenticatedLayout
import { superAdminApi } from '../../../services/superAdminApi';

interface CommunicationMetrics {
  email_metrics: {
    total_sent: number;
    delivered: number;
    opened: number;
    clicked: number;
    bounced: number;
    unsubscribed: number;
    delivery_rate: number;
    open_rate: number;
    click_rate: number;
    bounce_rate: number;
    unsubscribe_rate: number;
  };
  push_metrics: {
    total_sent: number;
    delivered: number;
    opened: number;
    clicked: number;
    delivery_rate: number;
    open_rate: number;
    click_rate: number;
  };
  sms_metrics: {
    total_sent: number;
    delivered: number;
    clicked: number;
    delivery_rate: number;
    click_rate: number;
  };
}

interface CampaignPerformance {
  campaign_id: string;
  campaign_name: string;
  type: 'email' | 'push' | 'sms';
  sent_date: string;
  recipients: number;
  delivered: number;
  opened: number;
  clicked: number;
  revenue_generated: number;
  roi: number;
}

interface AudienceSegment {
  segment_id: string;
  segment_name: string;
  size: number;
  engagement_rate: number;
  conversion_rate: number;
  avg_order_value: number;
  lifetime_value: number;
}

interface DeviceBreakdown {
  device_type: 'desktop' | 'mobile' | 'tablet';
  opens: number;
  clicks: number;
  open_rate: number;
  click_rate: number;
}

const AdvancedCommunicationAnalytics: React.FC = () => {
  const { t } = useTranslation();
  const [metrics, setMetrics] = useState<CommunicationMetrics | null>(null);
  const [campaigns, setCampaigns] = useState<CampaignPerformance[]>([]);
  const [segments, setSegments] = useState<AudienceSegment[]>([]);
  const [devices, setDevices] = useState<DeviceBreakdown[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'campaigns' | 'segments' | 'devices'>('overview');
  const [dateRange, setDateRange] = useState('30d');

  useEffect(() => {
    fetchAnalyticsData();
  }, [dateRange]);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);

      // Mock data - replace with real API calls
      setMetrics({
        email_metrics: {
          total_sent: 125847,
          delivered: 123456,
          opened: 30864,
          clicked: 4938,
          bounced: 2391,
          unsubscribed: 156,
          delivery_rate: 98.1,
          open_rate: 25.0,
          click_rate: 4.0,
          bounce_rate: 1.9,
          unsubscribe_rate: 0.1
        },
        push_metrics: {
          total_sent: 89234,
          delivered: 87456,
          opened: 26237,
          clicked: 3498,
          delivery_rate: 98.0,
          open_rate: 30.0,
          click_rate: 4.0
        },
        sms_metrics: {
          total_sent: 12456,
          delivered: 12234,
          clicked: 1223,
          delivery_rate: 98.2,
          click_rate: 10.0
        }
      });

      setCampaigns([
        {
          campaign_id: '1',
          campaign_name: 'Weekly Newsletter #47',
          type: 'email',
          sent_date: new Date(Date.now() - 86400000 * 7).toISOString(),
          recipients: 12847,
          delivered: 12623,
          opened: 3206,
          clicked: 513,
          revenue_generated: 15420.50,
          roi: 312.5
        },
        {
          campaign_id: '2',
          campaign_name: 'Flash Sale Alert',
          type: 'push',
          sent_date: new Date(Date.now() - 86400000 * 3).toISOString(),
          recipients: 8934,
          delivered: 8756,
          opened: 2627,
          clicked: 394,
          revenue_generated: 28750.00,
          roi: 575.0
        },
        {
          campaign_id: '3',
          campaign_name: 'Premium Upgrade Reminder',
          type: 'email',
          sent_date: new Date(Date.now() - 86400000 * 14).toISOString(),
          recipients: 5678,
          delivered: 5567,
          opened: 1113,
          clicked: 167,
          revenue_generated: 8340.00,
          roi: 278.0
        }
      ]);

      setSegments([
        {
          segment_id: '1',
          segment_name: 'Power Users',
          size: 1247,
          engagement_rate: 45.2,
          conversion_rate: 12.8,
          avg_order_value: 156.80,
          lifetime_value: 2847.50
        },
        {
          segment_id: '2',
          segment_name: 'New Subscribers',
          size: 3456,
          engagement_rate: 28.7,
          conversion_rate: 6.4,
          avg_order_value: 89.20,
          lifetime_value: 567.30
        },
        {
          segment_id: '3',
          segment_name: 'Enterprise Users',
          size: 892,
          engagement_rate: 52.1,
          conversion_rate: 18.9,
          avg_order_value: 445.60,
          lifetime_value: 8923.40
        }
      ]);

      setDevices([
        {
          device_type: 'mobile',
          opens: 18456,
          clicks: 2768,
          open_rate: 32.5,
          click_rate: 15.0
        },
        {
          device_type: 'desktop',
          opens: 10234,
          clicks: 1535,
          open_rate: 28.9,
          click_rate: 15.0
        },
        {
          device_type: 'tablet',
          opens: 2174,
          clicks: 326,
          open_rate: 24.1,
          click_rate: 15.0
        }
      ]);

    } catch (error) {
      console.error('Error fetching analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const getGrowthIcon = (rate: number) => {
    return rate > 0 ? 
      <ArrowUp className="w-4 h-4 text-green-400" /> : 
      <ArrowDown className="w-4 h-4 text-red-400" />;
  };

  const getGrowthColor = (rate: number) => {
    return rate > 0 ? 'text-green-400' : 'text-red-400';
  };

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'desktop': return <Monitor className="w-5 h-5 text-blue-400" />;
      case 'mobile': return <Smartphone className="w-5 h-5 text-green-400" />;
      case 'tablet': return <Monitor className="w-5 h-5 text-purple-400" />;
      default: return <Monitor className="w-5 h-5 text-gray-400" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">
              {t('superAdmin.communicationAnalytics.title', 'Communication Analytics')}
            </h1>
            <p className="text-gray-300 mt-2">
              {t('superAdmin.communicationAnalytics.subtitle', 'Advanced analytics for email, push, and SMS campaigns')}
            </p>
          </div>
          
          <div className="flex items-center gap-4">
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-white"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="1y">Last year</option>
            </select>
            
            <button
              onClick={fetchAnalyticsData}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
            >
              <RefreshCw className="w-4 h-4" />
              Refresh
            </button>
            
            <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
              <Download className="w-4 h-4" />
              Export
            </button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 bg-gray-800/50 p-1 rounded-lg">
          {[
            { id: 'overview', name: 'Overview', icon: BarChart3 },
            { id: 'campaigns', name: 'Campaign Performance', icon: TrendingUp },
            { id: 'segments', name: 'Audience Segments', icon: Users },
            { id: 'devices', name: 'Device Analytics', icon: Smartphone }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
                activeTab === tab.id
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-300 hover:text-white hover:bg-gray-700'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              {tab.name}
            </button>
          ))}
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && metrics && (
          <div className="space-y-6">
            {/* Email Metrics */}
            <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <Mail className="w-5 h-5 text-blue-400" />
                Email Campaign Metrics
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">{metrics.email_metrics.total_sent.toLocaleString()}</div>
                  <div className="text-sm text-gray-400">Total Sent</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">{formatPercentage(metrics.email_metrics.delivery_rate)}</div>
                  <div className="text-sm text-gray-400">Delivery Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">{formatPercentage(metrics.email_metrics.open_rate)}</div>
                  <div className="text-sm text-gray-400">Open Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">{formatPercentage(metrics.email_metrics.click_rate)}</div>
                  <div className="text-sm text-gray-400">Click Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-400">{formatPercentage(metrics.email_metrics.bounce_rate)}</div>
                  <div className="text-sm text-gray-400">Bounce Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-400">{formatPercentage(metrics.email_metrics.unsubscribe_rate)}</div>
                  <div className="text-sm text-gray-400">Unsubscribe Rate</div>
                </div>
              </div>
            </div>

            {/* Push Notification Metrics */}
            <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <Bell className="w-5 h-5 text-green-400" />
                Push Notification Metrics
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">{metrics.push_metrics.total_sent.toLocaleString()}</div>
                  <div className="text-sm text-gray-400">Total Sent</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">{formatPercentage(metrics.push_metrics.delivery_rate)}</div>
                  <div className="text-sm text-gray-400">Delivery Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-400">{formatPercentage(metrics.push_metrics.open_rate)}</div>
                  <div className="text-sm text-gray-400">Open Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">{formatPercentage(metrics.push_metrics.click_rate)}</div>
                  <div className="text-sm text-gray-400">Click Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-400">{metrics.push_metrics.clicked.toLocaleString()}</div>
                  <div className="text-sm text-gray-400">Total Clicks</div>
                </div>
              </div>
            </div>

            {/* SMS Metrics */}
            <div className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <MessageSquare className="w-5 h-5 text-purple-400" />
                SMS Campaign Metrics
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">{metrics.sms_metrics.total_sent.toLocaleString()}</div>
                  <div className="text-sm text-gray-400">Total Sent</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">{formatPercentage(metrics.sms_metrics.delivery_rate)}</div>
                  <div className="text-sm text-gray-400">Delivery Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">{formatPercentage(metrics.sms_metrics.click_rate)}</div>
                  <div className="text-sm text-gray-400">Click Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-400">{metrics.sms_metrics.clicked.toLocaleString()}</div>
                  <div className="text-sm text-gray-400">Total Clicks</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Campaign Performance Tab */}
        {activeTab === 'campaigns' && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-white">Top Performing Campaigns</h2>
            
            <div className="space-y-4">
              {campaigns.map((campaign) => (
                <div key={campaign.campaign_id} className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      {campaign.type === 'email' ? <Mail className="w-5 h-5 text-blue-400" /> :
                       campaign.type === 'push' ? <Bell className="w-5 h-5 text-green-400" /> :
                       <MessageSquare className="w-5 h-5 text-purple-400" />}
                      <div>
                        <h3 className="font-semibold text-white">{campaign.campaign_name}</h3>
                        <p className="text-sm text-gray-400">
                          {campaign.type.toUpperCase()} • {new Date(campaign.sent_date).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-green-400">{formatCurrency(campaign.revenue_generated)}</div>
                      <div className="text-sm text-gray-400">Revenue Generated</div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
                    <div className="text-center">
                      <div className="text-lg font-bold text-white">{campaign.recipients.toLocaleString()}</div>
                      <div className="text-sm text-gray-400">Recipients</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-green-400">{campaign.delivered.toLocaleString()}</div>
                      <div className="text-sm text-gray-400">Delivered</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-blue-400">{campaign.opened.toLocaleString()}</div>
                      <div className="text-sm text-gray-400">Opened</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-purple-400">{campaign.clicked.toLocaleString()}</div>
                      <div className="text-sm text-gray-400">Clicked</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-yellow-400">{formatPercentage(campaign.roi)}</div>
                      <div className="text-sm text-gray-400">ROI</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-orange-400">
                        {formatPercentage((campaign.clicked / campaign.delivered) * 100)}
                      </div>
                      <div className="text-sm text-gray-400">CTR</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Audience Segments Tab */}
        {activeTab === 'segments' && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-white">Audience Segment Performance</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {segments.map((segment) => (
                <div key={segment.segment_id} className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                  <div className="flex items-center gap-3 mb-4">
                    <Users className="w-6 h-6 text-blue-400" />
                    <div>
                      <h3 className="font-semibold text-white">{segment.segment_name}</h3>
                      <p className="text-sm text-gray-400">{segment.size.toLocaleString()} users</p>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Engagement Rate:</span>
                      <span className="text-green-400 font-bold">{formatPercentage(segment.engagement_rate)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Conversion Rate:</span>
                      <span className="text-blue-400 font-bold">{formatPercentage(segment.conversion_rate)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Avg Order Value:</span>
                      <span className="text-purple-400 font-bold">{formatCurrency(segment.avg_order_value)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Lifetime Value:</span>
                      <span className="text-yellow-400 font-bold">{formatCurrency(segment.lifetime_value)}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Device Analytics Tab */}
        {activeTab === 'devices' && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-white">Device Performance</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {devices.map((device) => (
                <div key={device.device_type} className="bg-gray-800/50 rounded-lg p-6 border border-gray-700">
                  <div className="flex items-center gap-3 mb-4">
                    {getDeviceIcon(device.device_type)}
                    <h3 className="font-semibold text-white capitalize">{device.device_type}</h3>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Opens:</span>
                      <span className="text-white font-bold">{device.opens.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Clicks:</span>
                      <span className="text-white font-bold">{device.clicks.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Open Rate:</span>
                      <span className="text-blue-400 font-bold">{formatPercentage(device.open_rate)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Click Rate:</span>
                      <span className="text-green-400 font-bold">{formatPercentage(device.click_rate)}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdvancedCommunicationAnalytics;
