// UI Consistency and Styling Testing Script
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:8000';
const FRONTEND_URL = 'http://localhost:3000';

const TEST_USERS = {
  user: { username: 'testuser', password: 'testpass123', expectedRole: 'user' },
  mentor: { username: 'testmentor', password: 'testpass123', expectedRole: 'mentor' },
  investor: { username: 'testinvestor', password: 'testpass123', expectedRole: 'investor' },
  moderator: { username: 'testmoderator', password: 'testpass123', expectedRole: 'moderator' },
  admin: { username: 'testadmin', password: 'testpass123', expectedRole: 'admin' },
  superadmin: { username: 'testsuperadmin', password: 'testpass123', expectedRole: 'super_admin' }
};

let testResults = {
  passed: 0,
  failed: 0,
  details: []
};

// Expected brand colors based on the codebase analysis
const BRAND_COLORS = {
  primary: {
    purple: '#8B5CF6', // purple-500
    blue: '#3B82F6',   // blue-500
    gradient: 'linear-gradient(135deg, #8B5CF6 0%, #3B82F6 100%)'
  },
  glass: {
    bg: 'rgba(255, 255, 255, 0.05)',
    border: 'rgba(255, 255, 255, 0.2)',
    hover: 'rgba(255, 255, 255, 0.1)',
    primary: '#ffffff',
    secondary: 'rgba(255, 255, 255, 0.7)'
  },
  background: {
    main: 'from-slate-900 to-indigo-950',
    login: 'from-gray-900 via-purple-900 to-violet-900',
    sidebar: 'rgba(0, 0, 0, 0.3)'
  }
};

// Expected UI elements and their styling
const UI_ELEMENTS = {
  logo: {
    gradient: 'from-purple-400 to-blue-400',
    sidebarGradient: 'from-purple-500 to-blue-500',
    icon: 'Sparkles'
  },
  buttons: {
    primary: 'glass-morphism',
    hover: 'bg-glass-hover',
    active: 'bg-glass-active'
  },
  cards: {
    primary: 'glass-morphism',
    light: 'glass-light'
  }
};

async function loginUser(userType, userData) {
  try {
    const response = await fetch(`${BASE_URL}/api/auth/token/`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        username: userData.username,
        password: userData.password
      })
    });

    if (!response.ok) {
      throw new Error(`Login failed with status ${response.status}`);
    }

    const data = await response.json();
    return { token: data.access, user: data.user };
  } catch (error) {
    console.error(`❌ Login failed for ${userType}: ${error.message}`);
    return null;
  }
}

async function testPageStyling(route, token, userType, expectedElements) {
  try {
    const response = await fetch(`${FRONTEND_URL}${route}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
      }
    });

    if (!response.ok) {
      throw new Error(`Route ${route} returned status ${response.status}`);
    }

    const html = await response.text();
    
    // Check for expected styling classes and elements
    const stylingChecks = {
      hasGlassMorphism: html.includes('glass-morphism') || html.includes('glass-light'),
      hasUniversalSidebar: html.includes('universal-sidebar') || html.includes('sidebar'),
      hasProperGradients: html.includes('gradient') || html.includes('bg-gradient'),
      hasConsistentColors: html.includes('purple') && html.includes('blue'),
      hasResponsiveClasses: html.includes('sm:') || html.includes('md:') || html.includes('lg:'),
      hasRTLSupport: html.includes('rtl') || html.includes('dir='),
      hasAccessibility: html.includes('role=') || html.includes('aria-'),
      hasProperFonts: html.includes('font-') || html.includes('text-')
    };

    return {
      route,
      userType,
      accessible: true,
      stylingChecks,
      score: Object.values(stylingChecks).filter(Boolean).length / Object.keys(stylingChecks).length * 100
    };

  } catch (error) {
    return {
      route,
      userType,
      accessible: false,
      error: error.message,
      score: 0
    };
  }
}

async function testColorConsistency() {
  console.log('\n🎨 Testing Color Consistency...');
  
  const results = { passed: 0, failed: 0, details: [] };
  
  // Test login page colors
  try {
    const loginResponse = await fetch(`${FRONTEND_URL}/login`);
    const loginHtml = await loginResponse.text();
    
    const loginColorChecks = {
      hasLoginGradient: loginHtml.includes('from-gray-900') && loginHtml.includes('via-purple-900') && loginHtml.includes('to-violet-900'),
      hasPurpleBlueGradient: loginHtml.includes('from-purple-400') && loginHtml.includes('to-blue-400'),
      hasGlassMorphism: loginHtml.includes('backdrop-blur') || loginHtml.includes('bg-black/30'),
      hasConsistentBranding: loginHtml.includes('purple') && loginHtml.includes('blue')
    };

    const loginScore = Object.values(loginColorChecks).filter(Boolean).length / Object.keys(loginColorChecks).length * 100;
    
    if (loginScore >= 75) {
      console.log(`✅ Login page color consistency: ${loginScore.toFixed(1)}%`);
      results.passed++;
    } else {
      console.log(`⚠️  Login page color consistency: ${loginScore.toFixed(1)}%`);
      results.failed++;
    }

    results.details.push({
      page: 'Login',
      score: loginScore,
      checks: loginColorChecks,
      status: loginScore >= 75 ? 'PASSED' : 'FAILED'
    });

  } catch (error) {
    console.error(`❌ Error testing login page colors: ${error.message}`);
    results.failed++;
  }

  return results;
}

async function testLogoConsistency() {
  console.log('\n🏷️  Testing Logo Consistency...');
  
  const results = { passed: 0, failed: 0, details: [] };
  
  const pagesToTest = [
    { route: '/login', name: 'Login Page' },
    { route: '/', name: 'Home Page' }
  ];

  for (const page of pagesToTest) {
    try {
      const response = await fetch(`${FRONTEND_URL}${page.route}`);
      const html = await response.text();
      
      const logoChecks = {
        hasSparklesIcon: html.includes('Sparkles') || html.includes('sparkles'),
        hasGradientLogo: html.includes('gradient') && (html.includes('purple') || html.includes('blue')),
        hasConsistentSizing: html.includes('w-') && html.includes('h-'),
        hasProperBranding: html.includes('app.name') || html.includes('Yasmeen') || html.includes('logo')
      };

      const logoScore = Object.values(logoChecks).filter(Boolean).length / Object.keys(logoChecks).length * 100;
      
      if (logoScore >= 75) {
        console.log(`✅ ${page.name} logo consistency: ${logoScore.toFixed(1)}%`);
        results.passed++;
      } else {
        console.log(`⚠️  ${page.name} logo consistency: ${logoScore.toFixed(1)}%`);
        results.failed++;
      }

      results.details.push({
        page: page.name,
        route: page.route,
        score: logoScore,
        checks: logoChecks,
        status: logoScore >= 75 ? 'PASSED' : 'FAILED'
      });

    } catch (error) {
      console.error(`❌ Error testing ${page.name} logo: ${error.message}`);
      results.failed++;
    }
  }

  return results;
}

async function testResponsiveDesign() {
  console.log('\n📱 Testing Responsive Design...');
  
  const results = { passed: 0, failed: 0, details: [] };
  
  // Test key pages for responsive classes
  const pagesToTest = ['/login', '/dashboard'];
  
  for (const route of pagesToTest) {
    try {
      const response = await fetch(`${FRONTEND_URL}${route}`);
      const html = await response.text();
      
      const responsiveChecks = {
        hasMobileClasses: html.includes('sm:') || html.includes('mobile'),
        hasTabletClasses: html.includes('md:') || html.includes('tablet'),
        hasDesktopClasses: html.includes('lg:') || html.includes('xl:'),
        hasFlexboxResponsive: html.includes('flex-col') && html.includes('flex-row'),
        hasGridResponsive: html.includes('grid-cols-1') && (html.includes('grid-cols-2') || html.includes('grid-cols-3')),
        hasResponsivePadding: html.includes('p-4') && (html.includes('sm:p-6') || html.includes('lg:p-8')),
        hasResponsiveText: html.includes('text-sm') && (html.includes('sm:text-base') || html.includes('lg:text-lg'))
      };

      const responsiveScore = Object.values(responsiveChecks).filter(Boolean).length / Object.keys(responsiveChecks).length * 100;
      
      if (responsiveScore >= 70) {
        console.log(`✅ ${route} responsive design: ${responsiveScore.toFixed(1)}%`);
        results.passed++;
      } else {
        console.log(`⚠️  ${route} responsive design: ${responsiveScore.toFixed(1)}%`);
        results.failed++;
      }

      results.details.push({
        route,
        score: responsiveScore,
        checks: responsiveChecks,
        status: responsiveScore >= 70 ? 'PASSED' : 'FAILED'
      });

    } catch (error) {
      console.error(`❌ Error testing responsive design for ${route}: ${error.message}`);
      results.failed++;
    }
  }

  return results;
}

async function testArabicLanguageSupport() {
  console.log('\n🌐 Testing Arabic Language Support...');
  
  const results = { passed: 0, failed: 0, details: [] };
  
  try {
    // Test login page for RTL support
    const loginResponse = await fetch(`${FRONTEND_URL}/login`);
    const loginHtml = await loginResponse.text();
    
    const rtlChecks = {
      hasRTLDirective: loginHtml.includes('dir="rtl"') || loginHtml.includes('rtl'),
      hasArabicFonts: loginHtml.includes('Tajawal') || loginHtml.includes('Noto Sans Arabic') || loginHtml.includes('Cairo'),
      hasRTLClasses: loginHtml.includes('flex-row-reverse') || loginHtml.includes('rtl'),
      hasLanguageSupport: loginHtml.includes('language') || loginHtml.includes('i18n'),
      hasRTLComponents: loginHtml.includes('RTLText') || loginHtml.includes('RTLIcon') || loginHtml.includes('RTLFlex')
    };

    const rtlScore = Object.values(rtlChecks).filter(Boolean).length / Object.keys(rtlChecks).length * 100;
    
    if (rtlScore >= 60) {
      console.log(`✅ Arabic/RTL support: ${rtlScore.toFixed(1)}%`);
      results.passed++;
    } else {
      console.log(`⚠️  Arabic/RTL support: ${rtlScore.toFixed(1)}%`);
      results.failed++;
    }

    results.details.push({
      feature: 'Arabic/RTL Support',
      score: rtlScore,
      checks: rtlChecks,
      status: rtlScore >= 60 ? 'PASSED' : 'FAILED'
    });

  } catch (error) {
    console.error(`❌ Error testing Arabic language support: ${error.message}`);
    results.failed++;
  }

  return results;
}

async function testUIConsistencyAcrossRoles(userType, userData) {
  console.log(`\n🎭 Testing UI consistency for ${userType}...`);
  
  try {
    const loginResult = await loginUser(userType, userData);
    if (!loginResult) {
      throw new Error('Login failed');
    }

    const { token } = loginResult;
    
    // Test key routes for this role
    const routesToTest = [
      '/dashboard',
      '/profile',
      '/settings'
    ];

    const routeResults = [];
    let totalScore = 0;

    for (const route of routesToTest) {
      const result = await testPageStyling(route, token, userType, UI_ELEMENTS);
      routeResults.push(result);
      totalScore += result.score;
    }

    const averageScore = totalScore / routesToTest.length;
    
    console.log(`   📊 Average UI consistency score: ${averageScore.toFixed(1)}%`);
    console.log(`   📋 Routes tested: ${routeResults.length}`);
    console.log(`   ✅ Accessible routes: ${routeResults.filter(r => r.accessible).length}`);

    testResults.passed++;
    testResults.details.push({
      userType,
      status: 'PASSED',
      averageScore,
      routeResults,
      message: 'UI consistency tests completed'
    });

  } catch (error) {
    console.error(`❌ UI consistency test failed for ${userType}: ${error.message}`);
    testResults.failed++;
    testResults.details.push({
      userType,
      status: 'FAILED',
      error: error.message
    });
  }
}

async function runUIConsistencyTests() {
  console.log('🎨 Starting UI Consistency and Styling Tests');
  console.log('=' * 60);
  
  // Test color consistency
  const colorResults = await testColorConsistency();
  testResults.passed += colorResults.passed;
  testResults.failed += colorResults.failed;
  
  // Test logo consistency
  const logoResults = await testLogoConsistency();
  testResults.passed += logoResults.passed;
  testResults.failed += logoResults.failed;
  
  // Test responsive design
  const responsiveResults = await testResponsiveDesign();
  testResults.passed += responsiveResults.passed;
  testResults.failed += responsiveResults.failed;
  
  // Test Arabic language support
  const arabicResults = await testArabicLanguageSupport();
  testResults.passed += arabicResults.passed;
  testResults.failed += arabicResults.failed;
  
  // Test UI consistency across all user roles
  for (const [userType, userData] of Object.entries(TEST_USERS)) {
    await testUIConsistencyAcrossRoles(userType, userData);
  }
  
  // Print comprehensive results
  console.log('\n🎨 UI Consistency Test Results');
  console.log('=' * 40);
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
  
  console.log('\n📋 Detailed UI Test Results:');
  
  // Color consistency results
  console.log('\n🎨 Color Consistency:');
  colorResults.details.forEach(detail => {
    const status = detail.status === 'PASSED' ? '✅' : '⚠️';
    console.log(`   ${status} ${detail.page}: ${detail.score.toFixed(1)}%`);
  });
  
  // Logo consistency results
  console.log('\n🏷️  Logo Consistency:');
  logoResults.details.forEach(detail => {
    const status = detail.status === 'PASSED' ? '✅' : '⚠️';
    console.log(`   ${status} ${detail.page}: ${detail.score.toFixed(1)}%`);
  });
  
  // Responsive design results
  console.log('\n📱 Responsive Design:');
  responsiveResults.details.forEach(detail => {
    const status = detail.status === 'PASSED' ? '✅' : '⚠️';
    console.log(`   ${status} ${detail.route}: ${detail.score.toFixed(1)}%`);
  });
  
  // Arabic support results
  console.log('\n🌐 Arabic Language Support:');
  arabicResults.details.forEach(detail => {
    const status = detail.status === 'PASSED' ? '✅' : '⚠️';
    console.log(`   ${status} ${detail.feature}: ${detail.score.toFixed(1)}%`);
  });
  
  // Role-based UI consistency
  console.log('\n🎭 Role-based UI Consistency:');
  testResults.details.forEach(detail => {
    if (detail.userType) {
      const status = detail.status === 'PASSED' ? '✅' : '❌';
      console.log(`   ${status} ${detail.userType.toUpperCase()}: ${detail.averageScore?.toFixed(1) || 'N/A'}%`);
    }
  });
  
  console.log('\n🎯 UI Consistency Assessment:');
  const overallSuccessRate = (testResults.passed / (testResults.passed + testResults.failed)) * 100;
  
  if (overallSuccessRate >= 90) {
    console.log('🎉 EXCELLENT! UI consistency is outstanding across all areas.');
    console.log('✅ Colors, logos, responsive design, and Arabic support are all working well.');
  } else if (overallSuccessRate >= 75) {
    console.log('✅ GOOD! UI consistency is solid with minor areas for improvement.');
    console.log('🔧 Review failed tests for enhancement opportunities.');
  } else {
    console.log('⚠️  NEEDS IMPROVEMENT! Several UI consistency issues detected.');
    console.log('🔧 Address failed tests to improve user experience.');
  }
  
  return testResults;
}

// Run the comprehensive UI tests
runUIConsistencyTests().catch(console.error);
