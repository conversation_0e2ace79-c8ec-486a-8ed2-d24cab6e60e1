#!/usr/bin/env python3
"""
Simple script to create test users
"""

import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from django.contrib.auth.models import User

def create_test_users():
    """Create test users with known passwords"""
    print("🔧 Creating test users...")
    
    # Test users to create
    users_to_create = [
        {'username': 'testuser', 'password': 'testpass123', 'is_staff': False, 'is_superuser': False},
        {'username': 'testmentor', 'password': 'testpass123', 'is_staff': False, 'is_superuser': False},
        {'username': 'testinvestor', 'password': 'testpass123', 'is_staff': False, 'is_superuser': False},
        {'username': 'testmoderator', 'password': 'testpass123', 'is_staff': False, 'is_superuser': False},
        {'username': 'testadmin', 'password': 'testpass123', 'is_staff': True, 'is_superuser': False},
        {'username': 'testsuperadmin', 'password': 'testpass123', 'is_staff': True, 'is_superuser': True},
    ]
    
    for user_data in users_to_create:
        username = user_data['username']
        try:
            if User.objects.filter(username=username).exists():
                print(f"👤 User {username} already exists, updating...")
                user = User.objects.get(username=username)
                user.set_password(user_data['password'])
                user.is_staff = user_data['is_staff']
                user.is_superuser = user_data['is_superuser']
                user.save()
                print(f"   ✅ Updated {username}")
            else:
                user = User.objects.create_user(
                    username=username,
                    password=user_data['password'],
                    is_staff=user_data['is_staff'],
                    is_superuser=user_data['is_superuser']
                )
                print(f"   ✅ Created {username}")
        except Exception as e:
            print(f"   ❌ Error with {username}: {e}")
    
    print("\n🎉 Test users setup completed!")
    
    # Test role determination
    print("\n🧪 Testing role determination...")
    from users.serializers import UserSerializer
    
    test_users = ['testuser', 'testmentor', 'testadmin']
    
    for username in test_users:
        try:
            user = User.objects.get(username=username)
            serializer = UserSerializer()
            determined_role = serializer.get_user_role(user)
            
            print(f"👤 {username}:")
            print(f"   is_staff: {user.is_staff}")
            print(f"   is_superuser: {user.is_superuser}")
            print(f"   Determined role: {determined_role}")
            
        except User.DoesNotExist:
            print(f"   ❌ User {username} does not exist")
        except Exception as e:
            print(f"   ❌ Error testing {username}: {e}")

if __name__ == '__main__':
    create_test_users()
