import React, { Suspense, lazy } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../hooks/useLanguage';
import LoadingFallback from '../components/ui/LoadingFallback';

// Lazy load all heavy components for better performance
const EnhancedHero = lazy(() => import('../components/EnhancedHero'));
const About = lazy(() => import('../components/About'));
const EnhancedAIFeatures = lazy(() => import('../components/EnhancedAIFeatures'));
const AICapabilitiesShowcase = lazy(() => import('../components/AICapabilitiesShowcase'));
const Features = lazy(() => import('../components/Features'));
const Events = lazy(() => import('../components/events/Events'));
const Community = lazy(() => import('../components/Community'));
const Contact = lazy(() => import('../components/Contact'));

const HomePage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // ✅ REMOVED: Redirect logic moved to SmartFallback for better route handling
  // ✅ REMOVED: Navbar and Footer - these are provided by PublicLayout

  return (
    <div className="min-h-screen">
      {/* Load hero section immediately for better perceived performance */}
      <Suspense fallback={<LoadingFallback />}>
        <EnhancedHero />
      </Suspense>

      {/* Lazy load other sections progressively */}
      <Suspense fallback={<LoadingFallback />}>
        <About />
      </Suspense>

      <Suspense fallback={<LoadingFallback />}>
        <EnhancedAIFeatures />
      </Suspense>

      <Suspense fallback={<LoadingFallback />}>
        <AICapabilitiesShowcase />
      </Suspense>

      <Suspense fallback={<LoadingFallback />}>
        <Features />
      </Suspense>

      <Suspense fallback={<LoadingFallback />}>
        <Events />
      </Suspense>

      <Suspense fallback={<LoadingFallback />}>
        <Community />
      </Suspense>

      <Suspense fallback={<LoadingFallback />}>
        <Contact />
      </Suspense>
    </div>
  );
};

export default HomePage;
