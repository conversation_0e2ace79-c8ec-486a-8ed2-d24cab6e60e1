/**
 * SIDEBAR DEBUGGER UTILITY
 * Browser console utility to debug role-based sidebar navigation issues
 * 
 * Usage in browser console:
 * 1. Open browser console (F12)
 * 2. Type: window.debugSidebar()
 * 3. Follow the diagnostic output
 */

import { getUserRole, hasAnyRole, canAccessRoute } from './unifiedRoleManager';
import { NAVIGATION_ITEMS, getNavigationItemsForRole } from '../config/navigationConfig';
import { User } from '../services/api';

interface SidebarDebugInfo {
  userInfo: {
    isAuthenticated: boolean;
    userId: number | null;
    username: string | null;
    userRole: string;
    rawUserObject: any;
  };
  navigationInfo: {
    totalItems: number;
    filteredItems: number;
    itemsByRole: Record<string, number>;
    userSpecificItems: string[];
    missingItems: string[];
  };
  systemInfo: {
    unifiedRoleManagerWorking: boolean;
    navigationConfigLoaded: boolean;
    sidebarComponentMounted: boolean;
  };
  recommendations: string[];
}

/**
 * Main debugging function - call this from browser console
 */
export function debugSidebar(): SidebarDebugInfo {
  console.log('🔍 SIDEBAR NAVIGATION DEBUGGER');
  console.log('=' .repeat(50));

  const debugInfo: SidebarDebugInfo = {
    userInfo: {
      isAuthenticated: false,
      userId: null,
      username: null,
      userRole: 'user',
      rawUserObject: null
    },
    navigationInfo: {
      totalItems: 0,
      filteredItems: 0,
      itemsByRole: {},
      userSpecificItems: [],
      missingItems: []
    },
    systemInfo: {
      unifiedRoleManagerWorking: false,
      navigationConfigLoaded: false,
      sidebarComponentMounted: false
    },
    recommendations: []
  };

  try {
    // 1. Check Redux store and user authentication
    console.log('\n📋 1. USER AUTHENTICATION CHECK');
    console.log('-' .repeat(30));

    const reduxStore = (window as any).__REDUX_STORE__ || (window as any).store;
    if (!reduxStore) {
      console.log('❌ Redux store not found');
      debugInfo.recommendations.push('Redux store not accessible - check if app is properly initialized');
    } else {
      const state = reduxStore.getState();
      const authState = state.auth;
      
      debugInfo.userInfo.isAuthenticated = authState?.isAuthenticated || false;
      debugInfo.userInfo.rawUserObject = authState?.user;
      
      if (authState?.user) {
        debugInfo.userInfo.userId = authState.user.id;
        debugInfo.userInfo.username = authState.user.username;
        debugInfo.userInfo.userRole = getUserRole(authState.user);
        
        console.log(`✅ User authenticated: ${debugInfo.userInfo.username}`);
        console.log(`✅ User ID: ${debugInfo.userInfo.userId}`);
        console.log(`✅ User role: ${debugInfo.userInfo.userRole}`);
        console.log(`📄 Raw user object:`, authState.user);
      } else {
        console.log('❌ User not authenticated or user object missing');
        debugInfo.recommendations.push('User not authenticated - check login status');
      }
    }

    // 2. Test unified role manager
    console.log('\n📋 2. UNIFIED ROLE MANAGER TEST');
    console.log('-' .repeat(30));

    try {
      const testUser = debugInfo.userInfo.rawUserObject;
      const detectedRole = getUserRole(testUser);
      
      console.log(`✅ getUserRole() working: ${detectedRole}`);
      console.log(`✅ hasAnyRole() working: ${hasAnyRole(testUser, ['user', 'admin'])}`);
      console.log(`✅ canAccessRoute() working: ${canAccessRoute(testUser, ['user'])}`);
      
      debugInfo.systemInfo.unifiedRoleManagerWorking = true;
    } catch (error) {
      console.log(`❌ Unified role manager error: ${error.message}`);
      debugInfo.recommendations.push('Unified role manager has errors - check imports');
    }

    // 3. Test navigation configuration
    console.log('\n📋 3. NAVIGATION CONFIGURATION TEST');
    console.log('-' .repeat(30));

    try {
      debugInfo.navigationInfo.totalItems = NAVIGATION_ITEMS.length;
      console.log(`✅ Navigation config loaded: ${debugInfo.navigationInfo.totalItems} total items`);
      
      // Test role-based filtering
      const userRole = debugInfo.userInfo.userRole;
      const filteredItems = getNavigationItemsForRole(userRole as any);
      debugInfo.navigationInfo.filteredItems = filteredItems.length;
      debugInfo.navigationInfo.userSpecificItems = filteredItems.map(item => item.name);
      
      console.log(`✅ Role-based filtering working: ${debugInfo.navigationInfo.filteredItems} items for ${userRole}`);
      console.log(`📋 Items for ${userRole}:`, debugInfo.navigationInfo.userSpecificItems);
      
      // Count items per role
      const roles = ['user', 'mentor', 'investor', 'moderator', 'admin', 'super_admin'];
      roles.forEach(role => {
        const items = getNavigationItemsForRole(role as any);
        debugInfo.navigationInfo.itemsByRole[role] = items.length;
      });
      
      console.log('📊 Items per role:', debugInfo.navigationInfo.itemsByRole);
      
      debugInfo.systemInfo.navigationConfigLoaded = true;
    } catch (error) {
      console.log(`❌ Navigation config error: ${error.message}`);
      debugInfo.recommendations.push('Navigation configuration has errors - check imports');
    }

    // 4. Check if sidebar component is mounted
    console.log('\n📋 4. SIDEBAR COMPONENT CHECK');
    console.log('-' .repeat(30));

    const sidebarElement = document.querySelector('[class*="sidebar"]') || 
                          document.querySelector('[data-testid*="sidebar"]') ||
                          document.querySelector('nav');
    
    if (sidebarElement) {
      console.log('✅ Sidebar component found in DOM');
      debugInfo.systemInfo.sidebarComponentMounted = true;
      
      // Count visible navigation items
      const navItems = sidebarElement.querySelectorAll('a, button');
      console.log(`📋 Visible navigation items in DOM: ${navItems.length}`);
      
      // Log visible item texts
      const visibleItems = Array.from(navItems).map(item => item.textContent?.trim()).filter(Boolean);
      console.log('📋 Visible navigation items:', visibleItems);
      
    } else {
      console.log('❌ Sidebar component not found in DOM');
      debugInfo.recommendations.push('Sidebar component not mounted - check if component is rendered');
    }

    // 5. Generate recommendations
    console.log('\n📋 5. DIAGNOSTIC RECOMMENDATIONS');
    console.log('-' .repeat(30));

    if (debugInfo.userInfo.isAuthenticated && debugInfo.systemInfo.unifiedRoleManagerWorking && debugInfo.systemInfo.navigationConfigLoaded) {
      if (debugInfo.navigationInfo.filteredItems === 0) {
        debugInfo.recommendations.push('No navigation items for current role - check role configuration');
      } else if (debugInfo.navigationInfo.filteredItems === debugInfo.navigationInfo.totalItems) {
        debugInfo.recommendations.push('All navigation items visible - role filtering may not be working');
      } else {
        console.log('✅ Role-based navigation appears to be working correctly');
      }
    }

    if (!debugInfo.userInfo.isAuthenticated) {
      debugInfo.recommendations.push('User not authenticated - login required');
    }

    if (debugInfo.userInfo.userRole === 'user' && debugInfo.navigationInfo.filteredItems > 15) {
      debugInfo.recommendations.push('Regular user seeing too many items - check role assignment');
    }

    // Display recommendations
    if (debugInfo.recommendations.length > 0) {
      console.log('\n🔧 RECOMMENDATIONS:');
      debugInfo.recommendations.forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec}`);
      });
    } else {
      console.log('✅ No issues detected - sidebar should be working correctly');
    }

  } catch (error) {
    console.error('❌ Debug function error:', error);
    debugInfo.recommendations.push(`Debug function error: ${error.message}`);
  }

  console.log('\n📊 COMPLETE DEBUG INFO:');
  console.log(debugInfo);
  
  return debugInfo;
}

/**
 * Quick role test function
 */
export function testUserRole(mockRole: string) {
  console.log(`🧪 Testing navigation for role: ${mockRole}`);
  
  const mockUser = {
    id: 999,
    username: `test_${mockRole}`,
    email: `<EMAIL>`,
    user_role: mockRole
  };
  
  const items = getNavigationItemsForRole(mockRole as any);
  console.log(`📋 Navigation items for ${mockRole}:`, items.map(item => item.name));
  console.log(`📊 Total items: ${items.length}`);
  
  return items;
}

/**
 * Compare navigation between roles
 */
export function compareRoleNavigation() {
  console.log('🔍 ROLE NAVIGATION COMPARISON');
  console.log('=' .repeat(40));

  const roles = ['user', 'mentor', 'investor', 'moderator', 'admin', 'super_admin'];

  roles.forEach(role => {
    const items = getNavigationItemsForRole(role as any);
    console.log(`\n🎯 ${role.toUpperCase()} (${items.length} items):`);
    items.forEach(item => {
      console.log(`   • ${item.name} (${item.path})`);
    });
  });
}

/**
 * Test authentication state and session
 */
export function testAuthState() {
  console.log('🔐 AUTHENTICATION STATE TEST');
  console.log('=' .repeat(40));

  const reduxStore = (window as any).__REDUX_STORE__ || (window as any).store;
  if (!reduxStore) {
    console.log('❌ Redux store not accessible');
    return;
  }

  const state = reduxStore.getState();
  const authState = state.auth;

  console.log('📊 Auth State:', {
    isAuthenticated: authState?.isAuthenticated,
    isLoading: authState?.isLoading,
    hasUser: !!authState?.user,
    hasToken: !!authState?.token,
    userId: authState?.user?.id,
    username: authState?.user?.username,
    userRole: authState?.user ? getUserRole(authState.user) : 'none'
  });

  // Test session storage
  const sessionData = {
    localStorage: {
      token: localStorage.getItem('token'),
      user: localStorage.getItem('user'),
      authState: localStorage.getItem('persist:auth')
    },
    sessionStorage: {
      token: sessionStorage.getItem('token'),
      user: sessionStorage.getItem('user')
    }
  };

  console.log('💾 Session Storage:', sessionData);

  return authState;
}

/**
 * Test API connectivity
 */
export function testAPIConnectivity() {
  console.log('🌐 API CONNECTIVITY TEST');
  console.log('=' .repeat(40));

  const baseURL = 'http://localhost:8000';
  const endpoints = [
    '/api/auth/user/',
    '/api/admin/stats/',
    '/api/analytics/overview/',
    '/api/admin/notifications/',
    '/api/admin/system-health/'
  ];

  endpoints.forEach(async (endpoint) => {
    try {
      const response = await fetch(baseURL + endpoint, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      console.log(`${endpoint}: ${response.status} ${response.statusText}`);
    } catch (error) {
      console.log(`${endpoint}: ❌ ${error.message}`);
    }
  });
}

/**
 * Performance test for navigation rendering
 */
export function testNavigationPerformance() {
  console.log('⚡ NAVIGATION PERFORMANCE TEST');
  console.log('=' .repeat(40));

  const roles = ['user', 'mentor', 'investor', 'moderator', 'admin', 'super_admin'];

  roles.forEach(role => {
    const start = performance.now();
    const items = getNavigationItemsForRole(role as any);
    const end = performance.now();

    console.log(`${role}: ${items.length} items in ${(end - start).toFixed(2)}ms`);
  });

  // Test multiple iterations
  console.log('\n🔄 Multiple iterations test:');
  const iterations = 100;
  const start = performance.now();

  for (let i = 0; i < iterations; i++) {
    getNavigationItemsForRole('admin');
  }

  const end = performance.now();
  const avgTime = (end - start) / iterations;

  console.log(`Average time over ${iterations} iterations: ${avgTime.toFixed(2)}ms`);
}

/**
 * Help function showing all available debug commands
 */
export function debugHelp() {
  console.log('🆘 SIDEBAR DEBUGGER HELP');
  console.log('=' .repeat(40));
  console.log('Available functions:');
  console.log('');
  console.log('🔍 Main Debugging:');
  console.log('  • debugSidebar() - Complete diagnostic report');
  console.log('  • testAuthState() - Check authentication state');
  console.log('  • testAPIConnectivity() - Test API endpoints');
  console.log('');
  console.log('🧪 Role Testing:');
  console.log('  • testUserRole("admin") - Test navigation for specific role');
  console.log('  • compareRoleNavigation() - Compare all roles side by side');
  console.log('');
  console.log('⚡ Performance:');
  console.log('  • testNavigationPerformance() - Measure navigation performance');
  console.log('');
  console.log('🆘 Help:');
  console.log('  • debugHelp() - Show this help message');
  console.log('');
  console.log('💡 Quick Start:');
  console.log('  1. Run debugSidebar() for complete analysis');
  console.log('  2. If issues found, run specific tests');
  console.log('  3. Use testUserRole() to test different roles');
}

// Make functions available globally for browser console
if (typeof window !== 'undefined') {
  (window as any).debugSidebar = debugSidebar;
  (window as any).testUserRole = testUserRole;
  (window as any).compareRoleNavigation = compareRoleNavigation;
  (window as any).testAuthState = testAuthState;
  (window as any).testAPIConnectivity = testAPIConnectivity;
  (window as any).testNavigationPerformance = testNavigationPerformance;
  (window as any).debugHelp = debugHelp;

  // Log availability message
  console.log('🔧 Sidebar Debugger Enhanced! Use debugHelp() for available functions.');
}
