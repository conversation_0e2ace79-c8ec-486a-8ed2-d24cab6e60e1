import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useAppSelector } from '../../store/hooks';
import { useLocation } from 'react-router-dom';
import { useCRUD } from '../../hooks/useCRUD';
import { BusinessIdea, businessIdeasAPI } from '../../services/incubatorApi';
import BusinessIdeaForm from '../../components/incubator/forms/BusinessIdeaForm';
import BusinessIdeaViewModal from '../../components/incubator/BusinessIdeaViewModal';
import { EnhancedCRUDTable } from '../../components/common';
import './BusinessIdeasPage.css';
// AuthenticatedLayout removed - page is already wrapped by route layout
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  Lightbulb,
  TrendingUp,
  Users,
  Calendar,
  AlertCircle,
  CheckCircle,
  Clock,
  Download,
  Filter,
  RefreshCw
} from 'lucide-react';

const BusinessIdeasPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);
  const location = useLocation();

  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedIdea, setSelectedIdea] = useState<BusinessIdea | null>(null);

  // CRUD operations for business ideas
  const businessIdeasCRUD = useCRUD({
    create: async (data: Partial<BusinessIdea>) => {
      if (!user) throw new Error('User not authenticated');
      return businessIdeasAPI.createBusinessIdea({
        ...data,
        owner_id: user.id
      });
    },
    read: () => businessIdeasAPI.getBusinessIdeas(),
    update: (id: number, data: Partial<BusinessIdea>) => 
      businessIdeasAPI.updateBusinessIdea(id, data),
    delete: (id: number) => businessIdeasAPI.deleteBusinessIdea(id)
  }, {
    onSuccess: (operation) => {
      if (operation === 'create') {
        setShowCreateForm(false);
      } else if (operation === 'update') {
        setShowEditForm(false);
        setSelectedIdea(null);
      } else if (operation === 'delete') {
        setShowDeleteDialog(false);
        setSelectedIdea(null);
      }
    }
  });

  // Load data on component mount
  useEffect(() => {
    businessIdeasCRUD.readItems();
  }, []);

  // Handle /new route - automatically open create form
  useEffect(() => {
    if (location.pathname.endsWith('/new')) {
      setShowCreateForm(true);
    }
  }, [location.pathname]);

  // Filter to show only user's ideas
  const userBusinessIdeas = businessIdeasCRUD.data.filter(idea => 
    idea.owner.id === user?.id
  );

  // Handle create
  const handleCreate = async (data: Partial<BusinessIdea>) => {
    return await businessIdeasCRUD.createItem(data);
  };

  // Handle edit
  const handleEdit = (idea: BusinessIdea) => {
    setSelectedIdea(idea);
    setShowEditForm(true);
  };

  // Handle update
  const handleUpdate = async (data: Partial<BusinessIdea>) => {
    if (!selectedIdea) return false;
    return await businessIdeasCRUD.updateItem(selectedIdea.id, data);
  };

  // Handle delete
  const handleDelete = (idea: BusinessIdea) => {
    setSelectedIdea(idea);
    setShowDeleteDialog(true);
  };

  // Handle export
  const handleExport = () => {
    const csvContent = [
      ['Title', 'Description', 'Stage', 'Status', 'Created', 'Updates'].join(','),
      ...userBusinessIdeas.map(idea => [
        `"${idea.title}"`,
        `"${idea.description}"`,
        idea.current_stage,
        idea.moderation_status,
        new Date(idea.created_at).toLocaleDateString(),
        idea.progress_count || 0
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `business-ideas-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  // Handle refresh
  const handleRefresh = () => {
    businessIdeasCRUD.readItems();
  };

  // Confirm delete
  const confirmDelete = async () => {
    if (!selectedIdea) return;
    await businessIdeasCRUD.deleteItem(selectedIdea.id);
  };

  // Table columns configuration
  const columns = [
    {
      key: 'title',
      label: t('incubator.title', 'Title'),
      render: (value: any, idea: BusinessIdea) => (
        <div>
          <div className="font-medium text-white">{idea.title}</div>
          <div className="text-sm text-gray-400 truncate max-w-xs">
            {idea.description}
          </div>
        </div>
      )
    },
    {
      key: 'current_stage',
      label: t('incubator.stage', 'Stage'),
      render: (value: any, idea: BusinessIdea) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          idea.current_stage === 'concept' ? 'bg-blue-900/50 text-blue-200' :
          idea.current_stage === 'validation' ? 'bg-yellow-900/50 text-yellow-200' :
          idea.current_stage === 'development' ? 'bg-orange-900/50 text-orange-200' :
          idea.current_stage === 'scaling' ? 'bg-purple-900/50 text-purple-200' :
          'bg-green-900/50 text-green-200'
        }`}>
          {t(`incubator.stages.${idea.current_stage}`, idea.current_stage)}
        </span>
      )
    },
    {
      key: 'moderation_status',
      label: t('common.status', 'Status'),
      render: (value: any, idea: BusinessIdea) => (
        <div className="flex items-center gap-2">
          {idea.moderation_status === 'approved' ? (
            <CheckCircle size={16} className="text-green-400" />
          ) : idea.moderation_status === 'rejected' ? (
            <AlertCircle size={16} className="text-red-400" />
          ) : (
            <Clock size={16} className="text-yellow-400" />
          )}
          <span className={`text-sm ${
            idea.moderation_status === 'approved' ? 'text-green-400' :
            idea.moderation_status === 'rejected' ? 'text-red-400' :
            'text-yellow-400'
          }`}>
            {t(`incubator.status.${idea.moderation_status}`, idea.moderation_status)}
          </span>
        </div>
      )
    },
    {
      key: 'progress_count',
      label: t('incubator.updates', 'Updates'),
      render: (value: any, idea: BusinessIdea) => (
        <span className="text-gray-300">{idea.progress_count || 0}</span>
      )
    },
    {
      key: 'created_at',
      label: t('common.created', 'Created'),
      render: (value: any, idea: BusinessIdea) => (
        <span className="text-gray-400 text-sm">
          {new Date(idea.created_at).toLocaleDateString()}
        </span>
      )
    }
  ];

  // Table actions
  const actions = [
    {
      label: t('incubator.view', 'View'),
      icon: <Eye className="w-4 h-4" />,
      onClick: (idea: BusinessIdea) => {
        setSelectedIdea(idea);
        setShowViewModal(true);
      },
      variant: 'secondary' as const
    },
    {
      label: t('incubator.edit', 'Edit'),
      icon: <Edit className="w-4 h-4" />,
      onClick: handleEdit,
      variant: 'primary' as const
    },
    {
      label: t('incubator.delete', 'Delete'),
      icon: <Trash2 className="w-4 h-4" />,
      onClick: handleDelete,
      variant: 'danger' as const
    }
  ];

  // Stats cards data
  const stats = [
    {
      title: t('incubator.totalIdeas', 'Total Ideas'),
      value: userBusinessIdeas.length,
      icon: Lightbulb,
      color: 'blue'
    },
    {
      title: t('incubator.approvedIdeas', 'Approved'),
      value: userBusinessIdeas.filter(idea => idea.moderation_status === 'approved').length,
      icon: CheckCircle,
      color: 'green'
    },
    {
      title: t('incubator.pendingIdeas', 'Pending'),
      value: userBusinessIdeas.filter(idea => idea.moderation_status === 'pending').length,
      icon: Clock,
      color: 'yellow'
    },
    {
      title: t('incubator.totalUpdates', 'Total Updates'),
      value: userBusinessIdeas.reduce((sum, idea) => sum + (idea.progress_count || 0), 0),
      icon: TrendingUp,
      color: 'purple'
    }
  ];

  return (
    <>
      <div className={`business-ideas-page ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="space-y-6">
              {/* Header */}
              <div className={`business-ideas-header rounded-xl p-6 ${isRTL ? 'text-right' : 'text-left'}`}>
                <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
                  <div>
                    <h1 className="text-3xl font-bold gradient-title mb-2">
                      {t('incubator.myBusinessIdeas', 'My Business Ideas')}
                    </h1>
                    <p className="text-white/70 text-lg">
                      {t('incubator.manageYourIdeas', 'Create and manage your business ideas')}
                    </p>
                  </div>
                  <div className={`flex items-center gap-3 mt-4 sm:mt-0 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <button
                      onClick={handleRefresh}
                      className="px-4 py-3 bg-white/10 hover:bg-white/20 text-white rounded-xl transition-all duration-300 flex items-center gap-2 border border-white/20"
                      title={t('common.refresh', 'Refresh')}
                    >
                      <RefreshCw size={18} />
                    </button>

                    <button
                      onClick={handleExport}
                      className="px-4 py-3 bg-white/10 hover:bg-white/20 text-white rounded-xl transition-all duration-300 flex items-center gap-2 border border-white/20"
                      title={t('common.export', 'Export')}
                    >
                      <Download size={18} />
                    </button>

                    <button
                      onClick={() => setShowCreateForm(true)}
                      className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-xl hover:shadow-glow transition-all duration-300 flex items-center gap-2 font-medium hover:scale-105"
                    >
                      <Plus size={20} />
                      {t('incubator.createIdea', 'Create Idea')}
                    </button>
                  </div>
                </div>
              </div>

              {/* Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {stats.map((stat, index) => (
                  <div key={index} className="business-ideas-stats-card rounded-xl p-6 group">
                    <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <div>
                        <p className="text-white/60 text-sm font-medium">{stat.title}</p>
                        <p className="text-3xl font-bold text-white mt-2">{stat.value}</p>
                      </div>
                      <div className={`p-4 rounded-xl transition-all duration-300 group-hover:scale-110 ${
                        stat.color === 'blue' ? 'icon-bg-blue' :
                        stat.color === 'green' ? 'icon-bg-green' :
                        stat.color === 'yellow' ? 'icon-bg-yellow' :
                        'icon-bg-purple'
                      }`}>
                        <stat.icon size={28} className={`${
                          stat.color === 'blue' ? 'text-blue-400' :
                          stat.color === 'green' ? 'text-green-400' :
                          stat.color === 'yellow' ? 'text-yellow-400' :
                          'text-purple-400'
                        } drop-shadow-lg`} />
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Error Display */}
              {businessIdeasCRUD.error && (
                <div className="bg-red-900/50 border border-red-500 rounded-lg p-4">
                  <div className="flex items-center gap-2">
                    <AlertCircle size={20} className="text-red-400" />
                    <span className="text-red-200">{businessIdeasCRUD.error}</span>
                  </div>
                </div>
              )}

              {/* Business Ideas Table */}
              <div className="business-ideas-table-container rounded-xl overflow-hidden" data-testid="business-ideas-list">
                <EnhancedCRUDTable
                  data={userBusinessIdeas}
                  columns={columns}
                  actions={actions}
                  isLoading={businessIdeasCRUD.isLoading}
                  emptyMessage={t('incubator.noIdeas', 'No business ideas found. Create your first idea to get started!')}
                  searchPlaceholder={t('incubator.searchIdeas', 'Search your business ideas...')}
                  title={t('incubator.businessIdeas', 'Business Ideas')}
                  createButtonLabel={t('incubator.createIdea', 'Create Idea')}
                  onCreate={() => setShowCreateForm(true)}
                  searchable={true}
                  sortable={true}
                  className="glass-table-dark"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Create Form Modal */}
      {showCreateForm && (
        <BusinessIdeaForm
          mode="create"
          onSubmit={handleCreate}
          onCancel={() => setShowCreateForm(false)}
          isSubmitting={businessIdeasCRUD.isLoading}
        />
      )}

      {/* Edit Form Modal */}
      {showEditForm && selectedIdea && (
        <BusinessIdeaForm
          mode="edit"
          initialData={selectedIdea}
          onSubmit={handleUpdate}
          onCancel={() => {
            setShowEditForm(false);
            setSelectedIdea(null);
          }}
          isSubmitting={businessIdeasCRUD.isLoading}
        />
      )}

      {/* Delete Confirmation Dialog */}
      {showDeleteDialog && selectedIdea && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-900 rounded-lg shadow-xl w-full max-w-md">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <AlertCircle size={24} className="text-red-400" />
                <h3 className="text-lg font-semibold text-white">
                  {t('common.confirmDelete', 'Confirm Delete')}
                </h3>
              </div>
              <p className="text-gray-300 mb-6">
                {t('incubator.deleteConfirmation', 'Are you sure you want to delete this business idea? This action cannot be undone.')}
              </p>
              <div className={`flex gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <button
                  onClick={() => {
                    setShowDeleteDialog(false);
                    setSelectedIdea(null);
                  }}
                  className="flex-1 px-4 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors"
                  disabled={businessIdeasCRUD.isLoading}
                >
                  {t('common.cancel', 'Cancel')}
                </button>
                <button
                  onClick={confirmDelete}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
                  disabled={businessIdeasCRUD.isLoading}
                >
                  {businessIdeasCRUD.isLoading ? t('common.deleting', 'Deleting...') : t('common.delete', 'Delete')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* View Modal */}
      <BusinessIdeaViewModal
        idea={selectedIdea}
        isOpen={showViewModal}
        onClose={() => {
          setShowViewModal(false);
          setSelectedIdea(null);
        }}
      />
    </>
  );
};

export default BusinessIdeasPage;
