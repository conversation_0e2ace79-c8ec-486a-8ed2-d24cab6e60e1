/**
 * Simple API cache utility to prevent duplicate requests
 * and reduce server load
 */

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiry: number;
}

class APICache {
  private cache = new Map<string, CacheEntry<any>>();
  private pendingRequests = new Map<string, Promise<any>>();

  /**
   * Get data from cache if valid, otherwise execute the fetcher function
   */
  async get<T>(
    key: string, 
    fetcher: () => Promise<T>, 
    ttlMinutes: number = 5
  ): Promise<T> {
    const now = Date.now();
    const cached = this.cache.get(key);

    // Return cached data if still valid
    if (cached && now < cached.expiry) {
      console.log(`📦 Cache hit for ${key}`);
      return cached.data;
    }

    // If there's already a pending request for this key, return it
    if (this.pendingRequests.has(key)) {
      // Reduced logging for performance
      if (process.env.NODE_ENV === 'development') {
        console.log(`⏳ Waiting for pending request: ${key}`);
      }
      return this.pendingRequests.get(key)!;
    }

    // Create new request
    // Reduced logging for performance
    if (process.env.NODE_ENV === 'development') {
      console.log(`🌐 Cache miss, fetching: ${key}`);
    }
    const requestPromise = fetcher()
      .then((data) => {
        // Cache the result
        this.cache.set(key, {
          data,
          timestamp: now,
          expiry: now + (ttlMinutes * 60 * 1000)
        });
        
        // Remove from pending requests
        this.pendingRequests.delete(key);
        
        return data;
      })
      .catch((error) => {
        // Remove from pending requests on error
        this.pendingRequests.delete(key);
        throw error;
      });

    // Store the pending request
    this.pendingRequests.set(key, requestPromise);

    return requestPromise;
  }

  /**
   * Invalidate cache entry
   */
  invalidate(key: string): void {
    this.cache.delete(key);
    this.pendingRequests.delete(key);
    console.log(`🗑️ Cache invalidated: ${key}`);
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
    this.pendingRequests.clear();
    console.log('🧹 Cache cleared');
  }

  /**
   * Get cache statistics
   */
  getStats(): { size: number; pending: number; entries: string[] } {
    return {
      size: this.cache.size,
      pending: this.pendingRequests.size,
      entries: Array.from(this.cache.keys())
    };
  }

  /**
   * Clean expired entries
   */
  cleanup(): void {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now >= entry.expiry) {
        this.cache.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      console.log(`🧹 Cleaned ${cleaned} expired cache entries`);
    }
  }
}

// Create singleton instance
export const apiCache = new APICache();

// Auto-cleanup every 10 minutes
setInterval(() => {
  apiCache.cleanup();
}, 10 * 60 * 1000);

// Cache key generators for common API calls
export const cacheKeys = {
  userStats: () => 'admin:user-stats',
  eventStats: () => 'admin:event-stats',
  postStats: () => 'admin:post-stats',
  resourceStats: () => 'admin:resource-stats',
  users: () => 'admin:users',
  recentActivity: () => 'admin:recent-activity',
  superAdminStats: () => 'super-admin:dashboard-stats',
  superAdminHealth: () => 'super-admin:system-health',
  superAdminCapabilities: () => 'super-admin:capabilities',
  analytics: (timeRange: string) => `analytics:overview:${timeRange}`,
  platformMetrics: (timeRange: string) => `analytics:platform:${timeRange}`,
};

export default apiCache;
