import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import Button from '../../components/ui/Button';
import { Badge } from '../../components/ui/badge';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { TrendingUp, TrendingDown, DollarSign, PieChart, BarChart3, Calendar } from 'lucide-react';
import { usePortfolio } from '../../hooks/useInvestment';

interface PortfolioItem {
  id: string;
  companyName: string;
  industry: string;
  investmentAmount: number;
  currentValue: number;
  shares: number;
  investmentDate: string;
  status: 'Active' | 'Exited' | 'Pending';
  performance: number; // percentage change
  lastUpdate: string;
}

interface PortfolioSummary {
  totalInvested: number;
  currentValue: number;
  totalReturn: number;
  returnPercentage: number;
  activeInvestments: number;
  exitedInvestments: number;
}

const PortfolioManagementPage: React.FC = () => {
  // Use real portfolio hook
  const {
    portfolio,
    portfolioMetrics,
    isLoading: loading,
    error,
    updatePortfolioItem
  } = usePortfolio();

  const [activeTab, setActiveTab] = useState('overview');

  // Portfolio data is now fetched by the hook

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'bg-green-100 text-green-800';
      case 'Exited': return 'bg-blue-100 text-blue-800';
      case 'Pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPerformanceColor = (performance: number) => {
    return performance >= 0 ? 'text-green-600' : 'text-red-600';
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Portfolio Management</h1>
          <p className="text-gray-600 mt-1">Track and manage your investment portfolio</p>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <PieChart className="w-4 h-4 mr-2" />
          Portfolio Analytics
        </Button>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Invested</p>
                  <p className="text-2xl font-bold">{formatCurrency(portfolioMetrics.totalInvestment)}</p>
                </div>
                <DollarSign className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Current Value</p>
                  <p className="text-2xl font-bold">{formatCurrency(portfolioMetrics.totalValue)}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Return</p>
                  <p className="text-2xl font-bold text-green-600">
                    {formatCurrency(portfolioMetrics.totalReturn)}
                  </p>
                  <p className="text-sm text-green-600">+{((portfolioMetrics.totalReturn / portfolioMetrics.totalInvestment) * 100).toFixed(1)}%</p>
                </div>
                <BarChart3 className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Investments</p>
                  <p className="text-2xl font-bold">{portfolioMetrics.activeInvestments}</p>
                  <p className="text-sm text-gray-600">{portfolioMetrics.exitedInvestments} exited</p>
                </div>
                <PieChart className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Portfolio Details */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="active">Active Investments</TabsTrigger>
          <TabsTrigger value="exited">Exited Investments</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Portfolio Holdings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {portfolio.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-3">
                        <div>
                          <h3 className="font-semibold">{item.companyName}</h3>
                          <p className="text-sm text-gray-600">{item.industry}</p>
                        </div>
                        <Badge className={getStatusColor(item.status)}>
                          {item.status}
                        </Badge>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-4 gap-8 text-right">
                      <div>
                        <p className="text-sm text-gray-600">Invested</p>
                        <p className="font-semibold">{formatCurrency(item.investmentAmount)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Current Value</p>
                        <p className="font-semibold">{formatCurrency(item.currentValue)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Performance</p>
                        <div className="flex items-center justify-end">
                          {item.performance >= 0 ? (
                            <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
                          ) : (
                            <TrendingDown className="w-4 h-4 text-red-600 mr-1" />
                          )}
                          <span className={getPerformanceColor(item.performance)}>
                            {item.performance >= 0 ? '+' : ''}{item.performance}%
                          </span>
                        </div>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Shares</p>
                        <p className="font-semibold">{item.shares.toLocaleString()}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="active" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Active Investments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {portfolio.filter(item => item.status === 'Active').map((item) => (
                  <div key={item.id} className="p-4 border rounded-lg">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="font-semibold text-lg">{item.companyName}</h3>
                        <p className="text-gray-600">{item.industry}</p>
                      </div>
                      <Badge className="bg-green-100 text-green-800">Active</Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600">Investment Date</p>
                        <p className="font-medium">{formatDate(item.investmentDate)}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Amount Invested</p>
                        <p className="font-medium">{formatCurrency(item.investmentAmount)}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Current Value</p>
                        <p className="font-medium">{formatCurrency(item.currentValue)}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Return</p>
                        <p className={`font-medium ${getPerformanceColor(item.performance)}`}>
                          {formatCurrency(item.currentValue - item.investmentAmount)} ({item.performance}%)
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex gap-2 mt-4">
                      <Button variant="outline" size="sm">View Details</Button>
                      <Button variant="outline" size="sm">Update Valuation</Button>
                      <Button variant="outline" size="sm">Add Notes</Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="exited" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Exited Investments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {portfolio.filter(item => item.status === 'Exited').map((item) => (
                  <div key={item.id} className="p-4 border rounded-lg bg-gray-50">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="font-semibold text-lg">{item.companyName}</h3>
                        <p className="text-gray-600">{item.industry}</p>
                      </div>
                      <Badge className="bg-blue-100 text-blue-800">Exited</Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600">Investment Period</p>
                        <p className="font-medium">{formatDate(item.investmentDate)} - {formatDate(item.lastUpdate)}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Total Invested</p>
                        <p className="font-medium">{formatCurrency(item.investmentAmount)}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Exit Value</p>
                        <p className="font-medium">{formatCurrency(item.currentValue)}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Total Return</p>
                        <p className={`font-medium ${getPerformanceColor(item.performance)}`}>
                          {formatCurrency(item.currentValue - item.investmentAmount)} ({item.performance}%)
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Portfolio Allocation</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-center text-gray-600">Portfolio allocation chart would go here</p>
                  <div className="h-64 bg-gray-100 rounded flex items-center justify-center">
                    <PieChart className="w-16 h-16 text-gray-400" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Performance Over Time</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-center text-gray-600">Performance chart would go here</p>
                  <div className="h-64 bg-gray-100 rounded flex items-center justify-center">
                    <BarChart3 className="w-16 h-16 text-gray-400" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PortfolioManagementPage;
