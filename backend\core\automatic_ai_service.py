"""
Automatic AI Service
Provides real automatic AI functionality for business analysis, recommendations, and monitoring
"""

import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from django.utils import timezone
from django.contrib.auth.models import User
from django.db import transaction
from .ai_service import ai_chat, ai_analyze_business, ai_get_status

logger = logging.getLogger(__name__)


class AutomaticAIService:
    """
    Service for managing automatic AI operations
    """
    
    def __init__(self):
        self.is_running = False
        self.worker_thread = None
        self.last_run = None
        self.next_scheduled = None
        self.running_tasks = 0
        self.status = 'idle'
        self.error_count = 0
        self.success_count = 0
        
    def start(self, user_id: Optional[int] = None) -> Dict[str, Any]:
        """Start automatic AI processing"""
        try:
            if self.is_running:
                return {
                    'success': False,
                    'message': 'Automatic AI is already running',
                    'status': self.status
                }
            
            # Check if AI service is available
            ai_status = ai_get_status()
            if not ai_status.get('available', False):
                return {
                    'success': False,
                    'message': 'AI service is not available. Please check configuration.',
                    'status': 'unavailable',
                    'ai_status': ai_status
                }
            
            self.is_running = True
            self.status = 'starting'
            self.error_count = 0
            
            # Start background worker thread
            self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
            self.worker_thread.start()
            
            logger.info("🚀 Automatic AI service started")
            
            return {
                'success': True,
                'message': 'Automatic AI processing started successfully',
                'status': 'running',
                'started_at': timezone.now().isoformat(),
                'user_id': user_id
            }
            
        except Exception as e:
            logger.error(f"Failed to start automatic AI: {e}")
            self.is_running = False
            self.status = 'error'
            return {
                'success': False,
                'message': f'Failed to start automatic AI: {str(e)}',
                'status': 'error'
            }
    
    def stop(self, user_id: Optional[int] = None) -> Dict[str, Any]:
        """Stop automatic AI processing"""
        try:
            if not self.is_running:
                return {
                    'success': False,
                    'message': 'Automatic AI is not running',
                    'status': self.status
                }
            
            self.is_running = False
            self.status = 'stopping'
            
            # Wait for worker thread to finish current task
            if self.worker_thread and self.worker_thread.is_alive():
                self.worker_thread.join(timeout=10)  # Wait up to 10 seconds
            
            self.status = 'stopped'
            self.worker_thread = None
            
            logger.info("🛑 Automatic AI service stopped")
            
            return {
                'success': True,
                'message': 'Automatic AI processing stopped successfully',
                'status': 'stopped',
                'stopped_at': timezone.now().isoformat(),
                'user_id': user_id
            }
            
        except Exception as e:
            logger.error(f"Failed to stop automatic AI: {e}")
            self.status = 'error'
            return {
                'success': False,
                'message': f'Failed to stop automatic AI: {str(e)}',
                'status': 'error'
            }
    
    def trigger_manual_analysis(self, user_id: Optional[int] = None) -> Dict[str, Any]:
        """Trigger manual AI analysis"""
        try:
            # Check if AI service is available
            ai_status = ai_get_status()
            if not ai_status.get('available', False):
                return {
                    'success': False,
                    'message': 'AI service is not available',
                    'ai_status': ai_status
                }
            
            # Run immediate analysis
            analysis_results = self._run_business_analysis()
            
            return {
                'success': True,
                'message': 'Manual AI analysis triggered successfully',
                'triggered_at': timezone.now().isoformat(),
                'results': analysis_results,
                'user_id': user_id
            }
            
        except Exception as e:
            logger.error(f"Failed to trigger manual analysis: {e}")
            return {
                'success': False,
                'message': f'Failed to trigger manual analysis: {str(e)}',
                'error': str(e)
            }
    
    def get_status(self) -> Dict[str, Any]:
        """Get current automatic AI status"""
        return {
            'automatic_ai_enabled': True,
            'is_running': self.is_running,
            'status': self.status,
            'running_tasks': self.running_tasks,
            'last_run': self.last_run.isoformat() if self.last_run else None,
            'next_scheduled': self.next_scheduled.isoformat() if self.next_scheduled else None,
            'error_count': self.error_count,
            'success_count': self.success_count,
            'worker_alive': self.worker_thread.is_alive() if self.worker_thread else False,
            'ai_service_status': ai_get_status(),
            'timestamp': timezone.now().isoformat()
        }
    
    def _worker_loop(self):
        """Background worker loop for automatic AI processing"""
        logger.info("🔄 Automatic AI worker loop started")
        self.status = 'running'
        
        while self.is_running:
            try:
                # Run periodic analysis every 30 minutes
                if self._should_run_analysis():
                    self.running_tasks += 1
                    self.status = 'processing'
                    
                    analysis_results = self._run_business_analysis()
                    
                    if analysis_results.get('success', False):
                        self.success_count += 1
                        logger.info("✅ Automatic AI analysis completed successfully")
                    else:
                        self.error_count += 1
                        logger.warning("⚠️ Automatic AI analysis completed with errors")
                    
                    self.last_run = timezone.now()
                    self.next_scheduled = self.last_run + timedelta(minutes=30)
                    self.running_tasks -= 1
                    self.status = 'running'
                
                # Sleep for 1 minute before checking again
                time.sleep(60)
                
            except Exception as e:
                logger.error(f"Error in automatic AI worker loop: {e}")
                self.error_count += 1
                self.status = 'error'
                time.sleep(60)  # Wait before retrying
        
        self.status = 'stopped'
        logger.info("🔄 Automatic AI worker loop stopped")
    
    def _should_run_analysis(self) -> bool:
        """Check if analysis should be run"""
        if not self.last_run:
            return True
        
        # Run every 30 minutes
        return timezone.now() - self.last_run > timedelta(minutes=30)
    
    def _run_business_analysis(self) -> Dict[str, Any]:
        """Run automatic business analysis"""
        try:
            # Get recent business ideas for analysis
            from incubator.models_base import BusinessIdea
            
            recent_ideas = BusinessIdea.objects.filter(
                created_at__gte=timezone.now() - timedelta(days=7)
            ).order_by('-created_at')[:5]
            
            analysis_results = []
            
            for idea in recent_ideas:
                try:
                    # Analyze business idea
                    analysis = ai_analyze_business(
                        business_idea=idea.description,
                        language='auto',
                        user_id=idea.owner.id
                    )
                    
                    if analysis.get('success', False):
                        analysis_results.append({
                            'business_idea_id': idea.id,
                            'business_idea_title': idea.title,
                            'analysis': analysis.get('data', {}),
                            'status': 'success'
                        })
                    else:
                        analysis_results.append({
                            'business_idea_id': idea.id,
                            'business_idea_title': idea.title,
                            'error': analysis.get('error', 'Unknown error'),
                            'status': 'error'
                        })
                        
                except Exception as e:
                    logger.error(f"Error analyzing business idea {idea.id}: {e}")
                    analysis_results.append({
                        'business_idea_id': idea.id,
                        'business_idea_title': idea.title,
                        'error': str(e),
                        'status': 'error'
                    })
            
            return {
                'success': True,
                'analyzed_count': len(analysis_results),
                'results': analysis_results,
                'timestamp': timezone.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in automatic business analysis: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': timezone.now().isoformat()
            }


# Global instance
_automatic_ai_service = None

def get_automatic_ai_service() -> AutomaticAIService:
    """Get the global automatic AI service instance"""
    global _automatic_ai_service
    if _automatic_ai_service is None:
        _automatic_ai_service = AutomaticAIService()
    return _automatic_ai_service
