import React from 'react';
// DashboardLayout removed - handled by routing system with AuthenticatedLayout
import {
  AnalyticsTabs,
  OverviewTab,
  ActivityTab,
  ContributorsTab,
  PopularThreadsTab,
  EngagementTab
} from './components';
import { useForumAnalyticsData } from './hooks';
import { useTranslation } from 'react-i18next';

const ForumAnalytics: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const {
    activeTab,
    setActiveTab,
    loading,
    overviewData,
    activityData,
    contributorsData,
    popularThreadsData,
    engagementData,
    period,
    setPeriod,
    timeRange,
    setTimeRange,
    popularPeriod,
    setPopularPeriod
  } = useForumAnalyticsData();

  return (
    <div className="mb-8">
        <h1 className="text-2xl font-bold">t("admin.forum.analytics", "Forum Analytics")</h1>
        <div className="text-gray-400 mt-1">t("admin.analyze.forum.activity", "Analyze forum activity and engagement")</div>
      </div>

      {/* Tab Navigation */}
      <AnalyticsTabs activeTab={activeTab} onTabChange={setActiveTab} />

      {/* Loading State */}
      {loading && (
        <div className={`flex justify-center items-center py-12 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
        </div>
      )}

      {/* Tab Content */}
      {!loading && (
        <>
          {activeTab === 'overview' && <OverviewTab data={overviewData} />}
          
          {activeTab === 'activity' && (
            <ActivityTab
              data={activityData}
              period={period}
              timeRange={timeRange}
              onPeriodChange={setPeriod}
              onTimeRangeChange={setTimeRange}
            />
          )}
          
          {activeTab === 'contributors' && <ContributorsTab data={contributorsData} />}
          
          {activeTab === 'popular' && (
            <PopularThreadsTab
              data={popularThreadsData}
              popularPeriod={popularPeriod}
              onPopularPeriodChange={setPopularPeriod}
            />
          )}
          
          {activeTab === 'engagement' && <EngagementTab data={engagementData} />}
        </>
      )}
    </div>
  );
};

export default ForumAnalytics;
