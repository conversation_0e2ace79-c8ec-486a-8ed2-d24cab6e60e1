/**
 * ROLE SYSTEM FIX VERIFICATION
 * Quick test to verify that the role system conflicts have been resolved
 */

import { 
  getUserRoles, 
  getUserPermissions, 
  canAccessRoute, 
  getDashboardRoute, 
  isSuperAdmin,
  isAdmin,
  hasRole,
  UserRole 
} from './unifiedRoleManager';

// Test user data
const testUsers = {
  superAdmin: {
    id: 1,
    username: 'super_admin_test',
    email: '<EMAIL>',
    first_name: 'Super',
    last_name: 'Admin',
    is_superuser: true,
    is_admin: true,
    is_staff: true,
    profile: null
  },
  admin: {
    id: 2,
    username: 'admin_test',
    email: '<EMAIL>',
    first_name: 'Admin',
    last_name: 'User',
    is_superuser: false,
    is_admin: true,
    is_staff: true,
    profile: null
  },
  moderator: {
    id: 3,
    username: 'moderator_test',
    email: '<EMAIL>',
    first_name: 'Moderator',
    last_name: 'User',
    is_superuser: false,
    is_admin: false,
    is_staff: true,
    profile: null
  },
  mentor: {
    id: 4,
    username: 'mentor_test',
    email: '<EMAIL>',
    first_name: '<PERSON><PERSON>',
    last_name: 'User',
    is_superuser: false,
    is_admin: false,
    is_staff: false,
    profile: {
      primary_role: { name: 'mentor' },
      active_roles: [{ name: 'mentor' }]
    }
  },
  regularUser: {
    id: 5,
    username: 'user_test',
    email: '<EMAIL>',
    first_name: 'Regular',
    last_name: 'User',
    is_superuser: false,
    is_admin: false,
    is_staff: false,
    profile: null
  }
};

/**
 * Run comprehensive role system tests
 */
export function testRoleSystemFixes() {
  console.log('🧪 Testing Role System Fixes...\n');
  
  let passedTests = 0;
  let totalTests = 0;
  const issues: string[] = [];

  // Helper function to run a test
  const runTest = (testName: string, testFn: () => boolean, expectedResult: boolean = true) => {
    totalTests++;
    try {
      const result = testFn();
      if (result === expectedResult) {
        console.log(`✅ ${testName}`);
        passedTests++;
      } else {
        console.log(`❌ ${testName} - Expected: ${expectedResult}, Got: ${result}`);
        issues.push(`${testName} failed`);
      }
    } catch (error) {
      console.log(`❌ ${testName} - Error: ${error}`);
      issues.push(`${testName} threw error: ${error}`);
    }
  };

  console.log('=== ROLE DETERMINATION TESTS ===');
  
  // Test 1: Super Admin Role Detection
  runTest('Super Admin has super_admin role', () => {
    const roles = getUserRoles(testUsers.superAdmin);
    return roles.includes('super_admin');
  });

  // Test 2: Admin Role Detection
  runTest('Admin has admin role', () => {
    const roles = getUserRoles(testUsers.admin);
    return roles.includes('admin');
  });

  // Test 3: Moderator Role Detection
  runTest('Moderator has moderator role', () => {
    const roles = getUserRoles(testUsers.moderator);
    return roles.includes('moderator');
  });

  // Test 4: Mentor Role Detection
  runTest('Mentor has mentor role', () => {
    const roles = getUserRoles(testUsers.mentor);
    return roles.includes('mentor');
  });

  // Test 5: Regular User Role Detection
  runTest('Regular user has user role', () => {
    const roles = getUserRoles(testUsers.regularUser);
    return roles.includes('user');
  });

  console.log('\n=== PERMISSION TESTS ===');

  // Test 6: Super Admin Permissions
  runTest('Super Admin has super_admin permission', () => {
    const permissions = getUserPermissions(testUsers.superAdmin);
    return permissions.includes('super_admin');
  });

  // Test 7: Admin Permissions
  runTest('Admin has admin permission', () => {
    const permissions = getUserPermissions(testUsers.admin);
    return permissions.includes('admin');
  });

  console.log('\n=== ACCESS CONTROL TESTS ===');

  // Test 8: Super Admin Can Access Everything
  runTest('Super Admin can access admin routes', () => {
    return canAccessRoute(testUsers.superAdmin, ['admin'], ['admin'], true);
  });

  // Test 9: Admin Can Access Admin Routes
  runTest('Admin can access admin routes', () => {
    return canAccessRoute(testUsers.admin, ['admin'], ['admin'], true);
  });

  // Test 10: Regular User Cannot Access Admin Routes
  runTest('Regular user cannot access admin routes', () => {
    return canAccessRoute(testUsers.regularUser, ['admin'], ['admin'], true);
  }, false);

  // Test 11: Mentor Can Access User Routes
  runTest('Mentor can access user routes', () => {
    return canAccessRoute(testUsers.mentor, ['user'], ['read'], true);
  });

  console.log('\n=== DASHBOARD ROUTING TESTS ===');

  // Test 12: Super Admin Dashboard Route
  runTest('Super Admin gets /super_admin dashboard', () => {
    return getDashboardRoute(testUsers.superAdmin) === '/super_admin';
  });

  // Test 13: Admin Dashboard Route
  runTest('Admin gets /admin dashboard', () => {
    return getDashboardRoute(testUsers.admin) === '/admin';
  });

  // Test 14: Regular User Dashboard Route
  runTest('Regular user gets /dashboard', () => {
    return getDashboardRoute(testUsers.regularUser) === '/dashboard';
  });

  console.log('\n=== HELPER FUNCTION TESTS ===');

  // Test 15: isSuperAdmin Function
  runTest('isSuperAdmin correctly identifies super admin', () => {
    return isSuperAdmin(testUsers.superAdmin);
  });

  // Test 16: isSuperAdmin Function (Negative)
  runTest('isSuperAdmin correctly identifies non-super admin', () => {
    return isSuperAdmin(testUsers.admin);
  }, false);

  // Test 17: isAdmin Function
  runTest('isAdmin correctly identifies admin', () => {
    return isAdmin(testUsers.admin);
  });

  // Test 18: hasRole Function
  runTest('hasRole correctly identifies mentor role', () => {
    return hasRole(testUsers.mentor, 'mentor');
  });

  console.log('\n=== TEST SUMMARY ===');
  console.log(`✅ Passed: ${passedTests}/${totalTests} tests`);
  console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests} tests`);
  
  if (issues.length > 0) {
    console.log('\n🚨 Issues Found:');
    issues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue}`);
    });
  } else {
    console.log('\n🎉 All tests passed! Role system is working correctly.');
  }

  return {
    passed: passedTests,
    total: totalTests,
    success: passedTests === totalTests,
    issues
  };
}

/**
 * Quick test that can be run in browser console
 */
export function quickTest() {
  console.log('🚀 Quick Role System Test');
  
  // Test the most critical functions
  const superAdmin = testUsers.superAdmin;
  const regularUser = testUsers.regularUser;
  
  console.log('Super Admin roles:', getUserRoles(superAdmin));
  console.log('Super Admin dashboard:', getDashboardRoute(superAdmin));
  console.log('Super Admin can access admin routes:', canAccessRoute(superAdmin, ['admin'], ['admin'], true));
  
  console.log('Regular User roles:', getUserRoles(regularUser));
  console.log('Regular User dashboard:', getDashboardRoute(regularUser));
  console.log('Regular User can access admin routes:', canAccessRoute(regularUser, ['admin'], ['admin'], true));
  
  console.log('✅ Quick test complete - check results above');
}

// Export for global access in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).testRoleSystemFixes = testRoleSystemFixes;
  (window as any).quickRoleTest = quickTest;
}
