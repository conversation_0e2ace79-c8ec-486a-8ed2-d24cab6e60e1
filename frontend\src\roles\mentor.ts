/**
 * MENTOR ROLE CONFIGURATION
 * Dedicated file for mentor role - no duplicates, single source of truth
 */

import { UserRole, PermissionLevel } from '../utils/unifiedRoleManager';

export const MENTOR_ROLE: UserRole = 'mentor';

export const MENTOR_PERMISSIONS: PermissionLevel[] = ['read', 'write'];

export const MENTOR_NAVIGATION_ITEMS = [
  {
    id: 'dashboard',
    name: 'dashboard.title',
    path: '/dashboard',
    icon: 'Home',
    category: 'main' as const
  },
  {
    id: 'mentor-dashboard',
    name: 'mentor.dashboard.title',
    path: '/dashboard/mentor',
    icon: 'Award',
    category: 'main' as const
  },

  {
    id: 'mentees',
    name: 'mentorship.mentees.title',
    path: '/dashboard/mentorship/mentees',
    icon: 'UserCheck',
    category: 'main' as const
  },
  {
    id: 'mentorship-sessions',
    name: 'mentorship.sessions.title',
    path: '/dashboard/mentorship/sessions',
    icon: 'Calendar',
    category: 'main' as const
  },
  {
    id: 'mentorship-calendar',
    name: 'mentorship.calendar.title',
    path: '/dashboard/mentorship/calendar',
    icon: 'Calendar',
    category: 'main' as const
  },
  {
    id: 'mentor-availability',
    name: 'mentor.availability.title',
    path: '/dashboard/mentorship/availability',
    icon: 'Clock',
    category: 'main' as const
  },
  {
    id: 'mentor-profile',
    name: 'mentor.profile.title',
    path: '/dashboard/mentorship/profile',
    icon: 'User',
    category: 'main' as const
  },
  {
    id: 'mentor-analytics',
    name: 'mentor.analytics.title',
    path: '/dashboard/mentorship/analytics',
    icon: 'TrendingUp',
    category: 'main' as const
  },
  {
    id: 'business-ideas',
    name: 'businessIdeas.title',
    path: '/dashboard/business-ideas',
    icon: 'Lightbulb',
    category: 'content' as const
  },
  {
    id: 'forums',
    name: 'forums.title',
    path: '/dashboard/forums',
    icon: 'MessageSquare',
    category: 'content' as const
  },
  {
    id: 'resources',
    name: 'resources.title',
    path: '/dashboard/resources',
    icon: 'BookOpen',
    category: 'content' as const
  },
  {
    id: 'ai-assistant',
    name: 'ai.assistant.title',
    path: '/dashboard/ai',
    icon: 'Bot',
    category: 'ai' as const
  },
  {
    id: 'profile',
    name: 'profile.title',
    path: '/profile',
    icon: 'User',
    category: 'main' as const
  },
  {
    id: 'settings',
    name: 'settings.title',
    path: '/settings',
    icon: 'Settings',
    category: 'main' as const
  }
];

export const MENTOR_ROUTES = [
  '/dashboard',
  '/dashboard/mentor',
  '/dashboard/mentorship/mentees',
  '/dashboard/mentorship/sessions',
  '/dashboard/mentorship/calendar',
  '/dashboard/mentorship/availability',
  '/dashboard/mentorship/profile',
  '/dashboard/mentorship/analytics',
  '/dashboard/business-ideas',
  '/dashboard/forums',
  '/dashboard/resources',
  '/dashboard/ai',
  '/profile',
  '/settings'
];

export const MENTOR_DASHBOARD_CONFIG = {
  defaultRoute: '/dashboard/mentor',
  welcomeMessage: 'Welcome to your mentor dashboard',
  features: [
    'mentee_management',
    'session_scheduling',
    'availability_management',
    'mentor_analytics',
    'business_idea_review',
    'forum_participation',
    'resource_access',
    'ai_assistant'
  ]
};

/**
 * Check if a user object represents a mentor
 */
export function isMentorRole(user: any): boolean {
  if (!user) return false;
  
  // Explicit mentor role
  if (user.user_role === 'mentor') return true;
  
  // Check profile-based role
  if (user.profile?.primary_role?.name === 'mentor') return true;
  
  // Check active roles
  if (user.profile?.active_roles) {
    return user.profile.active_roles.some((role: any) => role.name === 'mentor');
  }
  
  return false;
}

/**
 * Get mentor-specific dashboard route
 */
export function getMentorDashboardRoute(): string {
  return MENTOR_DASHBOARD_CONFIG.defaultRoute;
}

/**
 * Check if mentor can access specific mentorship feature
 */
export function canAccessMentorshipFeature(user: any, feature: string): boolean {
  if (!isMentorRole(user)) return false;
  
  return MENTOR_DASHBOARD_CONFIG.features.includes(feature);
}

export default {
  role: MENTOR_ROLE,
  permissions: MENTOR_PERMISSIONS,
  navigationItems: MENTOR_NAVIGATION_ITEMS,
  routes: MENTOR_ROUTES,
  dashboardConfig: MENTOR_DASHBOARD_CONFIG,
  isRole: isMentorRole,
  getDashboardRoute: getMentorDashboardRoute,
  canAccessFeature: canAccessMentorshipFeature
};
