/**
 * Consolidated AI Page
 * Main page for the new consolidated AI service with all features
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation, Link } from 'react-router-dom';
import {
  MessageSquare,
  BarChart3,
  Activity,
  Settings,
  Sparkles,
  Globe,
  Brain,
  Zap,
  ArrowLeft,
  Bot,
  Shield,
} from 'lucide-react';
import { useAppSelector } from '../store/hooks';
import { useLanguage } from '../hooks/useLanguage';
import { useTranslation } from 'react-i18next';
import { businessIdeasAPI } from '../services/incubatorApi';
import { useCentralizedAI as useConsolidatedAI } from '../hooks/useCentralizedAI';
import ConsolidatedAIChat from '../components/ai/ConsolidatedAIChat';
import ConsolidatedBusinessAnalysis from '../components/ai/ConsolidatedBusinessAnalysis';
import ConsolidatedAIStatus from '../components/ai/ConsolidatedAIStatus';
import RoleBasedAIInterface from '../components/ai/RoleBasedAIInterface';
import { RTLText } from '../components/common';
import { getRoleBasedAIConfig, getUserAICapabilities, getUserAIRateLimits, AICapability } from '../utils/roleBasedAI';
import { getUserRoles, isSuperAdmin } from '../utils/unifiedRoleManager';
import RoleBasedAIAccess, { AICapabilitiesDisplay } from '../components/ai/RoleBasedAIAccess';

interface ConsolidatedAIPageProps {
  businessIdeaId?: number;
}

type TabType = 'features' | 'chat' | 'analysis' | 'status' | 'settings' | 'access';

export const ConsolidatedAIPage: React.FC<ConsolidatedAIPageProps> = ({
  businessIdeaId: propBusinessIdeaId,
}) => {
  const { businessIdeaId: paramBusinessIdeaId } = useParams<{ businessIdeaId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAppSelector(state => state.auth);
  const { t } = useTranslation();
  const { isRTL, language: currentLanguage } = useLanguage();

  // Determine initial tab based on URL path
  const getInitialTab = (): TabType => {
    if (location.pathname.includes('/chat/enhancedBusiness')) {
      return 'analysis';
    }
    return 'features';
  };

  const [activeTab, setActiveTab] = useState<TabType>(getInitialTab());
  const [language, setLanguage] = useState<string>('auto');
  const [businessIdea, setBusinessIdea] = useState<any>(null);
  const [selectedCapability, setSelectedCapability] = useState<AICapability | null>(null);
  const [availableBusinessIdeas, setAvailableBusinessIdeas] = useState<any[]>([]);
  const [loadingBusinessIdeas, setLoadingBusinessIdeas] = useState(false);

  // Get role-based AI configuration
  const aiConfig = getRoleBasedAIConfig(user);
  const userCapabilities = getUserAICapabilities(user);
  const userRoles = getUserRoles(user);
  const rateLimits = getUserAIRateLimits(user);

  const businessIdeaId = propBusinessIdeaId || (paramBusinessIdeaId ? parseInt(paramBusinessIdeaId) : undefined);

  const {
    status,
    statusError: error,
    refreshStatus,
    isAvailable,
  } = useConsolidatedAI();

  // Create availability object from status - Fix nested status structure
  const availability = {
    consolidatedAI: (status as any)?.status?.available || status?.available || false,
    workflows: (status as any)?.status?.features?.business_analysis || status?.features?.business_analysis || false,
    mlService: (status as any)?.status?.features?.intelligent_responses || status?.features?.intelligent_responses || false,
    arabicProcessing: (status as any)?.status?.features?.multilingual || status?.features?.multilingual || false,
  };

  // Load business idea if ID is provided
  useEffect(() => {
    if (businessIdeaId) {
      const loadBusinessIdea = async () => {
        try {
          setLoadingBusinessIdeas(true);
          const idea = await businessIdeasAPI.getBusinessIdea(parseInt(businessIdeaId));

          if (idea) {
            setBusinessIdea({
              id: idea.id.toString(),
              title: idea.title,
              description: idea.description,
              category: idea.tags && idea.tags.length > 0 ? idea.tags[0].name : 'General',
              current_stage: idea.current_stage,
            });
          } else {
            console.error('Business idea not found');
            // Fallback to mock data if API fails
            setBusinessIdea({
              id: businessIdeaId,
              title: 'Business Idea Not Found',
              description: 'The requested business idea could not be loaded.',
              category: 'Unknown',
              current_stage: 'concept',
            });
          }
        } catch (error) {
          console.error('Error loading business idea:', error);
          // Fallback to mock data if API fails
          setBusinessIdea({
            id: businessIdeaId,
            title: 'Error Loading Business Idea',
            description: 'There was an error loading the business idea. Please try again.',
            category: 'Error',
            current_stage: 'concept',
          });
        } finally {
          setLoadingBusinessIdeas(false);
        }
      };

      loadBusinessIdea();
    }
  }, [businessIdeaId]);

  // Fetch available business ideas when no specific ID is provided
  useEffect(() => {
    if (!businessIdeaId && user) {
      const fetchBusinessIdeas = async () => {
        setLoadingBusinessIdeas(true);
        try {
          const ideas = await businessIdeasAPI.getBusinessIdeas();
          // Filter to user's business ideas
          const userIdeas = ideas.filter(idea => idea.owner.id === user.id);
          setAvailableBusinessIdeas(userIdeas);
        } catch (error) {
          console.error('Error fetching business ideas:', error);
          setAvailableBusinessIdeas([]);
        } finally {
          setLoadingBusinessIdeas(false);
        }
      };

      fetchBusinessIdeas();
    }
  }, [businessIdeaId, user]);

  // Check if user is super admin for sensitive tabs
  const isUserSuperAdmin = user && isSuperAdmin(user);

  const tabs = [
    {
      id: 'features' as TabType,
      name: t('admin.chat.aiFeatures', 'AI Features'),
      icon: <Sparkles className="w-5 h-5" />,
      description: t('admin.chat.featuresOverview', 'Overview of all AI capabilities and features'),
      available: true,
      isInternal: true,
    },
    {
      id: 'chat' as TabType,
      name: t('ai.chat.title'),
      icon: <MessageSquare className="w-5 h-5" />,
      description: t('ai.chat.description'),
      available: true, // Always available for users - they should be able to access chat interface
      isInternal: true,
    },
    {
      id: 'analysis' as TabType,
      name: t('ai.analysis.title'),
      icon: <BarChart3 className="w-5 h-5" />,
      description: t('ai.analysis.description'),
      available: true, // Always available - users can access analysis features
      isInternal: true,
    },
    // AI Status tab - ONLY for super admins (contains sensitive system information)
    ...(isUserSuperAdmin ? [{
      id: 'status' as TabType,
      name: t('ai.status.title'),
      icon: <Activity className="w-5 h-5" />,
      description: t('ai.status.description'),
      available: true,
      isInternal: true,
    }] : []),
    {
      id: 'settings' as TabType,
      name: t('ai.settings.title'),
      icon: <Settings className="w-5 h-5" />,
      description: t('ai.settings.description'),
      available: true,
      isInternal: true,
    },
    {
      id: 'access' as TabType,
      name: t('ai.access.title', 'AI Access'),
      icon: <Shield className="w-5 h-5" />,
      description: t('ai.access.description', 'View your AI capabilities and role-based access'),
      available: true,
      isInternal: true,
    },
  ];

  const capabilities = [
    {
      name: t('ai.capabilities.advancedAI'),
      icon: <Sparkles className="w-4 h-4" />,
      available: availability.consolidatedAI,
      description: t('ai.capabilities.advancedAIDesc'),
    },
    {
      name: t('ai.capabilities.workflows'),
      icon: <Zap className="w-4 h-4" />,
      available: availability.workflows,
      description: t('ai.capabilities.workflowsDesc'),
    },
    {
      name: t('ai.capabilities.mlInsights'),
      icon: <Brain className="w-4 h-4" />,
      available: availability.mlService,
      description: t('ai.capabilities.mlInsightsDesc'),
    },
    {
      name: t('ai.capabilities.culturalContext'),
      icon: <Globe className="w-4 h-4" />,
      available: availability.arabicProcessing,
      description: t('ai.capabilities.culturalContextDesc'),
    },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'features':
        return (
          <div className="space-y-8">
            {/* Role-Based Access Control */}
            <RoleBasedAIAccess>
              {/* Role-Based AI Interface */}
              <RoleBasedAIInterface
                onCapabilitySelect={(capability) => {
                  setSelectedCapability(capability);
                  // Auto-switch to appropriate tabs based on capability
                  if (capability.id === 'ai-assistant' || capability.id.includes('chat') || capability.id.includes('mentoring')) {
                    setActiveTab('chat');
                  } else if (capability.id === 'business-analysis' || capability.id.includes('analysis') || capability.id.includes('investment')) {
                    setActiveTab('analysis');
                  }
                }}
                showHeader={true}
                compact={false}
              />
            </RoleBasedAIAccess>

            {/* User Role and Limits Summary */}
            <div className="glass-light rounded-xl p-6 border border-glass-border">
              <RTLText as="h3" className="text-lg font-semibold text-white mb-4">
                {t('ai.access.currentAccess', 'Your Current AI Access')}
              </RTLText>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <RTLText as="div" className="text-2xl font-bold text-purple-400">
                    {userRoles.length}
                  </RTLText>
                  <RTLText as="div" className="text-sm text-glass-text-secondary">
                    {t('ai.access.activeRoles', 'Active Roles')}
                  </RTLText>
                </div>
                <div className="text-center">
                  <RTLText as="div" className="text-2xl font-bold text-blue-400">
                    {userCapabilities.length}
                  </RTLText>
                  <RTLText as="div" className="text-sm text-glass-text-secondary">
                    {t('ai.access.aiFeatures', 'AI Features')}
                  </RTLText>
                </div>
                <div className="text-center">
                  <RTLText as="div" className="text-2xl font-bold text-green-400">
                    {rateLimits.chat}
                  </RTLText>
                  <RTLText as="div" className="text-sm text-glass-text-secondary">
                    {t('ai.access.chatLimit', 'Chat Limit/Hour')}
                  </RTLText>
                </div>
              </div>
            </div>
          </div>
        );

      case 'chat':
        return (
          <RoleBasedAIAccess
            requiredCapability="basic_chat"
            showUpgradePrompt={true}
          >
            <ConsolidatedAIChat
              businessIdeaId={businessIdeaId}
              businessContext={businessIdea}
              userId={user?.id}
              userName={user?.first_name || user?.username}
              language={language}
              className="h-full"
            />
          </RoleBasedAIAccess>
        );

      case 'analysis':
        if (!businessIdeaId) {
          if (loadingBusinessIdeas) {
            return (
              <div className="flex items-center justify-center h-full">
                <div className="text-center p-8">
                  <div className="p-6 bg-purple-500/10 rounded-2xl inline-block mb-6">
                    <BarChart3 size={64} className="text-purple-400 animate-pulse" />
                  </div>
                  <RTLText as="h3" className="text-xl font-semibold text-white mb-3">
                    {t('common.loading', 'Loading...')}
                  </RTLText>
                  <RTLText as="p" className="text-gray-400">
                    {t('ai.analysis.loadingBusinessIdeas', 'Loading your business ideas...')}
                  </RTLText>
                </div>
              </div>
            );
          }

          if (availableBusinessIdeas.length === 0) {
            return (
              <div className="flex items-center justify-center h-full">
                <div className="text-center p-8">
                  <div className="p-6 bg-purple-500/10 rounded-2xl inline-block mb-6">
                    <BarChart3 size={64} className="text-purple-400" />
                  </div>
                  <RTLText as="h3" className="text-xl font-semibold text-white mb-3">
                    {t('ai.analysis.businessIdeaRequired', 'Business Idea Required')}
                  </RTLText>
                  <RTLText as="p" className="text-gray-400 mb-6 max-w-md">
                    {t('ai.analysis.selectBusinessIdea', 'Please select a business idea to access AI-powered analysis and insights')}
                  </RTLText>
                  <button
                    onClick={() => navigate('/dashboard/business-ideas')}
                    className="px-6 py-3 bg-purple-600/20 hover:bg-purple-600/30 border border-purple-500/30 text-purple-300 rounded-xl transition-all duration-300 hover:scale-105"
                  >
                    {t('ai.analysis.browseBusinessIdeas', 'Browse Business Ideas')}
                  </button>
                </div>
              </div>
            );
          }

          // Show business idea selection
          return (
            <div className="p-6">
              <div className="text-center mb-8">
                <div className="p-6 bg-purple-500/10 rounded-2xl inline-block mb-6">
                  <BarChart3 size={64} className="text-purple-400" />
                </div>
                <RTLText as="h3" className="text-xl font-semibold text-white mb-3">
                  {t('ai.analysis.selectBusinessIdea', 'Select Business Idea for Analysis')}
                </RTLText>
                <RTLText as="p" className="text-gray-400 mb-6">
                  {t('ai.analysis.chooseIdeaDescription', 'Choose one of your business ideas to get AI-powered analysis and insights')}
                </RTLText>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
                {availableBusinessIdeas.map((idea) => (
                  <button
                    key={idea.id}
                    onClick={() => navigate(`/chat/enhancedBusiness/${idea.id}`)}
                    className="text-left p-6 rounded-xl border border-purple-500/30 bg-purple-500/10 hover:bg-purple-500/20 transition-all duration-300 hover:scale-105 hover:border-purple-400/50"
                  >
                    <RTLText as="h4" className="font-semibold text-white mb-2">
                      {idea.title}
                    </RTLText>
                    <RTLText as="p" className="text-gray-400 text-sm mb-3">
                      {idea.description}
                    </RTLText>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-purple-300 bg-purple-500/20 px-2 py-1 rounded">
                        {idea.current_stage || 'Development'}
                      </span>
                      <span className="text-purple-400 text-sm">
                        {t('ai.analysis.analyzeIdea', 'Analyze →')}
                      </span>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          );
        }

        return (
          <RoleBasedAIAccess
            requiredCapability="basic_analysis"
            showUpgradePrompt={true}
          >
            <ConsolidatedBusinessAnalysis
              businessIdeaId={businessIdeaId}
              businessIdea={businessIdea}
              language={language}
              className="h-full"
            />
          </RoleBasedAIAccess>
        );

      case 'status':
        // Double security check - only super admins can access AI status
        if (!isUserSuperAdmin) {
          return (
            <div className="flex flex-col items-center justify-center h-full text-center">
              <Activity className="w-16 h-16 text-red-400 mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">
                {t('ai.status.accessDenied', 'Access Denied')}
              </h3>
              <p className="text-gray-300 max-w-md">
                {t('ai.status.superAdminOnly', 'AI system status monitoring is only available to super administrators for security reasons.')}
              </p>
            </div>
          );
        }
        return <ConsolidatedAIStatus className="h-full" />;

      case 'settings':
        return (
          <div className="space-y-6">
            <div className="flex items-center mb-6">
              <div className="p-3 bg-purple-500/20 rounded-xl mr-4">
                <Settings className="w-6 h-6 text-purple-400" />
              </div>
              <div>
                <RTLText as="h3" className="text-xl font-semibold text-white">
                  {t('admin.chat.aiSettings', 'AI Settings')}
                </RTLText>
                <RTLText as="p" className="text-gray-400">
                  {t('admin.chat.configureAI', 'Configure your AI assistant preferences')}
                </RTLText>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Language Settings */}
              <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                <RTLText as="h4" className="text-lg font-medium text-white mb-4">
                  {t('admin.chat.languageSettings', 'Language Settings')}
                </RTLText>
                <div>
                  <RTLText as="label" className="block text-sm font-medium mb-3 text-gray-300">
                    {t('admin.chat.preferredLanguage', 'Preferred Language')}
                  </RTLText>
                  <select
                    value={language}
                    onChange={(e) => setLanguage(e.target.value)}
                    className="w-full p-4 bg-white/10 border border-white/20 rounded-xl text-white focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-300"
                  >
                    <option value="auto" className="bg-gray-800">
                      {t('admin.chat.autoDetect', 'Auto-detect language')}
                    </option>
                    <option value="en" className="bg-gray-800">English</option>
                    <option value="ar" className="bg-gray-800">العربية (Arabic)</option>
                  </select>
                  <RTLText as="p" className="text-xs text-gray-400 mt-2">
                    {t('admin.chat.languageNote', 'AI will automatically detect and respond in the appropriate language')}
                  </RTLText>
                </div>
              </div>

              {/* Service Management */}
              <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
                <RTLText as="h4" className="text-lg font-medium text-white mb-4">
                  {t('admin.chat.serviceManagement', 'Service Management')}
                </RTLText>
                <div className="space-y-4">
                  <button
                    onClick={refreshStatus}
                    className="w-full px-6 py-3 bg-purple-600/20 hover:bg-purple-600/30 border border-purple-500/30 text-purple-300 rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-purple-500/20"
                  >
                    <div className={`flex items-center justify-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <Sparkles size={16} className="mr-2" />
                      {t('admin.chat.refreshStatus', 'Refresh AI Status')}
                    </div>
                  </button>
                  <RTLText as="p" className="text-xs text-gray-400">
                    {t('admin.chat.refreshNote', 'Check AI service availability and update capabilities')}
                  </RTLText>
                </div>
              </div>
            </div>

            {/* Enhanced Capabilities Overview */}
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6">
              <RTLText as="h4" className="text-lg font-medium text-white mb-4">
                {t('admin.chat.availableCapabilities', 'Available Capabilities')}
              </RTLText>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {capabilities.map((capability, index) => (
                  <div
                    key={index}
                    className={`p-4 rounded-xl border transition-all duration-300 ${
                      capability.available
                        ? 'bg-green-500/10 border-green-500/30 hover:bg-green-500/20'
                        : 'bg-gray-500/10 border-gray-500/30'
                    }`}
                  >
                    <div className={`flex items-center ${isRTL ? 'space-x-reverse space-x-3' : 'space-x-3'} mb-3`}>
                      <div className={`p-2 rounded-lg ${
                        capability.available ? 'bg-green-500/20' : 'bg-gray-500/20'
                      }`}>
                        <div className={`${
                          capability.available ? 'text-green-400' : 'text-gray-400'
                        }`}>
                          {capability.icon}
                        </div>
                      </div>
                      <div className="flex-1">
                        <RTLText as="div" className="font-semibold text-sm text-white">
                          {capability.name}
                        </RTLText>
                        <div className={`px-2 py-1 rounded-full text-xs mt-1 ${
                          capability.available
                            ? 'bg-green-500/20 text-green-400'
                            : 'bg-gray-500/20 text-gray-400'
                        }`}>
                          {capability.available ? t('common.available', 'Available') : t('common.unavailable', 'Unavailable')}
                        </div>
                      </div>
                      <div className={`w-3 h-3 rounded-full ${
                        capability.available ? 'bg-green-400 animate-pulse' : 'bg-gray-400'
                      }`} />
                    </div>
                    <RTLText as="div" className="text-xs text-gray-400">
                      {capability.description}
                    </RTLText>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );

      case 'access':
        return <AICapabilitiesDisplay />;

      default:
        return null;
    }
  };

  return (
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            {/* Enhanced Header with Vibrant Gradient */}
      <div className="bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 backdrop-blur-sm border border-purple-500/30 rounded-2xl p-6 mb-8 shadow-2xl shadow-purple-500/20">
        <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center ${isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>
            {businessIdeaId && (
              <button
                onClick={() => navigate(-1)}
                className="p-2 bg-white/10 hover:bg-white/20 rounded-lg text-gray-400 hover:text-gray-300 transition-all duration-300"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
            )}

            <div className={`flex items-center ${isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>
              <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-2xl shadow-2xl shadow-purple-500/50 animate-pulse">
                <Bot className="w-8 h-8 text-white" />
              </div>
              <div>
                <RTLText as="h1" className="text-3xl font-bold text-white mb-2">
                  {t('admin.yasmeenAI', 'Yasmeen AI Assistant')} 🤖
                </RTLText>
                <RTLText as="p" className="text-gray-300 text-lg">
                  {businessIdea
                    ? businessIdea.title
                    : t('ai.chat.aiDescription', 'Your intelligent AI assistant for business insights and support')
                  }
                </RTLText>
              </div>
            </div>
          </div>

          {/* Enhanced Status indicator */}
          <div className="flex flex-col items-end">
            <div className={`flex items-center ${isRTL ? 'space-x-reverse space-x-3' : 'space-x-3'} mb-2`}>
              <div className={`w-3 h-3 rounded-full animate-pulse ${
                availability.consolidatedAI ? 'bg-green-400' : 'bg-red-400'
              }`} />
              <RTLText as="span" className={`text-sm font-medium ${
                availability.consolidatedAI ? 'text-green-400' : 'text-red-400'
              }`}>
                {availability.consolidatedAI
                  ? t('admin.chat.aiOnline', 'AI Online')
                  : t('admin.chat.aiOffline', 'AI Offline')
                }
              </RTLText>
            </div>
            <RTLText as="div" className="text-xs text-gray-400">
              {t('admin.chat.lastUpdated', 'Last updated')}: {new Date().toLocaleTimeString()}
            </RTLText>
          </div>
        </div>

        {/* AI Capabilities Overview */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
          {capabilities.map((capability, index) => (
            <div
              key={index}
              className={`p-4 rounded-xl border transition-all duration-300 ${
                capability.available
                  ? 'bg-green-500/10 border-green-500/30 hover:bg-green-500/20'
                  : 'bg-gray-500/10 border-gray-500/30'
              }`}
            >
              <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''} mb-2`}>
                <div className={`p-2 rounded-lg ${
                  capability.available ? 'bg-green-500/20' : 'bg-gray-500/20'
                } ${isRTL ? 'ml-3' : 'mr-3'}`}>
                  <div className={`${
                    capability.available ? 'text-green-400' : 'text-gray-400'
                  }`}>
                    {capability.icon}
                  </div>
                </div>
                <div className="flex-1">
                  <RTLText as="div" className="font-medium text-sm text-white">
                    {capability.name}
                  </RTLText>
                </div>
                <div className={`w-2 h-2 rounded-full ${
                  capability.available ? 'bg-green-400' : 'bg-gray-400'
                }`} />
              </div>
              <RTLText as="div" className="text-xs text-gray-400">
                {capability.description}
              </RTLText>
            </div>
          ))}
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="mb-6">
        <div className="flex flex-wrap gap-2">
          {tabs.map((tab) => {
            const isActive = activeTab === tab.id;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                disabled={!tab.available}
                className={`flex items-center px-4 py-2 rounded-lg transition-all duration-300 ${
                  isActive
                    ? 'bg-purple-600/30 border border-purple-500/50 text-white shadow-lg'
                    : tab.available
                    ? 'text-gray-300 hover:bg-white/10 hover:text-white border border-transparent hover:border-white/20'
                    : 'text-gray-500 cursor-not-allowed opacity-50 border border-gray-700/30'
                } ${isRTL ? 'space-x-reverse space-x-2' : 'space-x-2'}`}
              >
                <div className={`${
                  isActive ? 'text-purple-300' : tab.available ? 'text-gray-400' : 'text-gray-500'
                }`}>
                  {tab.icon}
                </div>
                <span className="text-sm font-medium">{tab.name}</span>
                {!tab.available && (
                  <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse" />
                )}
              </button>
            );
          })}
        </div>
      </div>

      {/* Main Content Area */}
      <div className="bg-gradient-to-br from-gray-800/30 to-gray-900/30 backdrop-blur-sm border border-purple-500/20 rounded-2xl p-6 min-h-[600px] shadow-xl">
        {renderTabContent()}
      </div>
          </div>
        </div>
      </div>
  );
};

export default ConsolidatedAIPage;
