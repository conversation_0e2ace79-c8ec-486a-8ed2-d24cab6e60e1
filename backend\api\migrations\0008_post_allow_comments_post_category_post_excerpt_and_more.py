# Generated by Django 5.2.4 on 2025-07-17 08:07

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("api", "0007_alter_comment_author_alter_post_author_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="post",
            name="allow_comments",
            field=models.BooleanField(
                default=True, help_text="Allow users to comment on this post"
            ),
        ),
        migrations.AddField(
            model_name="post",
            name="category",
            field=models.CharField(
                choices=[
                    ("general", "General"),
                    ("business", "Business"),
                    ("technology", "Technology"),
                    ("marketing", "Marketing"),
                    ("finance", "Finance"),
                    ("legal", "Legal"),
                    ("operations", "Operations"),
                    ("product_development", "Product Development"),
                    ("strategy", "Strategy"),
                    ("networking", "Networking"),
                ],
                default="general",
                max_length=30,
            ),
        ),
        migrations.AddField(
            model_name="post",
            name="excerpt",
            field=models.Text<PERSON>ield(
                default="", help_text="Brief summary of the post", max_length=500
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="post",
            name="featured_image",
            field=models.URLField(
                blank=True, help_text="URL to featured image", null=True
            ),
        ),
        migrations.AddField(
            model_name="post",
            name="is_featured",
            field=models.BooleanField(
                default=False, help_text="Featured posts appear prominently"
            ),
        ),
        migrations.AddField(
            model_name="post",
            name="is_published",
            field=models.BooleanField(
                default=True, help_text="Only published posts are visible to users"
            ),
        ),
        migrations.AddField(
            model_name="post",
            name="post_type",
            field=models.CharField(
                choices=[
                    ("article", "Article"),
                    ("discussion", "Discussion"),
                    ("question", "Question"),
                    ("announcement", "Announcement"),
                    ("tutorial", "Tutorial"),
                    ("news", "News"),
                ],
                default="article",
                max_length=20,
            ),
        ),
    ]
