describe('Application Functionality Tests', () => {
  beforeEach(() => {
    cy.visit('/')
    cy.wait(2000) // Give the app time to load
  })

  it('should test all available routes', () => {
    // Common routes to test
    const routes = [
      '/',
      '/login',
      '/dashboard',
      '/admin',
      '/admin/dashboard',
      '/superadmin',
      '/mentor',
      '/business-plans',
      '/mentorship',
      '/funding',
      '/profile',
      '/settings'
    ]

    routes.forEach((route) => {
      cy.visit(route, { failOnStatusCode: false })
      
      // Check that we get some response (not a blank page)
      cy.get('body').should('not.be.empty')
      
      // Log what we found
      cy.url().then((currentUrl) => {
        cy.log(`Route ${route} -> ${currentUrl}`)
      })
      
      // Check for common error indicators
      cy.get('body').then(($body) => {
        const bodyText = $body.text().toLowerCase()
        
        if (bodyText.includes('404') || bodyText.includes('not found')) {
          cy.log(`Route ${route} shows 404`)
        } else if (bodyText.includes('error')) {
          cy.log(`Route ${route} shows error`)
        } else if (bodyText.includes('login') || bodyText.includes('sign in')) {
          cy.log(`Route ${route} shows login page`)
        } else if (bodyText.includes('dashboard')) {
          cy.log(`Route ${route} shows dashboard`)
        } else {
          cy.log(`Route ${route} shows content`)
        }
      })
    })
  })

  it('should test form interactions', () => {
    cy.get('body').then(($body) => {
      const forms = $body.find('form')
      
      if (forms.length > 0) {
        cy.log(`Found ${forms.length} forms`)
        
        // Test each form
        forms.each((index, form) => {
          const $form = Cypress.$(form)
          const inputs = $form.find('input, textarea, select')
          
          if (inputs.length > 0) {
            cy.log(`Form ${index + 1} has ${inputs.length} inputs`)
            
            // Test form inputs
            inputs.each((inputIndex, input) => {
              const $input = Cypress.$(input)
              const type = $input.attr('type') || 'text'
              const name = $input.attr('name') || `input-${inputIndex}`
              
              if (type === 'email') {
                cy.wrap(input).clear().type('<EMAIL>')
              } else if (type === 'password') {
                cy.wrap(input).clear().type('testpassword123')
              } else if (type === 'text' || type === 'textarea') {
                cy.wrap(input).clear().type('Test input')
              }
              
              cy.log(`Tested ${type} input: ${name}`)
            })
          }
        })
      } else {
        cy.log('No forms found on this page')
      }
    })
  })

  it('should test navigation and routing', () => {
    // Look for navigation links
    cy.get('a[href^="/"]').then(($links) => {
      if ($links.length > 0) {
        cy.log(`Found ${$links.length} internal links`)
        
        // Test first few navigation links
        const linksToTest = Math.min($links.length, 5)
        
        for (let i = 0; i < linksToTest; i++) {
          const href = $links.eq(i).attr('href')
          const text = $links.eq(i).text().trim()
          
          if (href && href !== '#') {
            cy.log(`Testing link: "${text}" -> ${href}`)
            
            // Click the link
            cy.wrap($links.eq(i)).click()
            
            // Wait for navigation
            cy.wait(1000)
            
            // Check that we navigated
            cy.url().should('include', href)
            
            // Go back to home
            cy.visit('/')
            cy.wait(1000)
          }
        }
      } else {
        cy.log('No internal navigation links found')
      }
    })
  })

  it('should test button interactions', () => {
    cy.get('button').then(($buttons) => {
      if ($buttons.length > 0) {
        cy.log(`Found ${$buttons.length} buttons`)
        
        // Test each button (but don't actually click submit buttons)
        $buttons.each((index, button) => {
          const $button = Cypress.$(button)
          const text = $button.text().trim()
          const type = $button.attr('type')
          const disabled = $button.prop('disabled')
          
          cy.log(`Button ${index + 1}: "${text}" (type: ${type}, disabled: ${disabled})`)
          
          // Only test non-submit buttons that aren't disabled
          if (type !== 'submit' && !disabled && !text.toLowerCase().includes('delete')) {
            cy.wrap(button).should('be.visible')
            
            // Test hover state
            cy.wrap(button).trigger('mouseover')
            cy.wrap(button).trigger('mouseout')
          }
        })
      } else {
        cy.log('No buttons found on this page')
      }
    })
  })

  it('should test data loading and display', () => {
    // Look for loading indicators
    cy.get('.loading, .spinner, [data-loading="true"]').then(($loaders) => {
      if ($loaders.length > 0) {
        cy.log('Loading indicators found')
        
        // Wait for loading to complete
        cy.get('.loading, .spinner, [data-loading="true"]', { timeout: 15000 })
          .should('not.exist')
      }
    })
    
    // Look for data tables or lists
    cy.get('table, .table, ul, ol, .list').then(($dataElements) => {
      if ($dataElements.length > 0) {
        cy.log(`Found ${$dataElements.length} data display elements`)
        
        // Check that data elements are visible
        cy.wrap($dataElements).first().should('be.visible')
      }
    })
    
    // Look for cards or content blocks
    cy.get('.card, .content, .item, [class*="card"]').then(($cards) => {
      if ($cards.length > 0) {
        cy.log(`Found ${$cards.length} content cards/blocks`)
      }
    })
  })

  it('should test error handling', () => {
    // Look for error messages or alerts
    cy.get('.error, .alert, .warning, [role="alert"]').then(($errors) => {
      if ($errors.length > 0) {
        cy.log(`Found ${$errors.length} error/alert elements`)
        
        // Check that error elements are properly styled
        cy.wrap($errors).each(($error) => {
          cy.wrap($error).should('be.visible')
        })
      } else {
        cy.log('No error elements found (this is good!)')
      }
    })
    
    // Test 404 page
    cy.visit('/non-existent-page', { failOnStatusCode: false })
    cy.get('body').should('not.be.empty')
    
    // Go back to home
    cy.visit('/')
  })

  it('should test mobile responsiveness', () => {
    // Test different mobile viewports
    const viewports = [
      { width: 320, height: 568, name: 'iPhone SE' },
      { width: 375, height: 812, name: 'iPhone X' },
      { width: 768, height: 1024, name: 'iPad' }
    ]
    
    viewports.forEach((viewport) => {
      cy.viewport(viewport.width, viewport.height)
      cy.log(`Testing ${viewport.name} (${viewport.width}x${viewport.height})`)
      
      // Check that content is still visible
      cy.get('body').should('be.visible')
      
      // Check for mobile menu if it exists
      if (viewport.width < 768) {
        cy.get('.mobile-menu, .hamburger, [data-mobile-menu]').then(($mobileMenu) => {
          if ($mobileMenu.length > 0) {
            cy.log('Mobile menu found')
            cy.wrap($mobileMenu).should('be.visible')
          }
        })
      }
    })
    
    // Reset to desktop
    cy.viewport(1280, 720)
  })

  it('should test search functionality if it exists', () => {
    // Look for search inputs
    cy.get('input[type="search"], input[placeholder*="search"], input[name*="search"]').then(($searchInputs) => {
      if ($searchInputs.length > 0) {
        cy.log('Search functionality found')
        
        const searchInput = $searchInputs.first()
        cy.wrap(searchInput).should('be.visible')
        
        // Test search input
        cy.wrap(searchInput).clear().type('test search')
        
        // Look for search button
        cy.get('button[type="submit"], button:contains("Search")').then(($searchButtons) => {
          if ($searchButtons.length > 0) {
            cy.log('Search button found')
            // Don't actually submit to avoid side effects
          }
        })
        
        // Clear search
        cy.wrap(searchInput).clear()
      } else {
        cy.log('No search functionality found')
      }
    })
  })
})
