/**
 * RBAC Unit Tests
 * Unit tests for the unified role manager functions
 */

import {
  getUserRole,
  getUserRoles,
  hasRole,
  hasAnyRole,
  hasPermission,
  hasAnyPermission,
  canAccessRoute,
  isSuperAdmin,
  isAdmin,
  getDashboardRoute,
  UserRole,
  PermissionLevel
} from '../../utils/unifiedRoleManager';
import { User } from '../../services/api';

// Mock test users
const createTestUser = (overrides: Partial<User> = {}): User => ({
  id: 1,
  username: 'testuser',
  email: '<EMAIL>',
  first_name: 'Test',
  last_name: 'User',
  is_admin: false,
  is_staff: false,
  is_superuser: false,
  ...overrides
});

const testUsers = {
  superAdmin: createTestUser({
    id: 1,
    username: 'superadmin',
    is_superuser: true,
    user_role: 'super_admin',
    role_permissions: ['read', 'write', 'moderate', 'admin', 'super_admin']
  }),
  
  admin: createTestUser({
    id: 2,
    username: 'admin',
    is_staff: true,
    user_role: 'admin',
    role_permissions: ['read', 'write', 'moderate', 'admin']
  }),
  
  moderator: createTestUser({
    id: 3,
    username: 'moderator',
    user_role: 'moderator',
    role_permissions: ['read', 'write', 'moderate'],
    profile: {
      role: 'moderator',
      primary_role: { name: 'moderator', permission_level: 'moderate', display_name: 'Moderator' },
      active_roles: [{ name: 'moderator', permission_level: 'moderate', display_name: 'Moderator' }]
    }
  }),
  
  mentor: createTestUser({
    id: 4,
    username: 'mentor',
    user_role: 'mentor',
    role_permissions: ['read', 'write'],
    profile: {
      role: 'mentor',
      primary_role: { name: 'mentor', permission_level: 'write', display_name: 'Mentor' },
      active_roles: [{ name: 'mentor', permission_level: 'write', display_name: 'Mentor' }]
    }
  }),
  
  investor: createTestUser({
    id: 5,
    username: 'investor',
    user_role: 'investor',
    role_permissions: ['read', 'write'],
    profile: {
      role: 'investor',
      primary_role: { name: 'investor', permission_level: 'write', display_name: 'Investor' },
      active_roles: [{ name: 'investor', permission_level: 'write', display_name: 'Investor' }]
    }
  }),
  
  user: createTestUser({
    id: 6,
    username: 'regularuser',
    user_role: 'user',
    role_permissions: ['read']
  })
};

describe('RBAC Unit Tests', () => {
  
  describe('getUserRole', () => {
    test('should return correct role for super admin', () => {
      expect(getUserRole(testUsers.superAdmin)).toBe('super_admin');
    });
    
    test('should return correct role for admin', () => {
      expect(getUserRole(testUsers.admin)).toBe('admin');
    });
    
    test('should return correct role for moderator', () => {
      expect(getUserRole(testUsers.moderator)).toBe('moderator');
    });
    
    test('should return correct role for mentor', () => {
      expect(getUserRole(testUsers.mentor)).toBe('mentor');
    });
    
    test('should return correct role for investor', () => {
      expect(getUserRole(testUsers.investor)).toBe('investor');
    });
    
    test('should return correct role for regular user', () => {
      expect(getUserRole(testUsers.user)).toBe('user');
    });
    
    test('should return user for null user', () => {
      expect(getUserRole(null)).toBe('user');
    });
  });

  describe('hasRole', () => {
    test('should correctly identify super admin role', () => {
      expect(hasRole(testUsers.superAdmin, 'super_admin')).toBe(true);
      expect(hasRole(testUsers.superAdmin, 'admin')).toBe(false);
    });
    
    test('should correctly identify admin role', () => {
      expect(hasRole(testUsers.admin, 'admin')).toBe(true);
      expect(hasRole(testUsers.admin, 'super_admin')).toBe(false);
    });
    
    test('should correctly identify specialized roles', () => {
      expect(hasRole(testUsers.moderator, 'moderator')).toBe(true);
      expect(hasRole(testUsers.mentor, 'mentor')).toBe(true);
      expect(hasRole(testUsers.investor, 'investor')).toBe(true);
    });
  });

  describe('hasAnyRole', () => {
    test('should return true if user has any of the specified roles', () => {
      expect(hasAnyRole(testUsers.admin, ['admin', 'super_admin'])).toBe(true);
      expect(hasAnyRole(testUsers.mentor, ['mentor', 'investor'])).toBe(true);
    });
    
    test('should return false if user has none of the specified roles', () => {
      expect(hasAnyRole(testUsers.user, ['admin', 'super_admin'])).toBe(false);
      expect(hasAnyRole(testUsers.mentor, ['admin', 'moderator'])).toBe(false);
    });
  });

  describe('isSuperAdmin', () => {
    test('should return true for super admin', () => {
      expect(isSuperAdmin(testUsers.superAdmin)).toBe(true);
    });
    
    test('should return false for non-super admin users', () => {
      expect(isSuperAdmin(testUsers.admin)).toBe(false);
      expect(isSuperAdmin(testUsers.user)).toBe(false);
    });
  });

  describe('isAdmin', () => {
    test('should return true for admin and super admin', () => {
      expect(isAdmin(testUsers.superAdmin)).toBe(true);
      expect(isAdmin(testUsers.admin)).toBe(true);
    });
    
    test('should return false for non-admin users', () => {
      expect(isAdmin(testUsers.moderator)).toBe(false);
      expect(isAdmin(testUsers.user)).toBe(false);
    });
  });

  describe('getDashboardRoute', () => {
    test('should return correct dashboard routes', () => {
      expect(getDashboardRoute(testUsers.superAdmin)).toBe('/super_admin');
      expect(getDashboardRoute(testUsers.admin)).toBe('/admin');
      expect(getDashboardRoute(testUsers.moderator)).toBe('/dashboard/moderator');
      expect(getDashboardRoute(testUsers.mentor)).toBe('/dashboard/mentor');
      expect(getDashboardRoute(testUsers.investor)).toBe('/dashboard/investor');
      expect(getDashboardRoute(testUsers.user)).toBe('/dashboard');
    });
  });

  describe('canAccessRoute', () => {
    test('super admin can access all routes', () => {
      expect(canAccessRoute(testUsers.superAdmin, ['admin'])).toBe(true);
      expect(canAccessRoute(testUsers.superAdmin, ['moderator'])).toBe(true);
      expect(canAccessRoute(testUsers.superAdmin, ['user'])).toBe(true);
    });
    
    test('admin can access admin routes but not super admin', () => {
      expect(canAccessRoute(testUsers.admin, ['admin'])).toBe(true);
      expect(canAccessRoute(testUsers.admin, ['super_admin'])).toBe(false);
    });
    
    test('specialized roles can only access their own routes', () => {
      expect(canAccessRoute(testUsers.moderator, ['moderator'])).toBe(true);
      expect(canAccessRoute(testUsers.moderator, ['admin'])).toBe(false);
      
      expect(canAccessRoute(testUsers.mentor, ['mentor'])).toBe(true);
      expect(canAccessRoute(testUsers.mentor, ['admin'])).toBe(false);
      
      expect(canAccessRoute(testUsers.investor, ['investor'])).toBe(true);
      expect(canAccessRoute(testUsers.investor, ['admin'])).toBe(false);
    });
    
    test('regular user can only access user routes', () => {
      expect(canAccessRoute(testUsers.user, ['user'])).toBe(true);
      expect(canAccessRoute(testUsers.user, ['admin'])).toBe(false);
      expect(canAccessRoute(testUsers.user, ['moderator'])).toBe(false);
    });
  });

  describe('Role Hierarchy', () => {
    test('should maintain proper role hierarchy', () => {
      const roles = ['user', 'investor', 'mentor', 'moderator', 'admin', 'super_admin'];
      
      // Super admin should have highest access
      expect(isSuperAdmin(testUsers.superAdmin)).toBe(true);
      expect(isAdmin(testUsers.superAdmin)).toBe(true);
      
      // Admin should have admin access but not super admin
      expect(isSuperAdmin(testUsers.admin)).toBe(false);
      expect(isAdmin(testUsers.admin)).toBe(true);
      
      // Specialized roles should not have admin access
      expect(isAdmin(testUsers.moderator)).toBe(false);
      expect(isAdmin(testUsers.mentor)).toBe(false);
      expect(isAdmin(testUsers.investor)).toBe(false);
      expect(isAdmin(testUsers.user)).toBe(false);
    });
  });

  describe('Edge Cases', () => {
    test('should handle null user gracefully', () => {
      expect(getUserRole(null)).toBe('user');
      expect(hasRole(null, 'admin')).toBe(false);
      expect(hasAnyRole(null, ['admin', 'user'])).toBe(false);
      expect(isSuperAdmin(null)).toBe(false);
      expect(isAdmin(null)).toBe(false);
      expect(canAccessRoute(null, ['user'])).toBe(false);
    });
    
    test('should handle user without role data', () => {
      const userWithoutRole = createTestUser({ user_role: undefined, role_permissions: undefined });
      expect(getUserRole(userWithoutRole)).toBe('user');
    });
  });
});
