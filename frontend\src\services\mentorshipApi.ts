import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Types
export interface Mentee {
  id: number;
  user: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
  };
  name?: string; // Computed field
  email?: string; // Computed field
  phone?: string;
  business_idea?: string;
  progress: number;
  sessions_completed: number;
  next_session?: string;
  status: 'active' | 'inactive' | 'completed';
  joined_date: string;
  goals: string[];
  rating: number;
  notes?: string;
}

export interface MentorshipSession {
  id: number;
  mentorship_match: number;
  mentee_name?: string; // Computed field
  title: string;
  description?: string;
  scheduled_at: string;
  duration_minutes: number;
  status: 'scheduled' | 'completed' | 'cancelled' | 'rescheduled' | 'in_progress';
  session_type: 'video' | 'phone' | 'in_person' | 'chat';
  location?: string;
  meeting_link?: string;
  notes?: string;
  rating?: number;
  feedback?: string;
  created_at: string;
  updated_at: string;
}

export interface BusinessIdea {
  id: number;
  title: string;
  description: string;
  problem_statement: string;
  solution_description: string;
  target_audience: string;
  market_opportunity?: string;
  business_model?: string;
  current_stage: 'concept' | 'validation' | 'development' | 'scaling' | 'established';
  category?: string;
  estimated_revenue?: number;
  required_funding?: number;
  time_to_market?: number;
  risk_level?: 'low' | 'medium' | 'high';
  status: 'draft' | 'active' | 'paused' | 'completed';
  tags: string[];
  owner: number;
  created_at: string;
  updated_at: string;
}

// API Functions

// Mentees API
export const menteesApi = {
  // Get all mentees for current mentor
  getAll: async (): Promise<Mentee[]> => {
    const response = await api.get('/incubator/mentorship-matches/');
    return response.data.results.map((match: any) => ({
      id: match.id,
      user: match.mentee,
      name: `${match.mentee.first_name} ${match.mentee.last_name}`,
      email: match.mentee.email,
      phone: match.mentee_profile?.phone || '',
      business_idea: match.business_idea?.title || '',
      progress: match.progress_percentage || 0,
      sessions_completed: match.sessions_completed || 0,
      next_session: match.next_session_date,
      status: match.status === 'active' ? 'active' : match.status === 'completed' ? 'completed' : 'inactive',
      joined_date: match.created_at,
      goals: match.goals ? match.goals.split(',').map((g: string) => g.trim()) : [],
      rating: match.average_rating || 0,
      notes: match.notes || ''
    }));
  },

  // Create new mentee (through mentorship match)
  create: async (data: Partial<Mentee>): Promise<Mentee> => {
    const response = await api.post('/incubator/mentorship-matches/', {
      mentee_email: data.email,
      business_idea_title: data.business_idea,
      goals: Array.isArray(data.goals) ? data.goals.join(', ') : data.goals,
      notes: data.notes
    });
    return response.data;
  },

  // Update mentee
  update: async (id: number, data: Partial<Mentee>): Promise<Mentee> => {
    const response = await api.patch(`/incubator/mentorship-matches/${id}/`, {
      goals: Array.isArray(data.goals) ? data.goals.join(', ') : data.goals,
      notes: data.notes,
      status: data.status
    });
    return response.data;
  },

  // Delete mentee
  delete: async (id: number): Promise<void> => {
    await api.delete(`/incubator/mentorship-matches/${id}/`);
  }
};

// Sessions API
export const sessionsApi = {
  // Get all sessions for current mentor
  getAll: async (): Promise<MentorshipSession[]> => {
    const response = await api.get('/incubator/mentorship-sessions/');
    return response.data.results.map((session: any) => ({
      id: session.id,
      mentorship_match: session.mentorship_match,
      mentee_name: session.mentorship_match_details?.mentee_name || 'Unknown',
      title: session.title,
      description: session.description || '',
      scheduled_at: session.scheduled_at,
      duration_minutes: session.duration_minutes,
      status: session.status,
      session_type: session.session_type,
      location: session.location || '',
      meeting_link: session.meeting_link || '',
      notes: session.notes || '',
      rating: session.rating,
      feedback: session.feedback || '',
      created_at: session.created_at,
      updated_at: session.updated_at
    }));
  },

  // Create new session
  create: async (data: Partial<MentorshipSession>): Promise<MentorshipSession> => {
    const response = await api.post('/incubator/mentorship-sessions/', {
      mentorship_match: data.mentorship_match,
      title: data.title,
      description: data.description,
      scheduled_at: data.scheduled_at,
      duration_minutes: data.duration_minutes,
      session_type: data.session_type,
      location: data.location,
      notes: data.notes
    });
    return response.data;
  },

  // Update session
  update: async (id: number, data: Partial<MentorshipSession>): Promise<MentorshipSession> => {
    const response = await api.patch(`/incubator/mentorship-sessions/${id}/`, data);
    return response.data;
  },

  // Delete session
  delete: async (id: number): Promise<void> => {
    await api.delete(`/incubator/mentorship-sessions/${id}/`);
  }
};

// Business Ideas API
export const businessIdeasApi = {
  // Get all business ideas
  getAll: async (): Promise<BusinessIdea[]> => {
    const response = await api.get('/incubator/business-ideas/');
    return response.data.results;
  },

  // Create new business idea
  create: async (data: Partial<BusinessIdea>): Promise<BusinessIdea> => {
    const response = await api.post('/incubator/business-ideas/', data);
    return response.data;
  },

  // Update business idea
  update: async (id: number, data: Partial<BusinessIdea>): Promise<BusinessIdea> => {
    const response = await api.patch(`/incubator/business-ideas/${id}/`, data);
    return response.data;
  },

  // Delete business idea
  delete: async (id: number): Promise<void> => {
    await api.delete(`/incubator/business-ideas/${id}/`);
  }
};

// Forums API
export interface ForumPost {
  id: number;
  title: string;
  content: string;
  category: string;
  author: {
    id: number;
    first_name: string;
    last_name: string;
  };
  replies_count: number;
  views_count: number;
  created_at: string;
  updated_at: string;
  is_pinned: boolean;
  tags: string[];
}

export const forumsApi = {
  getAll: async (): Promise<ForumPost[]> => {
    const response = await api.get('/incubator/forum-posts/');
    return response.data.results;
  },

  create: async (data: Partial<ForumPost>): Promise<ForumPost> => {
    const response = await api.post('/incubator/forum-posts/', data);
    return response.data;
  },

  update: async (id: number, data: Partial<ForumPost>): Promise<ForumPost> => {
    const response = await api.patch(`/incubator/forum-posts/${id}/`, data);
    return response.data;
  },

  delete: async (id: number): Promise<void> => {
    await api.delete(`/incubator/forum-posts/${id}/`);
  }
};

// Resources API
export interface Resource {
  id: number;
  title: string;
  description: string;
  resource_type: 'document' | 'video' | 'link' | 'template';
  category: string;
  file_url?: string;
  external_url?: string;
  download_count: number;
  rating: number;
  tags: string[];
  created_at: string;
  updated_at: string;
}

export const resourcesApi = {
  getAll: async (): Promise<Resource[]> => {
    const response = await api.get('/incubator/resources/');
    return response.data.results;
  },

  create: async (data: Partial<Resource>): Promise<Resource> => {
    const response = await api.post('/incubator/resources/', data);
    return response.data;
  },

  update: async (id: number, data: Partial<Resource>): Promise<Resource> => {
    const response = await api.patch(`/incubator/resources/${id}/`, data);
    return response.data;
  },

  delete: async (id: number): Promise<void> => {
    await api.delete(`/incubator/resources/${id}/`);
  }
};

// Dashboard Stats API
export interface DashboardStats {
  total_mentees: number;
  active_sessions: number;
  completed_sessions: number;
  average_rating: number;
  total_hours: number;
  response_rate: number;
  recent_activities: Array<{
    id: number;
    type: string;
    title: string;
    description: string;
    timestamp: string;
    status: string;
  }>;
}

export const dashboardApi = {
  getStats: async (): Promise<DashboardStats> => {
    const response = await api.get('/incubator/dashboard/stats/');
    return response.data;
  }
};

// Export all APIs
export default {
  mentees: menteesApi,
  sessions: sessionsApi,
  businessIdeas: businessIdeasApi,
  forums: forumsApi,
  resources: resourcesApi,
  dashboard: dashboardApi
};
