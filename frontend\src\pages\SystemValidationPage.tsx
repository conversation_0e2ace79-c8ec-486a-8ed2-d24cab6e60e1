/**
 * SYSTEM VALIDATION PAGE
 * A development page to test and validate the role system fixes
 */

import React, { useState, useEffect } from 'react';
import { validateSystem, generateValidationReport, ValidationResult } from '../utils/systemValidation';
import { useAppSelector } from '../store/hooks';
import { getUserRoleDebugInfo } from '../utils/unifiedRoleManager';

const SystemValidationPage: React.FC = () => {
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const { user } = useAppSelector(state => state.auth);

  const runValidation = async () => {
    setIsRunning(true);
    try {
      // Add a small delay to show loading state
      await new Promise(resolve => setTimeout(resolve, 500));
      const result = validateSystem();
      setValidationResult(result);
    } catch (error) {
      console.error('Validation failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  // Get current user debug info
  const userDebugInfo = getUserRoleDebugInfo(user);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-xl shadow-lg p-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              🔧 System Validation Dashboard
            </h1>
            <p className="text-gray-600">
              Test and validate the role system, routing, and navigation fixes
            </p>
          </div>

          {/* Current User Info */}
          <div className="bg-blue-50 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-blue-900 mb-4">Current User Debug Info</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium text-blue-800 mb-2">User Details</h3>
                <pre className="text-sm bg-white p-3 rounded border overflow-x-auto">
{JSON.stringify(userDebugInfo.user, null, 2)}
                </pre>
              </div>
              <div>
                <h3 className="font-medium text-blue-800 mb-2">Role & Permission Info</h3>
                <div className="bg-white p-3 rounded border space-y-2">
                  <div><strong>Roles:</strong> {userDebugInfo.roles.join(', ')}</div>
                  <div><strong>Permissions:</strong> {userDebugInfo.permissions.join(', ')}</div>
                  <div><strong>Highest Role:</strong> {userDebugInfo.highestRole}</div>
                  <div><strong>Highest Permission:</strong> {userDebugInfo.highestPermission}</div>
                  <div><strong>Is Admin:</strong> {userDebugInfo.isAdmin ? '✅' : '❌'}</div>
                  <div><strong>Is Super Admin:</strong> {userDebugInfo.isSuperAdmin ? '✅' : '❌'}</div>
                  <div><strong>Dashboard Route:</strong> {userDebugInfo.dashboardRoute}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Validation Controls */}
          <div className="mb-8">
            <button
              onClick={runValidation}
              disabled={isRunning}
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                isRunning
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-purple-600 hover:bg-purple-700 text-white shadow-lg hover:shadow-xl'
              }`}
            >
              {isRunning ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Running Validation...
                </span>
              ) : (
                '🚀 Run System Validation'
              )}
            </button>
          </div>

          {/* Validation Results */}
          {validationResult && (
            <div className="space-y-6">
              {/* Summary Card */}
              <div className={`rounded-lg p-6 ${
                validationResult.success 
                  ? 'bg-green-50 border border-green-200' 
                  : 'bg-red-50 border border-red-200'
              }`}>
                <div className="flex items-center mb-4">
                  <span className="text-2xl mr-3">
                    {validationResult.success ? '✅' : '❌'}
                  </span>
                  <h2 className={`text-xl font-semibold ${
                    validationResult.success ? 'text-green-900' : 'text-red-900'
                  }`}>
                    Validation {validationResult.success ? 'Passed' : 'Failed'}
                  </h2>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">
                      {validationResult.summary.totalRoutes}
                    </div>
                    <div className="text-sm text-gray-600">Total Routes</div>
                  </div>
                  <div className="text-center">
                    <div className={`text-2xl font-bold ${
                      validationResult.summary.criticalIssues === 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {validationResult.summary.criticalIssues}
                    </div>
                    <div className="text-sm text-gray-600">Critical Issues</div>
                  </div>
                  <div className="text-center">
                    <div className={`text-2xl font-bold ${
                      validationResult.summary.warnings === 0 ? 'text-green-600' : 'text-yellow-600'
                    }`}>
                      {validationResult.summary.warnings}
                    </div>
                    <div className="text-sm text-gray-600">Warnings</div>
                  </div>
                  <div className="text-center">
                    <div className={`text-2xl font-bold ${
                      validationResult.summary.roleConsistency ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {validationResult.summary.roleConsistency ? '✅' : '❌'}
                    </div>
                    <div className="text-sm text-gray-600">Role Consistency</div>
                  </div>
                </div>
              </div>

              {/* Route Access by Role */}
              <div className="bg-white border rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Route Access by Role</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {Object.entries(validationResult.summary.accessibleRoutes).map(([role, count]) => {
                    const percentage = ((count / validationResult.summary.totalRoutes) * 100).toFixed(1);
                    return (
                      <div key={role} className="bg-gray-50 rounded-lg p-4">
                        <div className="font-medium text-gray-900 capitalize">{role}</div>
                        <div className="text-2xl font-bold text-purple-600">{count}</div>
                        <div className="text-sm text-gray-600">{percentage}% of routes</div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Issues List */}
              {validationResult.issues.length > 0 && (
                <div className="bg-white border rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Issues Found ({validationResult.issues.length})
                  </h3>
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {validationResult.issues.map((issue, index) => (
                      <div
                        key={index}
                        className={`p-3 rounded-lg border-l-4 ${
                          issue.type === 'error'
                            ? 'bg-red-50 border-red-400'
                            : issue.type === 'warning'
                            ? 'bg-yellow-50 border-yellow-400'
                            : 'bg-blue-50 border-blue-400'
                        }`}
                      >
                        <div className="flex items-start">
                          <span className="text-sm font-medium uppercase tracking-wide mr-2">
                            {issue.category}
                          </span>
                          <span className={`text-xs px-2 py-1 rounded ${
                            issue.type === 'error'
                              ? 'bg-red-100 text-red-800'
                              : issue.type === 'warning'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-blue-100 text-blue-800'
                          }`}>
                            {issue.type}
                          </span>
                        </div>
                        <div className="mt-1 text-sm text-gray-700">{issue.message}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Full Report */}
              <div className="bg-white border rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Full Validation Report</h3>
                <pre className="text-xs bg-gray-50 p-4 rounded border overflow-x-auto whitespace-pre-wrap">
                  {generateValidationReport(validationResult)}
                </pre>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SystemValidationPage;
