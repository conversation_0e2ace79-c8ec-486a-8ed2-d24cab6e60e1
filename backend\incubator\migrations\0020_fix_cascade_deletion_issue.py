# Generated manually to fix cascade deletion issue

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('incubator', '0019_make_base_template_optional'),
    ]

    operations = [
        migrations.AlterField(
            model_name='custombusinessplantemplate',
            name='base_template',
            field=models.ForeignKey(
                blank=True, 
                null=True, 
                on_delete=django.db.models.deletion.SET_NULL, 
                related_name='custom_templates', 
                to='incubator.businessplantemplate'
            ),
        ),
    ]
