/**
 * RBAC VALIDATION TEST
 * Comprehensive test suite to validate the unified role-based access control system
 */

import { 
  getUserRole, 
  getUserRoles, 
  hasRole, 
  hasAnyRole, 
  canAccessRoute, 
  isSuperAdmin, 
  isAdmin, 
  getDashboardRoute,
  UserRole,
  PermissionLevel
} from './unifiedRoleManager';
import { User } from '../services/api';

// Test user data for different roles
const createTestUser = (overrides: Partial<User> = {}): User => ({
  id: 1,
  username: 'testuser',
  email: '<EMAIL>',
  first_name: 'Test',
  last_name: 'User',
  is_admin: false,
  is_staff: false,
  is_superuser: false,
  ...overrides
});

// Test cases for different user roles
const testUsers = {
  superAdmin: createTestUser({
    id: 1,
    username: 'superadmin',
    is_superuser: true,
    user_role: 'super_admin',
    role_permissions: ['read', 'write', 'moderate', 'admin', 'super_admin']
  }),
  
  admin: createTestUser({
    id: 2,
    username: 'admin',
    is_staff: true,
    user_role: 'admin',
    role_permissions: ['read', 'write', 'moderate', 'admin']
  }),
  
  moderator: createTestUser({
    id: 3,
    username: 'moderator',
    user_role: 'moderator',
    role_permissions: ['read', 'write', 'moderate'],
    profile: {
      role: 'moderator',
      primary_role: { name: 'moderator', permission_level: 'moderate', display_name: 'Moderator' },
      active_roles: [{ name: 'moderator', permission_level: 'moderate', display_name: 'Moderator' }]
    }
  }),
  
  mentor: createTestUser({
    id: 4,
    username: 'mentor',
    user_role: 'mentor',
    role_permissions: ['read', 'write'],
    profile: {
      role: 'mentor',
      primary_role: { name: 'mentor', permission_level: 'write', display_name: 'Mentor' },
      active_roles: [{ name: 'mentor', permission_level: 'write', display_name: 'Mentor' }]
    }
  }),
  
  investor: createTestUser({
    id: 5,
    username: 'investor',
    user_role: 'investor',
    role_permissions: ['read', 'write'],
    profile: {
      role: 'investor',
      primary_role: { name: 'investor', permission_level: 'write', display_name: 'Investor' },
      active_roles: [{ name: 'investor', permission_level: 'write', display_name: 'Investor' }]
    }
  }),
  
  user: createTestUser({
    id: 6,
    username: 'regularuser',
    user_role: 'user',
    role_permissions: ['read']
  })
};

// Expected role access matrix
const expectedAccess = {
  superAdmin: {
    role: 'super_admin',
    dashboardRoute: '/super_admin',
    canAccessSuperAdmin: true,
    canAccessAdmin: true,
    canAccessModerator: true,
    canAccessMentor: true,
    canAccessInvestor: true,
    canAccessUser: true
  },
  admin: {
    role: 'admin',
    dashboardRoute: '/admin',
    canAccessSuperAdmin: false,
    canAccessAdmin: true,
    canAccessModerator: true,
    canAccessMentor: true,
    canAccessInvestor: true,
    canAccessUser: true
  },
  moderator: {
    role: 'moderator',
    dashboardRoute: '/dashboard/moderator',
    canAccessSuperAdmin: false,
    canAccessAdmin: false,
    canAccessModerator: true,
    canAccessMentor: false,
    canAccessInvestor: false,
    canAccessUser: true
  },
  mentor: {
    role: 'mentor',
    dashboardRoute: '/dashboard/mentor',
    canAccessSuperAdmin: false,
    canAccessAdmin: false,
    canAccessModerator: false,
    canAccessMentor: true,
    canAccessInvestor: false,
    canAccessUser: true
  },
  investor: {
    role: 'investor',
    dashboardRoute: '/dashboard/investor',
    canAccessSuperAdmin: false,
    canAccessAdmin: false,
    canAccessModerator: false,
    canAccessMentor: false,
    canAccessInvestor: true,
    canAccessUser: true
  },
  user: {
    role: 'user',
    dashboardRoute: '/dashboard',
    canAccessSuperAdmin: false,
    canAccessAdmin: false,
    canAccessModerator: false,
    canAccessMentor: false,
    canAccessInvestor: false,
    canAccessUser: true
  }
};

/**
 * Run comprehensive RBAC validation tests
 */
export function runRBACValidationTests(): { passed: number; failed: number; results: any[] } {
  const results: any[] = [];
  let passed = 0;
  let failed = 0;

  console.log('🧪 Starting RBAC Validation Tests...\n');

  // Test each user type
  Object.entries(testUsers).forEach(([userType, user]) => {
    const expected = expectedAccess[userType as keyof typeof expectedAccess];
    
    console.log(`\n🔍 Testing ${userType} (${user.username}):`);
    
    // Test role determination
    const actualRole = getUserRole(user);
    const roleTest = actualRole === expected.role;
    console.log(`  Role: ${actualRole} ${roleTest ? '✅' : '❌'} (expected: ${expected.role})`);
    results.push({ test: `${userType}_role`, expected: expected.role, actual: actualRole, passed: roleTest });
    roleTest ? passed++ : failed++;
    
    // Test dashboard route
    const actualDashboard = getDashboardRoute(user);
    const dashboardTest = actualDashboard === expected.dashboardRoute;
    console.log(`  Dashboard: ${actualDashboard} ${dashboardTest ? '✅' : '❌'} (expected: ${expected.dashboardRoute})`);
    results.push({ test: `${userType}_dashboard`, expected: expected.dashboardRoute, actual: actualDashboard, passed: dashboardTest });
    dashboardTest ? passed++ : failed++;
    
    // Test admin checks
    const isAdminResult = isAdmin(user);
    const adminTest = isAdminResult === (userType === 'superAdmin' || userType === 'admin');
    console.log(`  Is Admin: ${isAdminResult} ${adminTest ? '✅' : '❌'}`);
    results.push({ test: `${userType}_isAdmin`, expected: (userType === 'superAdmin' || userType === 'admin'), actual: isAdminResult, passed: adminTest });
    adminTest ? passed++ : failed++;
    
    // Test super admin checks
    const isSuperAdminResult = isSuperAdmin(user);
    const superAdminTest = isSuperAdminResult === (userType === 'superAdmin');
    console.log(`  Is Super Admin: ${isSuperAdminResult} ${superAdminTest ? '✅' : '❌'}`);
    results.push({ test: `${userType}_isSuperAdmin`, expected: (userType === 'superAdmin'), actual: isSuperAdminResult, passed: superAdminTest });
    superAdminTest ? passed++ : failed++;
  });

  console.log(`\n📊 RBAC Validation Results:`);
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);

  return { passed, failed, results };
}

/**
 * Test route access for different user roles
 */
export function testRouteAccess(): void {
  console.log('\n🛣️ Testing Route Access...\n');
  
  const routes = [
    { path: '/super_admin', roles: ['super_admin' as UserRole], name: 'Super Admin Dashboard' },
    { path: '/admin', roles: ['admin' as UserRole, 'super_admin' as UserRole], name: 'Admin Dashboard' },
    { path: '/dashboard/moderator', roles: ['moderator' as UserRole], name: 'Moderator Dashboard' },
    { path: '/dashboard/mentor', roles: ['mentor' as UserRole], name: 'Mentor Dashboard' },
    { path: '/dashboard/investor', roles: ['investor' as UserRole], name: 'Investor Dashboard' },
    { path: '/dashboard', roles: ['user' as UserRole, 'mentor' as UserRole, 'investor' as UserRole, 'moderator' as UserRole, 'admin' as UserRole, 'super_admin' as UserRole], name: 'User Dashboard' }
  ];

  Object.entries(testUsers).forEach(([userType, user]) => {
    console.log(`\n👤 ${userType} route access:`);
    
    routes.forEach(route => {
      const hasAccess = canAccessRoute(user, route.roles);
      const shouldHaveAccess = route.roles.includes(getUserRole(user)) || isSuperAdmin(user);
      const accessTest = hasAccess === shouldHaveAccess;
      
      console.log(`  ${route.name}: ${hasAccess ? 'ALLOW' : 'DENY'} ${accessTest ? '✅' : '❌'}`);
    });
  });
}

// Export test users for external testing
export { testUsers, expectedAccess };
