// Simple Authentication Test
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:8000';

async function testLogin() {
  console.log('🚀 Starting simple authentication test...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/auth/token/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'testuser',
        password: 'testpass123'
      })
    });

    console.log('Response status:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Login successful!');
      console.log('User data:', JSON.stringify(data.user, null, 2));
      console.log('Access token received:', !!data.access);
      console.log('Refresh token received:', !!data.refresh);
    } else {
      const errorData = await response.json();
      console.log('❌ Login failed:', errorData);
    }
  } catch (error) {
    console.error('❌ Error during test:', error.message);
  }
}

testLogin();
