# Page snapshot

```yaml
- button "Open Menu":
  - img
- main:
  - img
  - heading "Super Admin Control Center" [level=1]
  - paragraph: System-wide monitoring and control - superadmin
  - heading "System Metrics" [level=2]
  - img
  - paragraph: Total Users
  - paragraph: 1,247
  - img
  - paragraph: Active Users
  - paragraph: "89"
  - img
  - paragraph: System Health
  - paragraph: healthy
  - img
  - paragraph: Security Alerts
  - paragraph: "1"
  - heading "Critical System Actions" [level=2]
  - link "System Management Manage system configuration and settings CRITICAL RISK":
    - /url: /super_admin/system-management
    - img
    - heading "System Management" [level=3]
    - paragraph: Manage system configuration and settings
    - text: CRITICAL RISK
  - link "User Impersonation Impersonate users for support HIGH RISK":
    - /url: /super_admin/user-impersonation
    - img
    - heading "User Impersonation" [level=3]
    - paragraph: Impersonate users for support
    - text: HIGH RISK
  - link "AI System Management Manage AI capabilities and configuration HIGH RISK":
    - /url: /super_admin/ai-system-management
    - img
    - heading "AI System Management" [level=3]
    - paragraph: Manage AI capabilities and configuration
    - text: HIGH RISK
  - link "Security Center Monitor security and access controls CRITICAL RISK":
    - /url: /super_admin/security
    - img
    - heading "Security Center" [level=3]
    - paragraph: Monitor security and access controls
    - text: CRITICAL RISK
  - heading "System Status" [level=2]
  - img
  - heading "System Health" [level=3]
  - paragraph: Optimal
  - img
  - heading "Performance" [level=3]
  - paragraph: 95%
  - img
  - heading "Pending Actions" [level=3]
  - paragraph: "3"
- button "Open Tanstack query devtools":
  - img
```