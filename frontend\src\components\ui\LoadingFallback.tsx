import React, { memo } from 'react';

interface LoadingFallbackProps {
  message?: string;
  size?: 'small' | 'medium' | 'large';
  fullScreen?: boolean;
}

/**
 * Optimized loading fallback component for React.Suspense
 * Memoized to prevent unnecessary re-renders during loading
 */
const LoadingFallback: React.FC<LoadingFallbackProps> = memo(({
  message = 'Loading...',
  size = 'medium',
  fullScreen = false
}) => {
  const sizeClasses = {
    small: 'w-6 h-6',
    medium: 'w-12 h-12',
    large: 'w-16 h-16'
  };

  const containerClasses = fullScreen
    ? 'fixed inset-0 flex flex-col items-center justify-center bg-white/80 backdrop-blur-sm z-50'
    : 'flex flex-col items-center justify-center p-6 min-h-[200px]';

  return (
    <div className={containerClasses}>
      <div
        className={`${sizeClasses[size]} rounded-full border-4 border-indigo-200 border-t-indigo-600 animate-spin mb-4`}
        role="status"
        aria-label="Loading"
      />
      <p className="text-indigo-600 text-sm font-medium" aria-live="polite">
        {message}
      </p>
    </div>
  );
});

export default LoadingFallback;
