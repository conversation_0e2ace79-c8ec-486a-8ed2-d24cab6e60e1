# 🛠️ RBAC Implementation Guide

**Document Version:** 1.0  
**Date:** January 2025  
**Audience:** Developers & System Administrators  
**Status:** ✅ PRODUCTION READY

---

## 📋 Quick Start

### **Essential Files**
```
frontend/src/utils/unifiedRoleManager.ts     # Core RBAC functions
frontend/src/config/navigationConfig.ts      # Navigation configuration
frontend/src/components/auth/RoleProtectedRoute.tsx  # Route protection
frontend/src/components/layout/UniversalSidebar.tsx  # Navigation rendering
```

### **Basic Usage**
```typescript
import { getUserRole, hasPermission, canAccessRoute } from '../utils/unifiedRoleManager';
import { RoleProtectedRoute } from '../components/auth/RoleProtectedRoute';

// Get user's role
const userRole = getUserRole(user);

// Check permissions
if (hasPermission(user, 'admin')) {
  // User has admin permissions
}

// Protect routes
<RoleProtectedRoute allowedRoles={['admin', 'super_admin']}>
  <AdminComponent />
</RoleProtectedRoute>
```

---

## 🏗️ Core Architecture

### **1. Unified Role Manager**
**File:** `frontend/src/utils/unifiedRoleManager.ts`

#### **Key Functions**
```typescript
// Role determination (primary function)
export const getUserRole = (user: any): UserRole => {
  if (!user) return 'user';
  
  // Priority: backend user_role → is_superuser → is_staff → default
  if (user.user_role) return user.user_role;
  if (user.is_superuser) return 'super_admin';
  if (user.is_staff) return 'admin';
  return 'user';
};

// Permission checking
export const hasPermission = (user: any, permission: PermissionLevel): boolean => {
  const userPermissions = getUserPermissions(user);
  return userPermissions.includes(permission);
};

// Route access validation
export const canAccessRoute = (
  user: any,
  allowedRoles: UserRole[],
  requiredPermissions: PermissionLevel[],
  requireAuth: boolean = true
): boolean => {
  if (!requireAuth) return true;
  if (!user && requireAuth) return false;
  
  const userRole = getUserRole(user);
  const hasRoleAccess = allowedRoles.includes(userRole);
  const hasPermissionAccess = requiredPermissions.every(permission => 
    hasPermission(user, permission)
  );
  
  return hasRoleAccess && hasPermissionAccess;
};
```

#### **Role Hierarchy**
```typescript
export type UserRole = 
  | 'super_admin'    // Highest authority
  | 'admin'          // Platform administration
  | 'moderator'      // Content moderation
  | 'mentor'         // Business mentorship
  | 'investor'       // Investment activities
  | 'user';          // Basic access
```

#### **Permission Levels**
```typescript
export type PermissionLevel = 
  | 'read'           // View content
  | 'write'          // Create/edit content
  | 'moderate'       // Moderate content
  | 'admin'          // Administrative functions
  | 'super_admin';   // System control
```

### **2. Navigation Configuration**
**File:** `frontend/src/config/navigationConfig.ts`

#### **Navigation Item Structure**
```typescript
export interface NavItem {
  id: string;                    // Unique identifier
  name: string;                  // Display name
  path: string;                  // Route path
  icon: string;                  // Icon component name
  allowedRoles: UserRole[];      // Roles that can access
  category: string;              // Grouping category
  riskLevel?: 'low' | 'medium' | 'high' | 'critical';
  requiresPermission?: PermissionLevel[];
  children?: NavItem[];          // Sub-navigation items
}
```

#### **Adding New Navigation Items**
```typescript
export const NAVIGATION_ITEMS: NavItem[] = [
  {
    id: 'new-feature',
    name: 'New Feature',
    path: '/dashboard/new-feature',
    icon: 'NewFeatureIcon',
    allowedRoles: ['user', 'mentor', 'investor', 'admin', 'super_admin'],
    category: 'main',
    riskLevel: 'medium',
    requiresPermission: ['write']
  },
  // ... other items
];
```

### **3. Route Protection**
**File:** `frontend/src/components/auth/RoleProtectedRoute.tsx`

#### **Basic Route Protection**
```typescript
<RoleProtectedRoute 
  allowedRoles={['admin', 'super_admin']}
  requireAuth={true}
  showUnauthorized={false}
  fallbackPath="/dashboard"
>
  <AdminComponent />
</RoleProtectedRoute>
```

#### **Convenience Components**
```typescript
// Super admin only
<SuperAdminRoute>
  <SystemMonitoring />
</SuperAdminRoute>

// Admin and super admin
<AdminRoute>
  <UserManagement />
</AdminRoute>

// Business users (user, mentor, investor)
<BusinessUserRoute>
  <BusinessPlans />
</BusinessUserRoute>
```

#### **useRoleAccess Hook**
```typescript
const MyComponent = () => {
  const { userRole, isAdmin, isSuperAdmin, isBusinessUser } = useRoleAccess();
  
  return (
    <div>
      {isAdmin && <AdminPanel />}
      {isSuperAdmin && <SuperAdminPanel />}
      {isBusinessUser && <BusinessContent />}
    </div>
  );
};
```

---

## 🔧 Implementation Patterns

### **1. Component-Level Access Control**
```typescript
import { useAppSelector } from '../store/hooks';
import { getUserRole, hasPermission } from '../utils/unifiedRoleManager';

const MyComponent = () => {
  const { user } = useAppSelector(state => state.auth);
  const userRole = getUserRole(user);
  
  // Conditional rendering based on role
  if (userRole === 'super_admin') {
    return <SuperAdminView />;
  }
  
  if (hasPermission(user, 'admin')) {
    return <AdminView />;
  }
  
  return <UserView />;
};
```

### **2. Navigation Rendering**
```typescript
import { getNavigationItemsForRole } from '../config/navigationConfig';

const Sidebar = () => {
  const { user } = useAppSelector(state => state.auth);
  const userRole = getUserRole(user);
  const navItems = getNavigationItemsForRole(userRole);
  
  return (
    <nav>
      {navItems.map(item => (
        <NavLink key={item.id} to={item.path}>
          {item.name}
        </NavLink>
      ))}
    </nav>
  );
};
```

### **3. API Request Authorization**
```typescript
const makeAuthorizedRequest = async (endpoint: string, user: any) => {
  // Check if user has permission for this endpoint
  if (!hasPermission(user, 'write')) {
    throw new Error('Insufficient permissions');
  }
  
  // Make API request with proper authorization
  return await apiRequest(endpoint, {
    headers: {
      'Authorization': `Bearer ${user.token}`,
      'X-User-Role': getUserRole(user)
    }
  });
};
```

### **4. Form Access Control**
```typescript
const BusinessPlanForm = () => {
  const { user } = useAppSelector(state => state.auth);
  const canEdit = hasPermission(user, 'write');
  const canDelete = hasPermission(user, 'admin');
  
  return (
    <form>
      <input disabled={!canEdit} />
      {canDelete && <DeleteButton />}
    </form>
  );
};
```

---

## 🚀 Adding New Features

### **1. Adding a New Role**
```typescript
// 1. Update UserRole type in unifiedRoleManager.ts
export type UserRole = 
  | 'super_admin'
  | 'admin'
  | 'moderator'
  | 'mentor'
  | 'investor'
  | 'user'
  | 'new_role';  // Add new role

// 2. Update permission mapping
const ROLE_PERMISSIONS: Record<UserRole, PermissionLevel[]> = {
  // ... existing roles
  'new_role': ['read', 'write'],  // Define permissions
};

// 3. Update dashboard routing
const DASHBOARD_ROUTES: Record<UserRole, string> = {
  // ... existing routes
  'new_role': '/dashboard/new-role',  // Define default route
};

// 4. Add navigation items for the new role
// 5. Update tests to include new role
// 6. Update documentation
```

### **2. Adding a New Navigation Item**
```typescript
// 1. Add to NAVIGATION_ITEMS in navigationConfig.ts
{
  id: 'new-feature',
  name: 'New Feature',
  path: '/dashboard/new-feature',
  icon: 'NewFeatureIcon',
  allowedRoles: ['user', 'mentor', 'admin', 'super_admin'],
  category: 'main',
  riskLevel: 'medium'
}

// 2. Create the route component
const NewFeaturePage = () => {
  return <div>New Feature Content</div>;
};

// 3. Add route protection
<RoleProtectedRoute allowedRoles={['user', 'mentor', 'admin', 'super_admin']}>
  <NewFeaturePage />
</RoleProtectedRoute>

// 4. Update tests
// 5. Update access control matrix documentation
```

### **3. Adding a New Permission Level**
```typescript
// 1. Update PermissionLevel type
export type PermissionLevel = 
  | 'read'
  | 'write'
  | 'moderate'
  | 'admin'
  | 'super_admin'
  | 'new_permission';  // Add new permission

// 2. Update role-permission mapping
const ROLE_PERMISSIONS: Record<UserRole, PermissionLevel[]> = {
  'super_admin': ['read', 'write', 'moderate', 'admin', 'super_admin', 'new_permission'],
  // ... update other roles as needed
};

// 3. Use in components
if (hasPermission(user, 'new_permission')) {
  // Show feature requiring new permission
}
```

---

## 🧪 Testing Guidelines

### **1. Unit Testing Roles**
```typescript
import { getUserRole, hasPermission } from '../utils/unifiedRoleManager';

describe('Role Management', () => {
  test('should return correct role for super admin', () => {
    const user = { is_superuser: true };
    expect(getUserRole(user)).toBe('super_admin');
  });
  
  test('should check permissions correctly', () => {
    const user = { user_role: 'admin' };
    expect(hasPermission(user, 'admin')).toBe(true);
    expect(hasPermission(user, 'super_admin')).toBe(false);
  });
});
```

### **2. Component Testing**
```typescript
import { render, screen } from '@testing-library/react';
import { RoleProtectedRoute } from '../RoleProtectedRoute';

test('should show content for authorized user', () => {
  const user = { user_role: 'admin' };
  
  render(
    <RoleProtectedRoute allowedRoles={['admin']}>
      <div>Admin Content</div>
    </RoleProtectedRoute>
  );
  
  expect(screen.getByText('Admin Content')).toBeInTheDocument();
});
```

### **3. Integration Testing**
```typescript
// Test complete user flow
test('admin user can access admin features', async () => {
  const adminUser = { user_role: 'admin' };
  
  // Test navigation access
  const navItems = getNavigationItemsForRole('admin');
  expect(navItems.some(item => item.id === 'user-management')).toBe(true);
  
  // Test route access
  expect(canAccessRoute(adminUser, ['admin'], ['admin'], true)).toBe(true);
  
  // Test component rendering
  render(<AdminDashboard user={adminUser} />);
  expect(screen.getByText('User Management')).toBeInTheDocument();
});
```

---

## 🔒 Security Best Practices

### **1. Always Validate on Backend**
```typescript
// Frontend role checking is for UX only
// Always validate permissions on the backend
const deleteUser = async (userId: number) => {
  // Frontend check (for UX)
  if (!hasPermission(user, 'admin')) {
    showError('Insufficient permissions');
    return;
  }
  
  // Backend will also validate permissions
  await apiRequest(`/api/users/${userId}`, 'DELETE');
};
```

### **2. Use Principle of Least Privilege**
```typescript
// Give users minimum required access
const getNavigationForUser = (user: any) => {
  const userRole = getUserRole(user);
  
  // Only show what user can actually access
  return NAVIGATION_ITEMS.filter(item => 
    item.allowedRoles.includes(userRole)
  );
};
```

### **3. Fail Secure**
```typescript
// Default to deny access
const canAccessFeature = (user: any, feature: string): boolean => {
  if (!user) return false;  // No user = no access
  
  const userRole = getUserRole(user);
  const allowedRoles = FEATURE_ACCESS[feature];
  
  return allowedRoles?.includes(userRole) ?? false;  // Default to false
};
```

### **4. Audit Access Attempts**
```typescript
const logAccessAttempt = (user: any, resource: string, granted: boolean) => {
  console.log({
    userId: user?.id,
    userRole: getUserRole(user),
    resource,
    granted,
    timestamp: new Date().toISOString()
  });
};
```

---

## 🚨 Common Pitfalls

### **1. ❌ Don't Hardcode Roles**
```typescript
// BAD
if (user.role === 'admin' || user.role === 'super_admin') {
  // This breaks when roles change
}

// GOOD
if (hasPermission(user, 'admin')) {
  // Uses centralized permission system
}
```

### **2. ❌ Don't Skip Route Protection**
```typescript
// BAD - No protection
<Route path="/admin" component={AdminPanel} />

// GOOD - Protected route
<Route path="/admin" element={
  <AdminRoute>
    <AdminPanel />
  </AdminRoute>
} />
```

### **3. ❌ Don't Trust Frontend Only**
```typescript
// BAD - Frontend only validation
const deleteUser = (userId) => {
  if (user.role === 'admin') {
    // Direct API call without backend validation
    fetch(`/api/users/${userId}`, { method: 'DELETE' });
  }
};

// GOOD - Backend validates too
const deleteUser = async (userId) => {
  // Frontend check for UX
  if (!hasPermission(user, 'admin')) {
    showError('Access denied');
    return;
  }
  
  try {
    // Backend will also validate permissions
    await apiRequest(`/api/users/${userId}`, 'DELETE');
  } catch (error) {
    if (error.status === 403) {
      showError('Access denied');
    }
  }
};
```

---

## 📚 Additional Resources

### **Documentation Files**
- `docs/RBAC_AUDIT_REPORT.md` - Complete system audit
- `docs/ACCESS_CONTROL_MATRIX.md` - Detailed access matrix
- `frontend/src/__tests__/rbac-integration.test.ts` - Integration tests

### **Test Commands**
```bash
# Run RBAC test suite
node frontend/scripts/test-rbac.js

# Run specific tests
npm test -- utils/__tests__/unifiedRoleManager.test.ts
npm test -- config/__tests__/navigationConfig.test.ts
```

### **Validation Tools**
- Mock data detection scanner
- Role consistency validator
- Duplicate code detector
- Security boundary tester

---

**🛠️ This guide provides everything needed to implement, extend, and maintain the RBAC system securely and effectively.**
