/**
 * API Integration Tests
 * Tests API endpoints, error handling, and data flow
 */

import { test, expect, Page } from '@playwright/test';

const BASE_URL = process.env.REACT_APP_BASE_URL || 'http://localhost:3000';
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Mock responses removed - tests should use real API or proper mocking

async function setupAPIInterception(page: Page, backendAvailable: boolean) {
  if (!backendAvailable) {
    // Intercept API calls and return mock data
    await page.route('**/api/incubator/business-ideas/**', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ results: MOCK_RESPONSES.businessIdeas })
      });
    });

    await page.route('**/api/incubator/business-plans/**', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ results: MOCK_RESPONSES.businessPlans })
      });
    });

    await page.route('**/api/incubator/mentor-profiles/**', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ results: MOCK_RESPONSES.mentors })
      });
    });

    // Mock authentication endpoints
    await page.route('**/api/auth/login/**', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: { id: 1, email: '<EMAIL>', role: 'entrepreneur' },
          access: 'mock_access_token',
          refresh: 'mock_refresh_token'
        })
      });
    });
  }
}

async function checkBackendHealth(): Promise<boolean> {
  try {
    const response = await fetch(`${API_BASE_URL}/api/health/`, { 
      method: 'GET',
      timeout: 5000 
    });
    return response.ok;
  } catch {
    return false;
  }
}

test.describe('API Integration Tests', () => {
  let backendAvailable: boolean;

  test.beforeAll(async () => {
    backendAvailable = await checkBackendHealth();
    console.log(`Backend available for API tests: ${backendAvailable}`);
  });

  test.beforeEach(async ({ page }) => {
    await setupAPIInterception(page, backendAvailable);
  });

  test.describe('Authentication API', () => {
    test('should handle successful login', async ({ page }) => {
      await page.goto(`${BASE_URL}/login`);
      
      // Monitor network requests
      const loginRequest = page.waitForResponse(response => 
        response.url().includes('/api/auth/login/') && response.request().method() === 'POST'
      );

      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'testpassword');
      await page.click('[data-testid="login-button"]');

      const response = await loginRequest;
      expect(response.status()).toBe(200);

      // Verify successful login redirect
      await page.waitForURL('**/dashboard/**');
    });

    test('should handle login errors gracefully', async ({ page }) => {
      // Mock failed login
      await page.route('**/api/auth/login/**', route => {
        route.fulfill({
          status: 400,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Invalid credentials' })
        });
      });

      await page.goto(`${BASE_URL}/login`);
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'wrongpassword');
      await page.click('[data-testid="login-button"]');

      // Verify error message is displayed
      await expect(page.locator('[data-testid="error-message"]')).toContainText('Invalid credentials');
    });

    test('should handle network errors', async ({ page }) => {
      // Mock network failure
      await page.route('**/api/auth/login/**', route => {
        route.abort('failed');
      });

      await page.goto(`${BASE_URL}/login`);
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'testpassword');
      await page.click('[data-testid="login-button"]');

      // Verify network error handling
      await expect(page.locator('[data-testid="error-message"]')).toContainText('Network error');
    });
  });

  test.describe('Business Ideas API', () => {
    test('should load business ideas correctly', async ({ page }) => {
      await page.goto(`${BASE_URL}/dashboard/business-ideas`);

      // Wait for API call
      const apiRequest = page.waitForResponse(response => 
        response.url().includes('/api/incubator/business-ideas/')
      );

      await apiRequest;

      // Verify data is displayed
      await expect(page.locator('[data-testid="business-ideas-list"]')).toBeVisible();
      await expect(page.locator('[data-testid="business-idea-card"]')).toHaveCount(1);
    });

    test('should handle business idea creation', async ({ page }) => {
      // Mock successful creation
      await page.route('**/api/incubator/business-ideas/**', route => {
        if (route.request().method() === 'POST') {
          route.fulfill({
            status: 201,
            contentType: 'application/json',
            body: JSON.stringify({
              id: 2,
              title: 'New Business Idea',
              description: 'Created via API test'
            })
          });
        } else {
          route.continue();
        }
      });

      await page.goto(`${BASE_URL}/dashboard/business-ideas/create`);
      await page.fill('[data-testid="title-input"]', 'New Business Idea');
      await page.fill('[data-testid="description-textarea"]', 'Created via API test');
      await page.click('[data-testid="submit-button"]');

      // Verify successful creation
      await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    });

    test('should handle API validation errors', async ({ page }) => {
      // Mock validation error
      await page.route('**/api/incubator/business-ideas/**', route => {
        if (route.request().method() === 'POST') {
          route.fulfill({
            status: 400,
            contentType: 'application/json',
            body: JSON.stringify({
              title: ['This field is required.'],
              description: ['This field cannot be blank.']
            })
          });
        } else {
          route.continue();
        }
      });

      await page.goto(`${BASE_URL}/dashboard/business-ideas/create`);
      await page.click('[data-testid="submit-button"]');

      // Verify validation errors are displayed
      await expect(page.locator('[data-testid="title-error"]')).toContainText('This field is required');
      await expect(page.locator('[data-testid="description-error"]')).toContainText('cannot be blank');
    });
  });

  test.describe('Real-time Features', () => {
    test('should handle WebSocket connections', async ({ page }) => {
      // Skip if backend not available
      if (!backendAvailable) {
        test.skip('Backend not available for WebSocket testing');
      }

      await page.goto(`${BASE_URL}/dashboard/chat`);

      // Monitor WebSocket connections
      let wsConnected = false;
      page.on('websocket', ws => {
        wsConnected = true;
        console.log('WebSocket connected:', ws.url());
      });

      // Wait for WebSocket connection
      await page.waitForTimeout(2000);
      expect(wsConnected).toBe(true);
    });

    test('should handle real-time notifications', async ({ page }) => {
      await page.goto(`${BASE_URL}/dashboard`);

      // Mock real-time notification
      await page.evaluate(() => {
        window.dispatchEvent(new CustomEvent('notification', {
          detail: {
            type: 'mentorship_request',
            message: 'You have a new mentorship request',
            timestamp: new Date().toISOString()
          }
        }));
      });

      // Verify notification is displayed
      await expect(page.locator('[data-testid="notification-toast"]')).toBeVisible();
      await expect(page.locator('[data-testid="notification-message"]')).toContainText('mentorship request');
    });
  });

  test.describe('Error Handling', () => {
    test('should handle 500 server errors', async ({ page }) => {
      await page.route('**/api/**', route => {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Internal server error' })
        });
      });

      await page.goto(`${BASE_URL}/dashboard/business-ideas`);

      // Verify error state is displayed
      await expect(page.locator('[data-testid="error-state"]')).toBeVisible();
      await expect(page.locator('[data-testid="error-message"]')).toContainText('server error');
    });

    test('should handle rate limiting', async ({ page }) => {
      await page.route('**/api/**', route => {
        route.fulfill({
          status: 429,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Rate limit exceeded' })
        });
      });

      await page.goto(`${BASE_URL}/dashboard/business-ideas`);

      // Verify rate limit message
      await expect(page.locator('[data-testid="rate-limit-message"]')).toBeVisible();
    });

    test('should handle unauthorized access', async ({ page }) => {
      await page.route('**/api/**', route => {
        route.fulfill({
          status: 401,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Unauthorized' })
        });
      });

      await page.goto(`${BASE_URL}/dashboard/business-ideas`);

      // Verify redirect to login
      await page.waitForURL('**/login**');
    });
  });

  test.describe('Performance', () => {
    test('should load pages within acceptable time limits', async ({ page }) => {
      const startTime = Date.now();
      
      await page.goto(`${BASE_URL}/dashboard`);
      await page.waitForSelector('[data-testid="dashboard-content"]');
      
      const loadTime = Date.now() - startTime;
      expect(loadTime).toBeLessThan(5000); // 5 seconds max
    });

    test('should handle large datasets efficiently', async ({ page }) => {
      // Mock large dataset
      const largeDataset = Array.from({ length: 100 }, (_, i) => ({
        id: i + 1,
        title: `Business Idea ${i + 1}`,
        description: `Description for business idea ${i + 1}`
      }));

      await page.route('**/api/incubator/business-ideas/**', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ results: largeDataset })
        });
      });

      const startTime = Date.now();
      await page.goto(`${BASE_URL}/dashboard/business-ideas`);
      await page.waitForSelector('[data-testid="business-ideas-list"]');
      
      const renderTime = Date.now() - startTime;
      expect(renderTime).toBeLessThan(3000); // 3 seconds max for large dataset
    });
  });
});
