/**
 * USER ROLE CONFIGURATION
 * Dedicated file for regular user role - no duplicates, single source of truth
 */

import { UserRole, PermissionLevel } from '../utils/unifiedRoleManager';

export const USER_ROLE: UserRole = 'user';

export const USER_PERMISSIONS: PermissionLevel[] = ['read'];

export const USER_NAVIGATION_ITEMS = [
  {
    id: 'dashboard',
    name: 'dashboard.title',
    path: '/dashboard',
    icon: 'Home',
    category: 'main' as const
  },
  {
    id: 'business-ideas',
    name: 'businessIdeas.title',
    path: '/dashboard/business-ideas',
    icon: 'Lightbulb',
    category: 'main' as const
  },
  {
    id: 'business-plans',
    name: 'businessPlans.title',
    path: '/dashboard/business-plans',
    icon: 'FileText',
    category: 'main' as const
  },
  {
    id: 'incubator',
    name: 'incubator.title',
    path: '/dashboard/incubator',
    icon: 'Rocket',
    category: 'main' as const
  },
  {
    id: 'posts',
    name: 'posts.title',
    path: '/dashboard/posts',
    icon: 'MessageSquare',
    category: 'content' as const
  },
  {
    id: 'events',
    name: 'events.title',
    path: '/dashboard/events',
    icon: 'Calendar',
    category: 'content' as const
  },
  {
    id: 'resources',
    name: 'resources.title',
    path: '/dashboard/resources',
    icon: 'BookOpen',
    category: 'content' as const
  },
  {
    id: 'templates',
    name: 'templates.title',
    path: '/dashboard/templates',
    icon: 'FileText',
    category: 'content' as const
  },
  {
    id: 'analytics',
    name: 'analytics.title',
    path: '/dashboard/analytics',
    icon: 'BarChart3',
    category: 'main' as const
  },
  {
    id: 'find-mentor',
    name: 'mentorship.findMentor',
    path: '/dashboard/find-mentor',
    icon: 'Users',
    category: 'main' as const
  },
  {
    id: 'ai-assistant',
    name: 'ai.assistant.title',
    path: '/dashboard/ai',
    icon: 'Bot',
    category: 'ai' as const
  },
  {
    id: 'profile',
    name: 'profile.title',
    path: '/profile',
    icon: 'User',
    category: 'main' as const
  },
  {
    id: 'settings',
    name: 'settings.title',
    path: '/settings',
    icon: 'Settings',
    category: 'main' as const
  }
];

export const USER_ROUTES = [
  '/dashboard',
  '/dashboard/business-ideas',
  '/dashboard/business-ideas/new',
  '/dashboard/business-plans',
  '/dashboard/incubator',
  '/dashboard/posts',
  '/dashboard/events',
  '/dashboard/resources',
  '/dashboard/templates',
  '/dashboard/analytics',
  '/dashboard/find-mentor',
  '/dashboard/ai',
  '/profile',
  '/settings'
];

export const USER_DASHBOARD_CONFIG = {
  defaultRoute: '/dashboard',
  welcomeMessage: 'Welcome to your dashboard',
  features: [
    'business_ideas',
    'business_plans',
    'incubator',
    'posts',
    'events',
    'resources',
    'templates',
    'analytics',
    'find_mentor',
    'ai_assistant'
  ]
};

/**
 * Check if a user object represents a regular user
 */
export function isUserRole(user: any): boolean {
  if (!user) return false;
  
  // Explicit user role
  if (user.user_role === 'user') return true;
  
  // No special flags and no special profile roles
  if (!user.is_staff && !user.is_superuser && !user.is_admin) {
    // Check if profile has no special roles
    if (!user.profile?.primary_role || user.profile.primary_role.name === 'user') {
      return true;
    }
  }
  
  return false;
}

/**
 * Get user-specific dashboard route
 */
export function getUserDashboardRoute(): string {
  return USER_DASHBOARD_CONFIG.defaultRoute;
}

export default {
  role: USER_ROLE,
  permissions: USER_PERMISSIONS,
  navigationItems: USER_NAVIGATION_ITEMS,
  routes: USER_ROUTES,
  dashboardConfig: USER_DASHBOARD_CONFIG,
  isRole: isUserRole,
  getDashboardRoute: getUserDashboardRoute
};
