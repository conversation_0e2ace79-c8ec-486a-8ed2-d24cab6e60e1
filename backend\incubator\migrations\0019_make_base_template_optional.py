# Generated manually to fix base_template field

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('incubator', '0015_auto_20250717_1130'),
    ]

    operations = [
        migrations.AlterField(
            model_name='custombusinessplantemplate',
            name='base_template',
            field=models.ForeignKey(
                blank=True, 
                null=True, 
                on_delete=django.db.models.deletion.CASCADE, 
                related_name='custom_templates', 
                to='incubator.businessplantemplate'
            ),
        ),
    ]
