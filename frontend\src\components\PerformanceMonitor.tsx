import React, { useEffect, useState, memo } from 'react';

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  memoryUsage: number;
  navigationTime: number;
}

/**
 * Performance monitoring component to track app performance
 * Only renders in development mode
 */
const PerformanceMonitor: React.FC = memo(() => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Only run in development
    if (process.env.NODE_ENV !== 'development') return;

    const measurePerformance = () => {
      try {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        const memory = (performance as any).memory;
        
        const loadTime = navigation.loadEventEnd - navigation.loadEventStart;
        const renderTime = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
        const memoryUsage = memory ? memory.usedJSHeapSize / 1024 / 1024 : 0;
        const navigationTime = navigation.loadEventEnd - navigation.fetchStart;

        setMetrics({
          loadTime: Math.round(loadTime),
          renderTime: Math.round(renderTime),
          memoryUsage: Math.round(memoryUsage * 100) / 100,
          navigationTime: Math.round(navigationTime)
        });

        // Log performance warnings
        if (loadTime > 3000) {
          console.warn('🐌 Slow page load detected:', loadTime + 'ms');
        }
        if (memoryUsage > 50) {
          console.warn('🧠 High memory usage detected:', memoryUsage + 'MB');
        }
        if (navigationTime > 5000) {
          console.warn('🚀 Slow navigation detected:', navigationTime + 'ms');
        }

      } catch (error) {
        console.error('Performance monitoring error:', error);
      }
    };

    // Measure performance after page load
    if (document.readyState === 'complete') {
      measurePerformance();
    } else {
      window.addEventListener('load', measurePerformance);
    }

    return () => {
      window.removeEventListener('load', measurePerformance);
    };
  }, []);

  // Only show in development and when metrics are available
  if (process.env.NODE_ENV !== 'development' || !metrics) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-blue-600 text-white px-3 py-2 rounded-lg text-sm font-medium shadow-lg hover:bg-blue-700 transition-colors"
        title="Performance Metrics"
      >
        📊 Perf
      </button>
      
      {isVisible && (
        <div className="absolute bottom-12 right-0 bg-white border border-gray-200 rounded-lg shadow-xl p-4 min-w-[250px]">
          <h3 className="font-semibold text-gray-800 mb-3">Performance Metrics</h3>
          
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Load Time:</span>
              <span className={metrics.loadTime > 3000 ? 'text-red-600 font-semibold' : 'text-green-600'}>
                {metrics.loadTime}ms
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Render Time:</span>
              <span className={metrics.renderTime > 1000 ? 'text-red-600 font-semibold' : 'text-green-600'}>
                {metrics.renderTime}ms
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Memory Usage:</span>
              <span className={metrics.memoryUsage > 50 ? 'text-red-600 font-semibold' : 'text-green-600'}>
                {metrics.memoryUsage}MB
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Navigation:</span>
              <span className={metrics.navigationTime > 5000 ? 'text-red-600 font-semibold' : 'text-green-600'}>
                {metrics.navigationTime}ms
              </span>
            </div>
          </div>
          
          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="text-xs text-gray-500">
              🟢 Good | 🟡 OK | 🔴 Needs Improvement
            </div>
          </div>
        </div>
      )}
    </div>
  );
});

PerformanceMonitor.displayName = 'PerformanceMonitor';

export default PerformanceMonitor;
