/**
 * Investment API Service
 * Handles all investment-related API calls
 */

import { apiRequest } from './api';

// Investment Types
export interface InvestmentOpportunity {
  id: string;
  companyName: string;
  industry: string;
  stage: 'Pre-Seed' | 'Seed' | 'Series A' | 'Series B' | 'Series C' | 'Growth';
  fundingGoal: number;
  currentFunding: number;
  valuation: number;
  description: string;
  location: string;
  foundedYear: number;
  teamSize: number;
  riskLevel: 'Low' | 'Medium' | 'High';
  expectedROI: string;
  deadline: string;
  tags: string[];
  businessPlan?: string;
  pitchDeck?: string;
  financials?: string;
  founders: Array<{
    name: string;
    role: string;
    experience: string;
  }>;
}

export interface PortfolioItem {
  id: string;
  companyName: string;
  industry: string;
  investmentAmount: number;
  currentValue: number;
  shares: number;
  investmentDate: string;
  status: 'Active' | 'Exited' | 'Failed';
  performance: number; // percentage
  lastUpdate: string;
  dividends?: number;
  exitValue?: number;
  exitDate?: string;
}

export interface InvestorProfile {
  personalInfo: {
    name: string;
    title: string;
    company: string;
    email: string;
    phone: string;
    location: string;
    bio: string;
    profileImage: string;
  };
  investmentPreferences: {
    industries: string[];
    stages: string[];
    minInvestment: number;
    maxInvestment: number;
    riskTolerance: 'Conservative' | 'Moderate' | 'Aggressive';
    geographicFocus: string[];
  };
  experience: {
    yearsInvesting: number;
    totalInvestments: number;
    successfulExits: number;
    portfolioValue: number;
    averageROI: number;
  };
  credentials: {
    accreditedInvestor: boolean;
    licenses: string[];
    certifications: string[];
  };
}

/**
 * Investment Opportunities API
 */
export const investmentOpportunitiesApi = {
  // Get all investment opportunities
  async getOpportunities(filters?: {
    industry?: string;
    stage?: string;
    minFunding?: number;
    maxFunding?: number;
    riskLevel?: string;
  }): Promise<InvestmentOpportunity[]> {
    try {
      const params = new URLSearchParams();
      if (filters?.industry) params.append('industry', filters.industry);
      if (filters?.stage) params.append('stage', filters.stage);
      if (filters?.minFunding) params.append('min_funding', filters.minFunding.toString());
      if (filters?.maxFunding) params.append('max_funding', filters.maxFunding.toString());
      if (filters?.riskLevel) params.append('risk_level', filters.riskLevel);

      const queryString = params.toString();
      const endpoint = `/api/investments/opportunities/${queryString ? `?${queryString}` : ''}`;
      
      const response = await apiRequest<{ results: InvestmentOpportunity[] }>(endpoint, 'GET');
      return response.results || [];
    } catch (error) {
      console.error('Failed to fetch investment opportunities:', error);
      throw error;
    }
  },

  // Get specific opportunity
  async getOpportunity(id: string): Promise<InvestmentOpportunity> {
    try {
      return await apiRequest<InvestmentOpportunity>(`/api/investments/opportunities/${id}/`, 'GET');
    } catch (error) {
      console.error('Failed to fetch investment opportunity:', error);
      throw error;
    }
  },

  // Express interest in opportunity
  async expressInterest(opportunityId: string, message?: string): Promise<void> {
    try {
      await apiRequest(`/api/investments/opportunities/${opportunityId}/interest/`, 'POST', {
        message
      });
    } catch (error) {
      console.error('Failed to express interest:', error);
      throw error;
    }
  },

  // Make investment
  async makeInvestment(opportunityId: string, amount: number): Promise<any> {
    try {
      return await apiRequest(`/api/investments/opportunities/${opportunityId}/invest/`, 'POST', {
        amount
      });
    } catch (error) {
      console.error('Failed to make investment:', error);
      throw error;
    }
  }
};

/**
 * Portfolio Management API
 */
export const portfolioApi = {
  // Get user's portfolio
  async getPortfolio(): Promise<PortfolioItem[]> {
    try {
      const response = await apiRequest<{ results: PortfolioItem[] }>('/api/investments/portfolio/', 'GET');
      return response.results || [];
    } catch (error) {
      console.error('Failed to fetch portfolio:', error);
      throw error;
    }
  },

  // Get portfolio item details
  async getPortfolioItem(id: string): Promise<PortfolioItem> {
    try {
      return await apiRequest<PortfolioItem>(`/api/investments/portfolio/${id}/`, 'GET');
    } catch (error) {
      console.error('Failed to fetch portfolio item:', error);
      throw error;
    }
  },

  // Update portfolio item
  async updatePortfolioItem(id: string, updates: Partial<PortfolioItem>): Promise<PortfolioItem> {
    try {
      return await apiRequest<PortfolioItem>(`/api/investments/portfolio/${id}/`, 'PATCH', updates);
    } catch (error) {
      console.error('Failed to update portfolio item:', error);
      throw error;
    }
  }
};

/**
 * Investor Profile API
 */
export const investorProfileApi = {
  // Get investor profile
  async getProfile(): Promise<InvestorProfile> {
    try {
      return await apiRequest<InvestorProfile>('/api/investments/profile/', 'GET');
    } catch (error) {
      console.error('Failed to fetch investor profile:', error);
      throw error;
    }
  },

  // Update investor profile
  async updateProfile(updates: Partial<InvestorProfile>): Promise<InvestorProfile> {
    try {
      return await apiRequest<InvestorProfile>('/api/investments/profile/', 'PATCH', updates);
    } catch (error) {
      console.error('Failed to update investor profile:', error);
      throw error;
    }
  },

  // Upload profile image
  async uploadProfileImage(file: File): Promise<{ profileImage: string }> {
    try {
      const formData = new FormData();
      formData.append('profile_image', file);
      
      return await apiRequest<{ profileImage: string }>('/api/investments/profile/image/', 'POST', formData);
    } catch (error) {
      console.error('Failed to upload profile image:', error);
      throw error;
    }
  }
};

/**
 * Investment Analytics API
 */
export const investmentAnalyticsApi = {
  // Get investment analytics
  async getAnalytics(): Promise<any> {
    try {
      return await apiRequest('/api/investments/analytics/', 'GET');
    } catch (error) {
      console.error('Failed to fetch investment analytics:', error);
      throw error;
    }
  },

  // Get market trends
  async getMarketTrends(): Promise<any> {
    try {
      return await apiRequest('/api/investments/market-trends/', 'GET');
    } catch (error) {
      console.error('Failed to fetch market trends:', error);
      throw error;
    }
  }
};

// Export all investment APIs
export const investmentApi = {
  opportunities: investmentOpportunitiesApi,
  portfolio: portfolioApi,
  profile: investorProfileApi,
  analytics: investmentAnalyticsApi
};

export default investmentApi;
