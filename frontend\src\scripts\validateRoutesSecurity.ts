/**
 * ROUTE SECURITY VALIDATION SCRIPT
 * Validates that all routes are properly covered by centralized role mapping
 * Identifies security gaps and inconsistencies
 */

import { ROUTE_ROLE_MAPPINGS, getRouteConfig } from '../config/centralizedRoleRouteMapping';
import { allRoutes } from '../routes/consolidatedRoutes';
import { UserRole } from '../utils/unifiedRoleManager';

interface SecurityIssue {
  severity: 'critical' | 'high' | 'medium' | 'low';
  category: 'missing_route' | 'inconsistent_roles' | 'security_gap' | 'documentation';
  route: string;
  issue: string;
  recommendation: string;
}

interface ValidationResult {
  totalRoutes: number;
  coveredRoutes: number;
  missingRoutes: string[];
  securityIssues: SecurityIssue[];
  summary: {
    critical: number;
    high: number;
    medium: number;
    low: number;
  };
}

/**
 * Main validation function
 */
export function validateRouteSecurity(): ValidationResult {
  const issues: SecurityIssue[] = [];
  const missingRoutes: string[] = [];
  const routePaths = allRoutes.map(route => route.path);
  const mappingPaths = ROUTE_ROLE_MAPPINGS.map(mapping => mapping.path);

  console.log('🔍 Starting Route Security Validation...');
  console.log(`📊 Total routes to validate: ${routePaths.length}`);
  console.log(`📊 Total mappings available: ${mappingPaths.length}`);

  // Check for routes missing from centralized mapping
  routePaths.forEach(path => {
    const config = getRouteConfig(path);
    if (!config) {
      missingRoutes.push(path);
      issues.push({
        severity: 'high',
        category: 'missing_route',
        route: path,
        issue: 'Route not found in centralized role mapping',
        recommendation: `Add route ${path} to ROUTE_ROLE_MAPPINGS with appropriate roles and permissions`
      });
    }
  });

  // Check for inconsistent role requirements
  validateRoleConsistency(issues);

  // Check for security gaps
  validateSecurityGaps(issues);

  // Check for documentation issues
  validateDocumentation(issues);

  const summary = {
    critical: issues.filter(i => i.severity === 'critical').length,
    high: issues.filter(i => i.severity === 'high').length,
    medium: issues.filter(i => i.severity === 'medium').length,
    low: issues.filter(i => i.severity === 'low').length
  };

  return {
    totalRoutes: routePaths.length,
    coveredRoutes: routePaths.length - missingRoutes.length,
    missingRoutes,
    securityIssues: issues,
    summary
  };
}

/**
 * Validate role consistency across similar routes
 */
function validateRoleConsistency(issues: SecurityIssue[]): void {
  const routeGroups = {
    dashboard: ROUTE_ROLE_MAPPINGS.filter(r => r.path.startsWith('/dashboard')),
    admin: ROUTE_ROLE_MAPPINGS.filter(r => r.path.startsWith('/admin')),
    superAdmin: ROUTE_ROLE_MAPPINGS.filter(r => r.path.startsWith('/super_admin')),
    mentorship: ROUTE_ROLE_MAPPINGS.filter(r => r.path.includes('mentorship')),
    investment: ROUTE_ROLE_MAPPINGS.filter(r => r.path.includes('investment'))
  };

  Object.entries(routeGroups).forEach(([groupName, routes]) => {
    if (routes.length < 2) return;

    // Check for inconsistent role requirements within groups
    const roleVariations = new Set(routes.map(r => JSON.stringify(r.allowedRoles.sort())));
    
    if (roleVariations.size > 3) { // Allow some variation but flag excessive differences
      issues.push({
        severity: 'medium',
        category: 'inconsistent_roles',
        route: `${groupName} group`,
        issue: `High role variation in ${groupName} routes (${roleVariations.size} different patterns)`,
        recommendation: `Review ${groupName} routes for consistent role requirements`
      });
    }
  });
}

/**
 * Validate for potential security gaps
 */
function validateSecurityGaps(issues: SecurityIssue[]): void {
  ROUTE_ROLE_MAPPINGS.forEach(route => {
    // Check for overly permissive routes
    if (route.allowedRoles.length >= 5 && !route.path.startsWith('/dashboard')) {
      issues.push({
        severity: 'medium',
        category: 'security_gap',
        route: route.path,
        issue: `Route accessible to ${route.allowedRoles.length} roles - potentially overly permissive`,
        recommendation: `Review if all roles really need access to ${route.path}`
      });
    }

    // Check for admin routes without proper permissions
    if (route.path.startsWith('/admin') && !route.requiredPermissions.includes('admin')) {
      issues.push({
        severity: 'high',
        category: 'security_gap',
        route: route.path,
        issue: 'Admin route without admin permission requirement',
        recommendation: `Add 'admin' to requiredPermissions for ${route.path}`
      });
    }

    // Check for super admin routes without proper permissions
    if (route.path.startsWith('/super_admin') && !route.requiredPermissions.includes('super_admin')) {
      issues.push({
        severity: 'critical',
        category: 'security_gap',
        route: route.path,
        issue: 'Super admin route without super_admin permission requirement',
        recommendation: `Add 'super_admin' to requiredPermissions for ${route.path}`
      });
    }

    // Check for public routes that might need authentication
    if (!route.requireAuth && route.path.includes('dashboard')) {
      issues.push({
        severity: 'high',
        category: 'security_gap',
        route: route.path,
        issue: 'Dashboard route that doesn\'t require authentication',
        recommendation: `Set requireAuth: true for ${route.path}`
      });
    }
  });
}

/**
 * Validate documentation completeness
 */
function validateDocumentation(issues: SecurityIssue[]): void {
  ROUTE_ROLE_MAPPINGS.forEach(route => {
    if (!route.description || route.description.length < 10) {
      issues.push({
        severity: 'low',
        category: 'documentation',
        route: route.path,
        issue: 'Route has insufficient description',
        recommendation: `Add detailed description for ${route.path} explaining access requirements`
      });
    }
  });
}

/**
 * Generate detailed report
 */
export function generateSecurityReport(): string {
  const result = validateRouteSecurity();
  
  let report = `
# 🛡️ ROUTE SECURITY VALIDATION REPORT
Generated: ${new Date().toISOString()}

## 📊 SUMMARY
- Total Routes: ${result.totalRoutes}
- Covered Routes: ${result.coveredRoutes}
- Missing Routes: ${result.missingRoutes.length}
- Security Issues: ${result.securityIssues.length}

## 🚨 ISSUE BREAKDOWN
- Critical: ${result.summary.critical}
- High: ${result.summary.high}
- Medium: ${result.summary.medium}
- Low: ${result.summary.low}

## ❌ MISSING ROUTES
${result.missingRoutes.length > 0 ? 
  result.missingRoutes.map(route => `- ${route}`).join('\n') : 
  'None - All routes are covered! ✅'
}

## 🔍 SECURITY ISSUES
`;

  result.securityIssues.forEach(issue => {
    const emoji = {
      critical: '🔴',
      high: '🟠',
      medium: '🟡',
      low: '🔵'
    }[issue.severity];

    report += `
### ${emoji} ${issue.severity.toUpperCase()}: ${issue.route}
**Category**: ${issue.category}
**Issue**: ${issue.issue}
**Recommendation**: ${issue.recommendation}
`;
  });

  return report;
}

/**
 * Run validation and log results
 */
export function runSecurityValidation(): void {
  console.log('🔍 Running Route Security Validation...\n');
  
  const result = validateRouteSecurity();
  
  console.log('📊 VALIDATION RESULTS:');
  console.log(`✅ Covered Routes: ${result.coveredRoutes}/${result.totalRoutes}`);
  console.log(`❌ Missing Routes: ${result.missingRoutes.length}`);
  console.log(`🚨 Security Issues: ${result.securityIssues.length}`);
  
  if (result.missingRoutes.length > 0) {
    console.log('\n❌ MISSING ROUTES:');
    result.missingRoutes.forEach(route => console.log(`  - ${route}`));
  }
  
  if (result.securityIssues.length > 0) {
    console.log('\n🚨 SECURITY ISSUES:');
    result.securityIssues.forEach(issue => {
      const emoji = { critical: '🔴', high: '🟠', medium: '🟡', low: '🔵' }[issue.severity];
      console.log(`  ${emoji} ${issue.severity.toUpperCase()}: ${issue.route}`);
      console.log(`     ${issue.issue}`);
    });
  }
  
  if (result.missingRoutes.length === 0 && result.securityIssues.length === 0) {
    console.log('\n🎉 ALL ROUTES ARE SECURE! No issues found.');
  } else {
    console.log(`\n⚠️ Found ${result.securityIssues.length} security issues that need attention.`);
  }
}

// Export for use in other scripts
export default {
  validateRouteSecurity,
  generateSecurityReport,
  runSecurityValidation
};
