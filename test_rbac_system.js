/**
 * RBAC System Test Runner
 * Simple Node.js script to test the role-based access control system
 */

// Mock the unified role manager functions for testing
const mockUsers = {
  superAdmin: {
    id: 1,
    username: 'superadmin',
    email: '<EMAIL>',
    is_superuser: true,
    is_staff: true,
    is_admin: true,
    user_role: 'super_admin',
    role_permissions: ['read', 'write', 'moderate', 'admin', 'super_admin']
  },
  admin: {
    id: 2,
    username: 'admin',
    email: '<EMAIL>',
    is_superuser: false,
    is_staff: true,
    is_admin: true,
    user_role: 'admin',
    role_permissions: ['read', 'write', 'moderate', 'admin']
  },
  moderator: {
    id: 3,
    username: 'moderator',
    email: '<EMAIL>',
    is_superuser: false,
    is_staff: false,
    is_admin: false,
    user_role: 'moderator',
    role_permissions: ['read', 'write', 'moderate'],
    profile: {
      role: 'moderator',
      primary_role: { name: 'moderator', permission_level: 'moderate', display_name: 'Moderator' }
    }
  },
  mentor: {
    id: 4,
    username: 'mentor',
    email: '<EMAIL>',
    is_superuser: false,
    is_staff: false,
    is_admin: false,
    user_role: 'mentor',
    role_permissions: ['read', 'write'],
    profile: {
      role: 'mentor',
      primary_role: { name: 'mentor', permission_level: 'write', display_name: 'Mentor' }
    }
  },
  investor: {
    id: 5,
    username: 'investor',
    email: '<EMAIL>',
    is_superuser: false,
    is_staff: false,
    is_admin: false,
    user_role: 'investor',
    role_permissions: ['read', 'write'],
    profile: {
      role: 'investor',
      primary_role: { name: 'investor', permission_level: 'write', display_name: 'Investor' }
    }
  },
  user: {
    id: 6,
    username: 'regularuser',
    email: '<EMAIL>',
    is_superuser: false,
    is_staff: false,
    is_admin: false,
    user_role: 'user',
    role_permissions: ['read']
  }
};

// Mock unified role manager functions
function getUserRole(user) {
  if (!user) return 'user';
  if (user.user_role) return user.user_role;
  if (user.profile?.role) return user.profile.role;
  if (user.is_superuser) return 'super_admin';
  if (user.is_staff || user.is_admin) return 'admin';
  return 'user';
}

function hasRole(user, roleName) {
  return getUserRole(user) === roleName;
}

function hasAnyRole(user, roleNames) {
  return roleNames.includes(getUserRole(user));
}

function isSuperAdmin(user) {
  return hasRole(user, 'super_admin');
}

function isAdmin(user) {
  return hasRole(user, 'admin') || isSuperAdmin(user);
}

function getDashboardRoute(user) {
  const role = getUserRole(user);
  const routes = {
    'super_admin': '/super_admin',
    'admin': '/admin',
    'moderator': '/dashboard/moderator',
    'mentor': '/dashboard/mentor',
    'investor': '/dashboard/investor',
    'user': '/dashboard'
  };
  return routes[role] || '/dashboard';
}

function canAccessRoute(user, requiredRoles, requiredPermissions) {
  if (!user) return false;
  if (isSuperAdmin(user)) return true;
  if (requiredRoles && requiredRoles.length > 0) {
    return hasAnyRole(user, requiredRoles);
  }
  return true;
}

// Test navigation items for different roles
const navigationItems = [
  { name: 'Dashboard', userTypes: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'] },
  { name: 'Business Ideas', userTypes: ['user', 'mentor', 'investor'] },
  { name: 'Business Plans', userTypes: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'] },
  { name: 'Super Admin Dashboard', userTypes: ['super_admin'] },
  { name: 'Admin Dashboard', userTypes: ['admin', 'super_admin'] },
  { name: 'Moderator Dashboard', userTypes: ['moderator'] },
  { name: 'Mentor Dashboard', userTypes: ['mentor'] },
  { name: 'Investor Dashboard', userTypes: ['investor'] }
];

// Run tests
function runRBACTests() {
  console.log('🧪 Starting RBAC System Tests...\n');
  console.log('=' .repeat(60));
  
  let totalTests = 0;
  let passedTests = 0;
  let failedTests = 0;

  // Test each user type
  Object.entries(mockUsers).forEach(([userType, user]) => {
    console.log(`\n🔍 Testing ${userType} (${user.username}):`);
    
    // Test role determination
    const role = getUserRole(user);
    console.log(`  Role: ${role}`);
    totalTests++;
    
    // Test dashboard route
    const dashboard = getDashboardRoute(user);
    console.log(`  Dashboard Route: ${dashboard}`);
    totalTests++;
    
    // Test admin status
    const adminStatus = isAdmin(user);
    console.log(`  Is Admin: ${adminStatus}`);
    totalTests++;
    
    // Test super admin status
    const superAdminStatus = isSuperAdmin(user);
    console.log(`  Is Super Admin: ${superAdminStatus}`);
    totalTests++;
    
    // Test navigation access
    console.log(`  Navigation Access:`);
    navigationItems.forEach(item => {
      const hasAccess = hasAnyRole(user, item.userTypes);
      const accessStatus = hasAccess ? '✅ ALLOW' : '❌ DENY';
      console.log(`    ${item.name}: ${accessStatus}`);
      totalTests++;
      if (hasAccess === item.userTypes.includes(role) || isSuperAdmin(user)) {
        passedTests++;
      } else {
        failedTests++;
      }
    });
    
    passedTests += 4; // Role, dashboard, admin, super admin tests
  });

  console.log('\n' + '='.repeat(60));
  console.log('📊 RBAC Test Results:');
  console.log(`✅ Passed: ${passedTests}`);
  console.log(`❌ Failed: ${failedTests}`);
  console.log(`📈 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  if (failedTests === 0) {
    console.log('\n🎉 ALL TESTS PASSED! RBAC system is working correctly.');
  } else {
    console.log('\n⚠️ Some tests failed. Please review the implementation.');
  }
  
  console.log('\n' + '='.repeat(60));
}

// Run the tests
runRBACTests();
