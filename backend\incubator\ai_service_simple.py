"""
Simple AI service for incubator functionality using only Gemini
This avoids the complex AI configuration that was causing startup issues
"""
import os
import logging
from typing import Optional, Dict, Any
from django.conf import settings

logger = logging.getLogger(__name__)


class SimpleGeminiService:
    """Simple Gemini AI service without complex dependencies"""
    
    def __init__(self):
        self.api_key = None
        self.model = None
        self.is_available = False
        self._initialize()
    
    def _initialize(self):
        """Initialize Gemini service"""
        try:
            # Get API key from environment or settings
            self.api_key = os.getenv('GEMINI_API_KEY') or getattr(settings, 'GEMINI_API_KEY', None)
            
            if not self.api_key:
                logger.warning("🤖 Gemini API key not found. AI features will be disabled.")
                return
            
            # Import and configure Gemini
            import google.generativeai as genai
            genai.configure(api_key=self.api_key)
            
            # Initialize model
            self.model = genai.GenerativeModel('gemini-1.5-flash')
            self.is_available = True
            
            logger.info("✅ Simple Gemini service initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Gemini service: {e}")
            self.is_available = False
    
    def generate_business_analysis(self, business_idea: Dict[str, Any]) -> Optional[str]:
        """Generate AI analysis for a business idea"""
        if not self.is_available:
            return None
        
        try:
            prompt = self._create_analysis_prompt(business_idea)
            response = self.model.generate_content(prompt)
            return response.text
            
        except Exception as e:
            logger.error(f"❌ Failed to generate business analysis: {e}")
            return None
    
    def generate_business_suggestions(self, business_idea: Dict[str, Any]) -> Optional[str]:
        """Generate AI suggestions for improving a business idea"""
        if not self.is_available:
            return None
        
        try:
            prompt = self._create_suggestions_prompt(business_idea)
            response = self.model.generate_content(prompt)
            return response.text
            
        except Exception as e:
            logger.error(f"❌ Failed to generate business suggestions: {e}")
            return None
    
    def _create_analysis_prompt(self, business_idea: Dict[str, Any]) -> str:
        """Create prompt for business analysis"""
        return f"""
        Analyze this business idea and provide insights:
        
        Title: {business_idea.get('title', 'N/A')}
        Description: {business_idea.get('description', 'N/A')}
        Problem Statement: {business_idea.get('problem_statement', 'N/A')}
        Solution: {business_idea.get('solution_description', 'N/A')}
        Target Audience: {business_idea.get('target_audience', 'N/A')}
        Market Opportunity: {business_idea.get('market_opportunity', 'N/A')}
        Business Model: {business_idea.get('business_model', 'N/A')}
        Current Stage: {business_idea.get('current_stage', 'N/A')}
        
        Please provide:
        1. Market viability assessment
        2. Competitive analysis
        3. Risk assessment
        4. Growth potential
        5. Key success factors
        
        Keep the analysis concise and actionable.
        """
    
    def _create_suggestions_prompt(self, business_idea: Dict[str, Any]) -> str:
        """Create prompt for business suggestions"""
        return f"""
        Provide improvement suggestions for this business idea:
        
        Title: {business_idea.get('title', 'N/A')}
        Description: {business_idea.get('description', 'N/A')}
        Problem Statement: {business_idea.get('problem_statement', 'N/A')}
        Solution: {business_idea.get('solution_description', 'N/A')}
        Target Audience: {business_idea.get('target_audience', 'N/A')}
        
        Please suggest:
        1. Ways to refine the problem statement
        2. Improvements to the solution
        3. Better target audience definition
        4. Revenue model ideas
        5. Next steps for validation
        
        Provide practical, actionable suggestions.
        """


# Global service instance
_gemini_service = None


def get_gemini_service() -> SimpleGeminiService:
    """Get the global Gemini service instance"""
    global _gemini_service
    if _gemini_service is None:
        _gemini_service = SimpleGeminiService()
    return _gemini_service


def is_ai_available() -> bool:
    """Check if AI service is available"""
    service = get_gemini_service()
    return service.is_available


def generate_business_analysis(business_idea_data: Dict[str, Any]) -> Optional[str]:
    """Generate AI analysis for a business idea"""
    service = get_gemini_service()
    return service.generate_business_analysis(business_idea_data)


def generate_business_suggestions(business_idea_data: Dict[str, Any]) -> Optional[str]:
    """Generate AI suggestions for a business idea"""
    service = get_gemini_service()
    return service.generate_business_suggestions(business_idea_data)
