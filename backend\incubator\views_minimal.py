"""
Minimal views for basic CRUD functionality without complex dependencies
"""
from rest_framework import viewsets, permissions, status
from rest_framework.response import Response
from rest_framework.decorators import action
from django.contrib.auth.models import User
from .models_base import BusinessIdea, ProgressUpdate
from .ai_service_simple import generate_business_analysis, generate_business_suggestions, is_ai_available
from rest_framework import serializers
import logging

logger = logging.getLogger(__name__)


class SimpleBusinessIdeaSerializer(serializers.ModelSerializer):
    """Simple serializer without complex dependencies"""
    owner_username = serializers.CharField(source='owner.username', read_only=True)
    
    class Meta:
        model = BusinessIdea
        fields = [
            'id', 'title', 'description', 'problem_statement', 
            'solution_description', 'target_audience', 'market_opportunity',
            'business_model', 'current_stage', 'moderation_status',
            'created_at', 'updated_at', 'owner', 'owner_username'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'owner_username']

    def create(self, validated_data):
        # Set the owner to the current user
        validated_data['owner'] = self.context['request'].user
        return super().create(validated_data)


class SimpleProgressUpdateSerializer(serializers.ModelSerializer):
    """Simple serializer for progress updates"""
    
    class Meta:
        model = ProgressUpdate
        fields = [
            'id', 'business_idea', 'title', 'description', 
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class SimpleBusinessIdeaViewSet(viewsets.ModelViewSet):
    """Simple business idea viewset without complex dependencies"""
    serializer_class = SimpleBusinessIdeaSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # Return business ideas for the current user
        return BusinessIdea.objects.filter(owner=self.request.user).order_by('-created_at')

    def perform_create(self, serializer):
        serializer.save(owner=self.request.user)

    @action(detail=True, methods=['post'])
    def ai_analysis(self, request, pk=None):
        """Generate AI analysis for a business idea"""
        try:
            business_idea = self.get_object()

            if not is_ai_available():
                return Response({
                    'error': 'AI service is not available',
                    'analysis': None
                }, status=status.HTTP_503_SERVICE_UNAVAILABLE)

            # Convert business idea to dict for AI analysis
            business_data = {
                'title': business_idea.title,
                'description': business_idea.description,
                'problem_statement': business_idea.problem_statement,
                'solution_description': business_idea.solution_description,
                'target_audience': business_idea.target_audience,
                'market_opportunity': business_idea.market_opportunity,
                'business_model': business_idea.business_model,
                'current_stage': business_idea.current_stage,
            }

            analysis = generate_business_analysis(business_data)

            if analysis:
                return Response({
                    'success': True,
                    'analysis': analysis
                })
            else:
                return Response({
                    'error': 'Failed to generate AI analysis',
                    'analysis': None
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            logger.error(f"Error in AI analysis: {e}")
            return Response({
                'error': 'Internal server error',
                'analysis': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['post'])
    def ai_suggestions(self, request, pk=None):
        """Generate AI suggestions for improving a business idea"""
        try:
            business_idea = self.get_object()

            if not is_ai_available():
                return Response({
                    'error': 'AI service is not available',
                    'suggestions': None
                }, status=status.HTTP_503_SERVICE_UNAVAILABLE)

            # Convert business idea to dict for AI suggestions
            business_data = {
                'title': business_idea.title,
                'description': business_idea.description,
                'problem_statement': business_idea.problem_statement,
                'solution_description': business_idea.solution_description,
                'target_audience': business_idea.target_audience,
            }

            suggestions = generate_business_suggestions(business_data)

            if suggestions:
                return Response({
                    'success': True,
                    'suggestions': suggestions
                })
            else:
                return Response({
                    'error': 'Failed to generate AI suggestions',
                    'suggestions': None
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            logger.error(f"Error in AI suggestions: {e}")
            return Response({
                'error': 'Internal server error',
                'suggestions': None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def ai_status(self, request):
        """Check AI service availability"""
        return Response({
            'ai_available': is_ai_available(),
            'service': 'gemini' if is_ai_available() else 'none'
        })


class SimpleProgressUpdateViewSet(viewsets.ModelViewSet):
    """Simple progress update viewset"""
    serializer_class = SimpleProgressUpdateSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        # Return progress updates for business ideas owned by the current user
        return ProgressUpdate.objects.filter(
            business_idea__owner=self.request.user
        ).order_by('-created_at')
