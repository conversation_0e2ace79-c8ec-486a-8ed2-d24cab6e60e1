"""
AI Configuration Helper
Provides utilities for setting up and validating AI service configuration
"""

import os
import logging
from typing import Dict, Any, Optional
from django.conf import settings
from .models import AIConfiguration

logger = logging.getLogger(__name__)


class AIConfigurationHelper:
    """Helper class for AI configuration management"""
    
    @staticmethod
    def validate_gemini_api_key(api_key: str) -> Dict[str, Any]:
        """Validate a Gemini API key"""
        if not api_key:
            return {
                'valid': False,
                'error': 'API key is empty',
                'suggestion': 'Please provide a valid Gemini API key'
            }
        
        if len(api_key) < 30:
            return {
                'valid': False,
                'error': 'API key appears to be too short',
                'suggestion': 'Gemini API keys are typically longer than 30 characters'
            }
        
        if not api_key.startswith('AIza'):
            return {
                'valid': False,
                'error': 'API key format appears incorrect',
                'suggestion': 'Gemini API keys typically start with "AIza"'
            }
        
        # Test the API key by trying to initialize Gemini
        try:
            import google.generativeai as genai
            genai.configure(api_key=api_key)
            
            # Try to create a model instance
            model = genai.GenerativeModel('gemini-1.5-flash')
            
            # Try a simple test
            response = model.generate_content(
                "Respond with exactly: TEST_OK",
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=10,
                    temperature=0.1,
                )
            )
            
            if response and hasattr(response, 'text') and 'TEST_OK' in response.text:
                return {
                    'valid': True,
                    'message': 'API key is valid and working',
                    'test_response': response.text.strip()
                }
            else:
                return {
                    'valid': False,
                    'error': 'API key validation test failed',
                    'suggestion': 'The API key may be valid but the service is not responding correctly'
                }
                
        except Exception as e:
            error_str = str(e).lower()
            if 'api key' in error_str or 'authentication' in error_str:
                return {
                    'valid': False,
                    'error': 'API key authentication failed',
                    'suggestion': 'Please check that your API key is correct and has proper permissions',
                    'details': str(e)
                }
            elif 'quota' in error_str or 'rate limit' in error_str:
                return {
                    'valid': False,
                    'error': 'API quota or rate limit exceeded',
                    'suggestion': 'Your API key may be valid but you have exceeded usage limits',
                    'details': str(e)
                }
            else:
                return {
                    'valid': False,
                    'error': f'API key validation failed: {str(e)}',
                    'suggestion': 'Please check your API key and network connection'
                }
    
    @staticmethod
    def get_configuration_status() -> Dict[str, Any]:
        """Get current AI configuration status"""
        status = {
            'gemini_configured': False,
            'api_key_source': None,
            'api_key_valid': False,
            'database_config_exists': False,
            'environment_config_exists': False,
            'recommendations': []
        }
        
        # Check database configuration
        try:
            db_config = AIConfiguration.objects.filter(
                provider='gemini', 
                key='api_key',
                is_active=True
            ).first()
            
            if db_config:
                status['database_config_exists'] = True
                status['api_key_source'] = 'database'
                # Don't validate here to avoid exposing the key
        except Exception as e:
            logger.warning(f"Could not check database configuration: {e}")
        
        # Check environment configuration
        env_key = os.getenv('GEMINI_API_KEY')
        settings_key = getattr(settings, 'GEMINI_API_KEY', None)
        
        if env_key:
            status['environment_config_exists'] = True
            if not status['api_key_source']:
                status['api_key_source'] = 'environment'
        elif settings_key:
            status['environment_config_exists'] = True
            if not status['api_key_source']:
                status['api_key_source'] = 'settings'
        
        # Provide recommendations
        if not status['database_config_exists'] and not status['environment_config_exists']:
            status['recommendations'].append({
                'type': 'error',
                'message': 'No Gemini API key configured',
                'action': 'Set GEMINI_API_KEY environment variable or configure in database'
            })
        
        if status['environment_config_exists'] and not status['database_config_exists']:
            status['recommendations'].append({
                'type': 'info',
                'message': 'Using environment configuration',
                'action': 'Consider moving to database configuration for better management'
            })
        
        return status
    
    @staticmethod
    def setup_database_configuration(api_key: str, user_id: Optional[int] = None) -> Dict[str, Any]:
        """Set up database configuration for Gemini API"""
        try:
            # Validate the API key first
            validation = AIConfigurationHelper.validate_gemini_api_key(api_key)
            if not validation['valid']:
                return {
                    'success': False,
                    'error': 'Invalid API key',
                    'validation': validation
                }
            
            # Get or create the configuration
            config, created = AIConfiguration.objects.get_or_create(
                provider='gemini',
                key='api_key',
                defaults={
                    'value': api_key,
                    'config_type': 'api_key',
                    'is_active': True,
                    'is_sensitive': True,
                    'description': 'Gemini API key for AI services',
                    'created_by_id': user_id,
                    'updated_by_id': user_id
                }
            )
            
            if not created:
                # Update existing configuration
                config.value = api_key
                config.is_active = True
                config.updated_by_id = user_id
                config.save()
            
            return {
                'success': True,
                'message': 'Gemini API configuration saved successfully',
                'created': created,
                'config_id': config.id
            }
            
        except Exception as e:
            logger.error(f"Failed to setup database configuration: {e}")
            return {
                'success': False,
                'error': f'Failed to save configuration: {str(e)}'
            }
    
    @staticmethod
    def get_setup_instructions() -> Dict[str, Any]:
        """Get setup instructions for AI configuration"""
        return {
            'title': 'Gemini AI Configuration Setup',
            'steps': [
                {
                    'step': 1,
                    'title': 'Get Gemini API Key',
                    'description': 'Visit Google AI Studio to get your free Gemini API key',
                    'url': 'https://makersuite.google.com/app/apikey',
                    'instructions': [
                        'Go to https://makersuite.google.com/app/apikey',
                        'Sign in with your Google account',
                        'Click "Create API Key"',
                        'Copy the generated API key'
                    ]
                },
                {
                    'step': 2,
                    'title': 'Configure API Key',
                    'description': 'Set up the API key in your environment or database',
                    'options': [
                        {
                            'method': 'Environment Variable (Recommended)',
                            'instructions': [
                                'Create a .env file in your backend directory',
                                'Add: GEMINI_API_KEY=your_api_key_here',
                                'Restart the Django server'
                            ]
                        },
                        {
                            'method': 'Database Configuration',
                            'instructions': [
                                'Use the Super Admin panel',
                                'Go to AI Configuration',
                                'Add new configuration with provider=gemini, key=api_key'
                            ]
                        }
                    ]
                },
                {
                    'step': 3,
                    'title': 'Verify Configuration',
                    'description': 'Test that the AI service is working',
                    'instructions': [
                        'Go to /api/ai/status/ to check AI service status',
                        'Try the AI chat functionality',
                        'Check the automatic AI endpoints'
                    ]
                }
            ],
            'troubleshooting': [
                {
                    'issue': 'API key authentication failed',
                    'solutions': [
                        'Verify the API key is correct',
                        'Check that the API key has proper permissions',
                        'Ensure you have not exceeded usage quotas'
                    ]
                },
                {
                    'issue': 'AI service not available',
                    'solutions': [
                        'Check that GEMINI_API_KEY is set correctly',
                        'Restart the Django server',
                        'Check the Django logs for error messages'
                    ]
                }
            ]
        }
