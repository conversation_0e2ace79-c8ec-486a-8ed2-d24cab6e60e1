/**
 * Templates Page
 * Main page for browsing and managing business plan templates
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { FileText, Search, Filter, Star, Download, Eye, Plus } from 'lucide-react';
import { useAppSelector } from '../../store/hooks';

interface Template {
  id: number;
  title: string;
  description: string;
  category: string;
  industry: string;
  rating: number;
  downloads: number;
  preview_image?: string;
  is_premium: boolean;
  created_at: string;
  tags: string[];
}

const TemplatesPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAppSelector(state => state.auth);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedIndustry, setSelectedIndustry] = useState('all');

  const categories = ['all', 'startup', 'ecommerce', 'saas', 'retail', 'consulting', 'manufacturing'];
  const industries = ['all', 'technology', 'healthcare', 'finance', 'education', 'food', 'real_estate'];

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      // const response = await fetch('/api/templates/', {
      //   headers: {
      //     'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      //   }
      // });
      // const data = await response.json();
      
      // Mock data for now
      const mockTemplates: Template[] = [
        {
          id: 1,
          title: 'Tech Startup Business Plan',
          description: 'Comprehensive template for technology startups with funding sections',
          category: 'startup',
          industry: 'technology',
          rating: 4.8,
          downloads: 1250,
          is_premium: false,
          created_at: '2024-01-15',
          tags: ['startup', 'tech', 'funding', 'mvp']
        },
        {
          id: 2,
          title: 'E-commerce Business Plan',
          description: 'Perfect for online retail businesses and marketplaces',
          category: 'ecommerce',
          industry: 'retail',
          rating: 4.6,
          downloads: 890,
          is_premium: true,
          created_at: '2024-01-10',
          tags: ['ecommerce', 'retail', 'online', 'marketplace']
        },
        {
          id: 3,
          title: 'SaaS Business Plan Template',
          description: 'Tailored for Software as a Service companies',
          category: 'saas',
          industry: 'technology',
          rating: 4.9,
          downloads: 2100,
          is_premium: false,
          created_at: '2024-01-08',
          tags: ['saas', 'software', 'subscription', 'b2b']
        }
      ];
      
      setTemplates(mockTemplates);
    } catch (err) {
      setError('Failed to fetch templates');
      console.error('Error fetching templates:', err);
    } finally {
      setLoading(false);
    }
  };

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    const matchesIndustry = selectedIndustry === 'all' || template.industry === selectedIndustry;
    
    return matchesSearch && matchesCategory && matchesIndustry;
  });

  const handleUseTemplate = (templateId: number) => {
    // TODO: Navigate to business plan creation with template
    console.log('Use template:', templateId);
  };

  const handlePreview = (templateId: number) => {
    // TODO: Open template preview modal
    console.log('Preview template:', templateId);
  };

  const handleDownload = (templateId: number) => {
    // TODO: Download template
    console.log('Download template:', templateId);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3, 4, 5, 6].map(i => (
                <div key={i} className="bg-white rounded-lg shadow p-6">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                  <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-red-900 mb-2">Error</h2>
            <p className="text-red-700">{error}</p>
            <button
              onClick={fetchTemplates}
              className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {t('templates.title', 'Business Plan Templates')}
          </h1>
          <p className="text-gray-600">
            {t('templates.subtitle', 'Choose from our collection of professional business plan templates')}
          </p>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder={t('templates.search', 'Search templates...')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Category Filter */}
            <div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {t(`templates.categories.${category}`, category.charAt(0).toUpperCase() + category.slice(1))}
                  </option>
                ))}
              </select>
            </div>

            {/* Industry Filter */}
            <div>
              <select
                value={selectedIndustry}
                onChange={(e) => setSelectedIndustry(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {industries.map(industry => (
                  <option key={industry} value={industry}>
                    {t(`templates.industries.${industry}`, industry.charAt(0).toUpperCase() + industry.slice(1))}
                  </option>
                ))}
              </select>
            </div>

            {/* Clear Filters */}
            <button
              onClick={() => {
                setSearchTerm('');
                setSelectedCategory('all');
                setSelectedIndustry('all');
              }}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              {t('templates.clearFilters', 'Clear Filters')}
            </button>
          </div>
        </div>

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-gray-600">
            {t('templates.resultsCount', 'Showing {{count}} templates', { count: filteredTemplates.length })}
          </p>
        </div>

        {/* Templates Grid */}
        {filteredTemplates.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {t('templates.noResults', 'No Templates Found')}
            </h3>
            <p className="text-gray-600 mb-6">
              {t('templates.tryDifferentFilters', 'Try adjusting your search or filters')}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTemplates.map((template) => (
              <div key={template.id} className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow">
                <div className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">
                      {template.title}
                    </h3>
                    {template.is_premium && (
                      <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                        {t('templates.premium', 'Premium')}
                      </span>
                    )}
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                    {template.description}
                  </p>
                  
                  {/* Tags */}
                  <div className="flex flex-wrap gap-1 mb-4">
                    {template.tags.slice(0, 3).map((tag, index) => (
                      <span key={index} className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                        {tag}
                      </span>
                    ))}
                    {template.tags.length > 3 && (
                      <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                        +{template.tags.length - 3}
                      </span>
                    )}
                  </div>
                  
                  {/* Stats */}
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                    <div className="flex items-center">
                      <Star className="w-4 h-4 text-yellow-400 mr-1" />
                      <span>{template.rating}</span>
                    </div>
                    <div className="flex items-center">
                      <Download className="w-4 h-4 mr-1" />
                      <span>{template.downloads.toLocaleString()}</span>
                    </div>
                  </div>
                  
                  {/* Actions */}
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleUseTemplate(template.id)}
                      className="flex-1 flex items-center justify-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <Plus className="w-4 h-4 mr-1" />
                      {t('templates.use', 'Use Template')}
                    </button>
                    <button
                      onClick={() => handlePreview(template.id)}
                      className="p-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                      title={t('templates.preview', 'Preview')}
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default TemplatesPage;
