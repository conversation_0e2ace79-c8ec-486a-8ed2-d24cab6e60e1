// Browser Console Testing Script
// Copy and paste this into your browser's developer console (F12)

console.log('🚀 Starting Browser Console Tests...');

// Test users
const testUsers = {
  user: { username: 'testuser', password: 'testpass123', expectedRole: 'user' },
  mentor: { username: 'testmentor', password: 'testpass123', expectedRole: 'mentor' },
  investor: { username: 'testinvestor', password: 'testpass123', expectedRole: 'investor' },
  moderator: { username: 'testmoderator', password: 'testpass123', expectedRole: 'moderator' },
  admin: { username: 'testadmin', password: 'testpass123', expectedRole: 'admin' },
  superadmin: { username: 'testsuperadmin', password: 'testpass123', expectedRole: 'super_admin' }
};

// Test authentication for a specific user
async function testLogin(userType) {
  const userData = testUsers[userType];
  if (!userData) {
    console.error(`❌ Unknown user type: ${userType}`);
    return;
  }

  console.log(`🧪 Testing login for ${userType}...`);
  
  try {
    const response = await fetch('http://localhost:8000/api/auth/token/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: userData.username,
        password: userData.password
      })
    });

    if (response.ok) {
      const data = await response.json();
      console.log(`✅ ${userType} login successful!`);
      console.log(`   Role: ${data.user.user_role}`);
      console.log(`   Permissions: ${data.user.role_permissions?.join(', ')}`);
      console.log(`   Token received: ${!!data.access}`);
      
      // Store token for further testing
      window.testToken = data.access;
      window.testUser = data.user;
      
      return data;
    } else {
      const errorData = await response.json();
      console.error(`❌ ${userType} login failed:`, errorData);
    }
  } catch (error) {
    console.error(`❌ Error testing ${userType}:`, error);
  }
}

// Test all users
async function testAllUsers() {
  console.log('🧪 Testing all user types...');
  
  for (const userType of Object.keys(testUsers)) {
    await testLogin(userType);
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between tests
  }
  
  console.log('✅ All user tests completed!');
}

// Test current page access
function testCurrentPage() {
  console.log(`🔍 Testing current page: ${window.location.pathname}`);
  
  if (window.testToken) {
    console.log(`✅ Authenticated as: ${window.testUser?.username} (${window.testUser?.user_role})`);
    console.log(`📍 Current URL: ${window.location.href}`);
    console.log(`🎯 Expected for this role: Page should be accessible`);
  } else {
    console.log(`⚠️  No authentication token available. Run testLogin('userType') first.`);
  }
}

// Test navigation to different routes
async function testNavigation(route) {
  console.log(`🧭 Testing navigation to: ${route}`);
  
  if (!window.testToken) {
    console.log(`⚠️  Please login first using testLogin('userType')`);
    return;
  }

  try {
    const response = await fetch(`http://localhost:3000${route}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${window.testToken}`,
        'Accept': 'text/html'
      }
    });

    if (response.ok) {
      console.log(`✅ Route ${route} is accessible`);
      console.log(`   Status: ${response.status}`);
      console.log(`   You can navigate to: http://localhost:3000${route}`);
    } else {
      console.log(`⚠️  Route ${route} returned status: ${response.status}`);
    }
  } catch (error) {
    console.error(`❌ Error testing route ${route}:`, error);
  }
}

// Quick test functions
window.quickTest = {
  // Test specific user login
  login: testLogin,
  
  // Test all users
  all: testAllUsers,
  
  // Test current page
  page: testCurrentPage,
  
  // Test navigation
  nav: testNavigation,
  
  // Quick login shortcuts
  user: () => testLogin('user'),
  mentor: () => testLogin('mentor'),
  investor: () => testLogin('investor'),
  moderator: () => testLogin('moderator'),
  admin: () => testLogin('admin'),
  superadmin: () => testLogin('superadmin'),
  
  // Test common routes
  dashboard: () => testNavigation('/dashboard'),
  profile: () => testNavigation('/profile'),
  settings: () => testNavigation('/settings'),
  adminDash: () => testNavigation('/admin'),
  superAdminDash: () => testNavigation('/super_admin'),
  
  // Show available commands
  help: () => {
    console.log(`
🧪 Available Test Commands:

📝 Login Tests:
   quickTest.user()        - Test regular user login
   quickTest.mentor()      - Test mentor login  
   quickTest.investor()    - Test investor login
   quickTest.moderator()   - Test moderator login
   quickTest.admin()       - Test admin login
   quickTest.superadmin()  - Test super admin login
   quickTest.all()         - Test all user types

🧭 Navigation Tests:
   quickTest.dashboard()   - Test dashboard access
   quickTest.profile()     - Test profile access
   quickTest.settings()    - Test settings access
   quickTest.adminDash()   - Test admin dashboard
   quickTest.superAdminDash() - Test super admin dashboard
   quickTest.nav('/path')  - Test custom route

🔍 Status Tests:
   quickTest.page()        - Test current page access
   quickTest.help()        - Show this help

📋 Example Usage:
   quickTest.user()        // Login as user
   quickTest.dashboard()   // Test dashboard access
   quickTest.nav('/dashboard/business-ideas') // Test custom route
    `);
  }
};

// Auto-run help on load
console.log(`
🎉 Browser Console Testing Ready!

Type 'quickTest.help()' to see available commands.

Quick start:
1. quickTest.user()      - Login as regular user
2. quickTest.dashboard() - Test dashboard access
3. quickTest.page()      - Check current page status

All test functions are available under 'quickTest.*'
`);

// Make test users available globally for manual testing
window.testUsers = testUsers;
