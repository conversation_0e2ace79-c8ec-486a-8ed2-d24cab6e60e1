import { LoginPage } from '../support/page-objects/LoginPage'
import { DashboardPage } from '../support/page-objects/DashboardPage'

describe('Authentication Flow', () => {
  let loginPage: LoginPage
  let dashboardPage: DashboardPage

  beforeEach(() => {
    loginPage = new LoginPage()
    dashboardPage = new DashboardPage()
    
    // Clear any existing authentication
    cy.clearLocalStorage()
    cy.clearCookies()
  })

  describe('Login Functionality', () => {
    it('should display login form correctly', () => {
      loginPage.visit()
        .shouldBeAccessible()
        .shouldSupportKeyboardNavigation()
    })

    it('should validate email format', () => {
      loginPage.visit()
        .shouldValidateEmailFormat()
    })

    it('should require password', () => {
      loginPage.visit()
        .shouldRequirePassword()
    })

    it('should handle invalid credentials', () => {
      loginPage.visit()
        .shouldHandleInvalidCredentials()
    })

    it('should login successfully with valid credentials - User', () => {
      cy.intercept('POST', '**/api/auth/login/', { fixture: 'login-response.json' }).as('loginRequest')
      cy.intercept('GET', '**/api/auth/user/', { fixture: 'user.json' }).as('getUserRequest')

      loginPage.visit()
        .login('<EMAIL>', 'password123')

      cy.wait('@loginRequest')
      cy.wait('@getUserRequest')

      loginPage.shouldRedirectToDashboard('user')
      
      // Verify authentication state
      cy.window().its('localStorage').should('have.property', 'token')
      
      dashboardPage.shouldBeVisible()
        .shouldShowUserProfile('Test User')
    })

    it('should login successfully with valid credentials - Admin', () => {
      cy.intercept('POST', '**/api/auth/login/', { 
        body: { 
          ...require('../fixtures/login-response.json'),
          user: { ...require('../fixtures/admin.json') }
        }
      }).as('adminLoginRequest')
      cy.intercept('GET', '**/api/auth/user/', { fixture: 'admin.json' }).as('getAdminRequest')

      loginPage.visit()
        .login('<EMAIL>', 'admin123')

      cy.wait('@adminLoginRequest')
      cy.wait('@getAdminRequest')

      loginPage.shouldRedirectToDashboard('admin')
      
      dashboardPage.shouldBeVisible()
        .shouldShowUserProfile('Admin User')
        .shouldShowCorrectRoleBasedContent('admin')
    })

    it('should remember user when "Remember Me" is checked', () => {
      cy.intercept('POST', '**/api/auth/login/', { fixture: 'login-response.json' }).as('loginRequest')

      loginPage.visit()
        .login('<EMAIL>', 'password123', true)

      cy.wait('@loginRequest')

      // Check that remember me preference is stored
      cy.window().its('localStorage').should('have.property', 'rememberMe', 'true')
    })

    it('should support language switching on login page', () => {
      loginPage.visit()
        .switchLanguage('ar')

      // Check that language changed
      cy.get('html').should('have.attr', 'lang', 'ar')
      cy.get('body').should('have.attr', 'dir', 'rtl')

      // Switch back to English
      loginPage.switchLanguage('en')
      cy.get('html').should('have.attr', 'lang', 'en')
      cy.get('body').should('have.attr', 'dir', 'ltr')
    })

    it('should not expose password in DOM', () => {
      loginPage.visit()
        .shouldNotExposePasswordInDOM()
    })

    it('should load quickly', () => {
      loginPage.shouldLoadQuickly()
    })
  })

  describe('Logout Functionality', () => {
    beforeEach(() => {
      // Login first
      cy.intercept('POST', '**/api/auth/login/', { fixture: 'login-response.json' }).as('loginRequest')
      cy.intercept('GET', '**/api/auth/user/', { fixture: 'user.json' }).as('getUserRequest')
      
      loginPage.visit()
        .login('<EMAIL>', 'password123')
      
      cy.wait('@loginRequest')
      cy.wait('@getUserRequest')
    })

    it('should logout successfully', () => {
      cy.intercept('POST', '**/api/auth/logout/', { statusCode: 200 }).as('logoutRequest')

      dashboardPage.logout()

      cy.wait('@logoutRequest')

      // Should redirect to login page
      cy.url().should('include', '/login')

      // Should clear authentication state
      cy.window().its('localStorage').should('not.have.property', 'token')
    })

    it('should handle logout errors gracefully', () => {
      cy.intercept('POST', '**/api/auth/logout/', { statusCode: 500 }).as('logoutError')

      dashboardPage.logout()

      cy.wait('@logoutError')

      // Should still redirect to login even if logout API fails
      cy.url().should('include', '/login')
      cy.window().its('localStorage').should('not.have.property', 'token')
    })
  })

  describe('Session Management', () => {
    it('should redirect to login when accessing protected route without authentication', () => {
      dashboardPage.shouldRequireAuthentication()
    })

    it('should handle expired tokens', () => {
      // Set expired token
      cy.window().then((win) => {
        win.localStorage.setItem('token', 'expired-token')
      })

      cy.intercept('GET', '**/api/auth/user/', { statusCode: 401 }).as('unauthorizedRequest')

      dashboardPage.visit()

      cy.wait('@unauthorizedRequest')

      // Should redirect to login
      cy.url().should('include', '/login')
    })

    it('should refresh token automatically', () => {
      // Set token that will expire soon
      cy.window().then((win) => {
        win.localStorage.setItem('token', 'expiring-token')
        win.localStorage.setItem('refreshToken', 'valid-refresh-token')
      })

      cy.intercept('POST', '**/api/auth/refresh/', { 
        body: { access: 'new-access-token' }
      }).as('refreshRequest')

      cy.intercept('GET', '**/api/auth/user/', { fixture: 'user.json' }).as('getUserRequest')

      dashboardPage.visit()

      cy.wait('@refreshRequest')
      cy.wait('@getUserRequest')

      // Should stay on dashboard with new token
      cy.url().should('include', '/dashboard')
      cy.window().its('localStorage.token').should('eq', 'new-access-token')
    })
  })

  describe('Security Tests', () => {
    it('should prevent XSS attacks in login form', () => {
      const xssPayload = '<script>alert("XSS")</script>'
      
      loginPage.visit()
        .enterEmail(xssPayload)
        .enterPassword(xssPayload)

      // Should not execute script
      cy.window().then((win) => {
        expect(win.alert).to.be.undefined
      })

      // Should escape HTML in error messages
      cy.get('body').should('not.contain.html', '<script>')
    })

    it('should implement CSRF protection', () => {
      cy.intercept('POST', '**/api/auth/login/', (req) => {
        // Check for CSRF token in headers
        expect(req.headers).to.have.property('x-csrftoken')
      }).as('csrfCheck')

      loginPage.visit()
        .login('<EMAIL>', 'password123')

      cy.wait('@csrfCheck')
    })

    it('should rate limit login attempts', () => {
      // Simulate multiple failed login attempts
      for (let i = 0; i < 5; i++) {
        cy.intercept('POST', '**/api/auth/login/', { statusCode: 401 }).as(`failedLogin${i}`)
        
        loginPage.visit()
          .login('<EMAIL>', 'wrongpassword')
        
        cy.wait(`@failedLogin${i}`)
      }

      // Next attempt should be rate limited
      cy.intercept('POST', '**/api/auth/login/', { statusCode: 429 }).as('rateLimited')
      
      loginPage.visit()
        .login('<EMAIL>', 'wrongpassword')

      cy.wait('@rateLimited')
      
      loginPage.shouldShowErrorMessage('Too many login attempts')
    })
  })

  describe('Accessibility Tests', () => {
    it('should be fully accessible', () => {
      loginPage.visit()
        .shouldBeAccessible()
    })

    it('should support screen readers', () => {
      loginPage.visit()

      // Check ARIA labels and descriptions
      cy.get('[data-testid="email-input"]')
        .should('have.attr', 'aria-label')
        .or('have.attr', 'aria-labelledby')

      cy.get('[data-testid="password-input"]')
        .should('have.attr', 'aria-label')
        .or('have.attr', 'aria-labelledby')

      // Check form validation messages
      loginPage.enterEmail('invalid-email')
        .clickLogin()

      cy.get('[data-testid="email-input"]')
        .should('have.attr', 'aria-invalid', 'true')
        .and('have.attr', 'aria-describedby')
    })

    it('should have proper focus management', () => {
      loginPage.visit()

      // Focus should start on first input
      cy.get('[data-testid="email-input"]').should('be.focused')

      // Tab order should be logical
      loginPage.shouldSupportKeyboardNavigation()
    })
  })
})
