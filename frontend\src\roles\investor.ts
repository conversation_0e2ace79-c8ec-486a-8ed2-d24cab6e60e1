/**
 * INVESTOR ROLE CONFIGURATION
 * Dedicated file for investor role - no duplicates, single source of truth
 */

import { UserRole, PermissionLevel } from '../utils/unifiedRoleManager';

export const INVESTOR_ROLE: UserRole = 'investor';

export const INVESTOR_PERMISSIONS: PermissionLevel[] = ['read', 'write'];

export const INVESTOR_NAVIGATION_ITEMS = [
  {
    id: 'dashboard',
    name: 'dashboard.title',
    path: '/dashboard',
    icon: 'Home',
    category: 'main' as const
  },
  {
    id: 'investor-dashboard',
    name: 'investor.dashboard.title',
    path: '/dashboard/investor',
    icon: 'TrendingUp',
    category: 'main' as const
  },
  {
    id: 'investments',
    name: 'investments.title',
    path: '/dashboard/investments',
    icon: 'DollarSign',
    category: 'main' as const
  },
  {
    id: 'investment-opportunities',
    name: 'investment.opportunities.title',
    path: '/dashboard/investment/opportunities',
    icon: 'TrendingUp',
    category: 'main' as const
  },
  {
    id: 'portfolio',
    name: 'investment.portfolio.title',
    path: '/dashboard/investment/portfolio',
    icon: 'PieChart',
    category: 'main' as const
  },
  {
    id: 'due-diligence',
    name: 'investment.dueDiligence.title',
    path: '/dashboard/investments/due-diligence',
    icon: 'Search',
    category: 'main' as const
  },
  {
    id: 'investor-analytics',
    name: 'investor.analytics.title',
    path: '/dashboard/investments/analytics',
    icon: 'BarChart3',
    category: 'main' as const
  },
  {
    id: 'investor-profile',
    name: 'investor.profile.title',
    path: '/dashboard/investments/profile',
    icon: 'User',
    category: 'main' as const
  },
  {
    id: 'business-ideas',
    name: 'businessIdeas.title',
    path: '/dashboard/business-ideas',
    icon: 'Lightbulb',
    category: 'content' as const
  },
  {
    id: 'forums',
    name: 'forums.title',
    path: '/dashboard/forums',
    icon: 'MessageSquare',
    category: 'content' as const
  },
  {
    id: 'resources',
    name: 'resources.title',
    path: '/dashboard/resources',
    icon: 'BookOpen',
    category: 'content' as const
  },
  {
    id: 'ai-assistant',
    name: 'ai.assistant.title',
    path: '/dashboard/ai',
    icon: 'Bot',
    category: 'ai' as const
  },
  {
    id: 'profile',
    name: 'profile.title',
    path: '/profile',
    icon: 'User',
    category: 'main' as const
  },
  {
    id: 'settings',
    name: 'settings.title',
    path: '/settings',
    icon: 'Settings',
    category: 'main' as const
  }
];

export const INVESTOR_ROUTES = [
  '/dashboard',
  '/dashboard/investor',
  '/dashboard/investments',
  '/dashboard/investment/opportunities',
  '/dashboard/investment/portfolio',
  '/dashboard/investments/due-diligence',
  '/dashboard/investments/analytics',
  '/dashboard/investments/profile',
  '/dashboard/business-ideas',
  '/dashboard/forums',
  '/dashboard/resources',
  '/dashboard/ai',
  '/profile',
  '/settings'
];

export const INVESTOR_DASHBOARD_CONFIG = {
  defaultRoute: '/dashboard/investments',
  welcomeMessage: 'Welcome to your investor dashboard',
  features: [
    'investment_opportunities',
    'portfolio_management',
    'due_diligence',
    'investment_analytics',
    'business_idea_evaluation',
    'forum_participation',
    'resource_access',
    'ai_assistant'
  ]
};

/**
 * Check if a user object represents an investor
 */
export function isInvestorRole(user: any): boolean {
  if (!user) return false;
  
  // Explicit investor role
  if (user.user_role === 'investor') return true;
  
  // Check profile-based role
  if (user.profile?.primary_role?.name === 'investor') return true;
  
  // Check active roles
  if (user.profile?.active_roles) {
    return user.profile.active_roles.some((role: any) => role.name === 'investor');
  }
  
  return false;
}

/**
 * Get investor-specific dashboard route
 */
export function getInvestorDashboardRoute(): string {
  return INVESTOR_DASHBOARD_CONFIG.defaultRoute;
}

/**
 * Check if investor can access specific investment feature
 */
export function canAccessInvestmentFeature(user: any, feature: string): boolean {
  if (!isInvestorRole(user)) return false;
  
  return INVESTOR_DASHBOARD_CONFIG.features.includes(feature);
}

export default {
  role: INVESTOR_ROLE,
  permissions: INVESTOR_PERMISSIONS,
  navigationItems: INVESTOR_NAVIGATION_ITEMS,
  routes: INVESTOR_ROUTES,
  dashboardConfig: INVESTOR_DASHBOARD_CONFIG,
  isRole: isInvestorRole,
  getDashboardRoute: getInvestorDashboardRoute,
  canAccessFeature: canAccessInvestmentFeature
};
