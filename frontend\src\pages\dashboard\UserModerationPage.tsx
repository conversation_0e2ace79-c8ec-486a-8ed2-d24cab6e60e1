import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import Button from '../../components/ui/Button';
import { Badge } from '../../components/ui/badge';
import { Input } from '../../components/ui/input';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { User, Shield, AlertTriangle, Ban, CheckCircle, Search, Filter, Mail, Calendar, Eye } from 'lucide-react';

interface UserAccount {
  id: string;
  name: string;
  email: string;
  role: 'user' | 'mentor' | 'investor' | 'moderator' | 'admin';
  status: 'active' | 'suspended' | 'banned' | 'pending_review';
  joinDate: string;
  lastActive: string;
  totalPosts: number;
  totalComments: number;
  reportsReceived: number;
  reportsSubmitted: number;
  warningsIssued: number;
  profileCompleteness: number;
  verificationStatus: 'verified' | 'unverified' | 'pending';
  suspensionReason?: string;
  suspensionExpiry?: string;
}

interface ModerationAction {
  id: string;
  userId: string;
  userName: string;
  action: 'warning' | 'suspension' | 'ban' | 'unban' | 'verification';
  reason: string;
  duration?: string;
  moderatorId: string;
  moderatorName: string;
  timestamp: string;
  notes?: string;
}

const UserModerationPage: React.FC = () => {
  const [users, setUsers] = useState<UserAccount[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserAccount[]>([]);
  const [moderationActions, setModerationActions] = useState<ModerationAction[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [roleFilter, setRoleFilter] = useState('all');
  const [activeTab, setActiveTab] = useState('users');

  // Load real user moderation data from API
  useEffect(() => {
    // TODO: Replace with actual API call to fetch user accounts for moderation
    // const fetchUsers = async () => {
    //   try {
    //     const response = await api.get('/admin/user-moderation/');
    //     setUsers(response.data.results);
    //   } catch (error) {
    //     console.error('Failed to fetch users:', error);
    //   }
    // };
    // fetchUsers();

    // Mock data for now
    const mockData: UserAccount[] = [
      {
        id: '2',
        username: 'sarah_johnson',
        email: '<EMAIL>',
        firstName: 'Sarah',
        lastName: 'Johnson',
        role: 'user',
        status: 'active',
        joinDate: '2023-03-20',
        lastActive: '2024-01-14T16:45:00Z',
        totalPosts: 45,
        totalComments: 123,
        reportsReceived: 0,
        reportsSubmitted: 8,
        warningsIssued: 0,
        profileCompleteness: 95,
        verificationStatus: 'verified'
      },
      {
        id: '3',
        name: 'Mike Wilson',
        email: '<EMAIL>',
        role: 'user',
        status: 'suspended',
        joinDate: '2023-11-10',
        lastActive: '2024-01-12T09:15:00Z',
        totalPosts: 8,
        totalComments: 34,
        reportsReceived: 5,
        reportsSubmitted: 1,
        warningsIssued: 2,
        profileCompleteness: 60,
        verificationStatus: 'unverified',
        suspensionReason: 'Spam posting and inappropriate comments',
        suspensionExpiry: '2024-01-25'
      },
      {
        id: '4',
        name: 'Emily Davis',
        email: '<EMAIL>',
        role: 'investor',
        status: 'pending_review',
        joinDate: '2024-01-10',
        lastActive: '2024-01-13T14:20:00Z',
        totalPosts: 2,
        totalComments: 5,
        reportsReceived: 2,
        reportsSubmitted: 0,
        warningsIssued: 0,
        profileCompleteness: 40,
        verificationStatus: 'pending'
      }
    ];

    const mockActions: ModerationAction[] = [
      {
        id: '1',
        userId: '3',
        userName: 'Mike Wilson',
        action: 'suspension',
        reason: 'Spam posting and inappropriate comments',
        duration: '14 days',
        moderatorId: 'mod1',
        moderatorName: 'Admin User',
        timestamp: '2024-01-11T10:00:00Z',
        notes: 'Multiple reports received. User warned previously.'
      },
      {
        id: '2',
        userId: '4',
        userName: 'Emily Davis',
        action: 'warning',
        reason: 'Promotional content in comments',
        moderatorId: 'mod1',
        moderatorName: 'Admin User',
        timestamp: '2024-01-12T15:30:00Z',
        notes: 'First warning issued for promotional content.'
      }
    ];

    setTimeout(() => {
      setUsers(mockData);
      setFilteredUsers(mockData);
      setModerationActions([]);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter users based on search and filters
  useEffect(() => {
    let filtered = users;

    if (searchTerm) {
      filtered = filtered.filter(user => 
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(user => user.status === statusFilter);
    }

    if (roleFilter !== 'all') {
      filtered = filtered.filter(user => user.role === roleFilter);
    }

    setFilteredUsers(filtered);
  }, [users, searchTerm, statusFilter, roleFilter]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'suspended': return 'bg-orange-100 text-orange-800';
      case 'banned': return 'bg-red-100 text-red-800';
      case 'pending_review': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-purple-100 text-purple-800';
      case 'moderator': return 'bg-blue-100 text-blue-800';
      case 'mentor': return 'bg-green-100 text-green-800';
      case 'investor': return 'bg-orange-100 text-orange-800';
      case 'user': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getVerificationColor = (status: string) => {
    switch (status) {
      case 'verified': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'unverified': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleSuspendUser = (userId: string) => {
    setUsers(users.map(user => 
      user.id === userId 
        ? { ...user, status: 'suspended' as const, suspensionReason: 'Manual suspension', suspensionExpiry: '2024-02-15' }
        : user
    ));
  };

  const handleUnsuspendUser = (userId: string) => {
    setUsers(users.map(user => 
      user.id === userId 
        ? { ...user, status: 'active' as const, suspensionReason: undefined, suspensionExpiry: undefined }
        : user
    ));
  };

  const handleBanUser = (userId: string) => {
    setUsers(users.map(user => 
      user.id === userId ? { ...user, status: 'banned' as const } : user
    ));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">User Moderation</h1>
          <p className="text-gray-600 mt-1">Manage user accounts and moderation actions</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Filter className="w-4 h-4 mr-2" />
            Advanced Filters
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Shield className="w-4 h-4 mr-2" />
            Bulk Actions
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Users</p>
                <p className="text-2xl font-bold">{users.length}</p>
              </div>
              <User className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Users</p>
                <p className="text-2xl font-bold text-green-600">
                  {users.filter(user => user.status === 'active').length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Suspended</p>
                <p className="text-2xl font-bold text-orange-600">
                  {users.filter(user => user.status === 'suspended').length}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Review</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {users.filter(user => user.status === 'pending_review').length}
                </p>
              </div>
              <Eye className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select 
              value={statusFilter} 
              onChange={(e) => setStatusFilter(e.target.value)}
              className="border rounded px-3 py-2"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="suspended">Suspended</option>
              <option value="banned">Banned</option>
              <option value="pending_review">Pending Review</option>
            </select>
            <select 
              value={roleFilter} 
              onChange={(e) => setRoleFilter(e.target.value)}
              className="border rounded px-3 py-2"
            >
              <option value="all">All Roles</option>
              <option value="user">User</option>
              <option value="mentor">Mentor</option>
              <option value="investor">Investor</option>
              <option value="moderator">Moderator</option>
              <option value="admin">Admin</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* User Management Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="users">Users ({filteredUsers.length})</TabsTrigger>
          <TabsTrigger value="actions">Recent Actions ({moderationActions.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-4">
          <div className="space-y-4">
            {filteredUsers.map((user) => (
              <Card key={user.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-4 flex-1">
                      <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                        <User className="w-6 h-6 text-gray-400" />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="font-semibold text-lg">{user.name}</h3>
                          <Badge className={getStatusColor(user.status)}>
                            {user.status.replace('_', ' ')}
                          </Badge>
                          <Badge className={getRoleColor(user.role)}>
                            {user.role}
                          </Badge>
                          <Badge className={getVerificationColor(user.verificationStatus)}>
                            {user.verificationStatus}
                          </Badge>
                        </div>
                        
                        <p className="text-gray-600 mb-3">{user.email}</p>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                          <div>
                            <p className="text-gray-600">Joined</p>
                            <p className="font-medium">{formatDate(user.joinDate)}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">Last Active</p>
                            <p className="font-medium">{formatDate(user.lastActive)}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">Posts/Comments</p>
                            <p className="font-medium">{user.totalPosts}/{user.totalComments}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">Reports</p>
                            <p className="font-medium">{user.reportsReceived} received</p>
                          </div>
                        </div>

                        <div className="flex items-center gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">Profile: </span>
                            <span className="font-medium">{user.profileCompleteness}%</span>
                          </div>
                          {user.warningsIssued > 0 && (
                            <div>
                              <span className="text-gray-600">Warnings: </span>
                              <span className="font-medium text-orange-600">{user.warningsIssued}</span>
                            </div>
                          )}
                        </div>

                        {user.suspensionReason && (
                          <div className="mt-3 p-3 bg-orange-50 border border-orange-200 rounded">
                            <p className="text-sm text-orange-800">
                              <strong>Suspension Reason:</strong> {user.suspensionReason}
                            </p>
                            {user.suspensionExpiry && (
                              <p className="text-sm text-orange-700">
                                <strong>Expires:</strong> {formatDate(user.suspensionExpiry)}
                              </p>
                            )}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex flex-col gap-2 ml-4">
                      <Button variant="outline" size="sm">
                        <Eye className="w-4 h-4 mr-1" />
                        View Profile
                      </Button>
                      
                      <Button variant="outline" size="sm">
                        <Mail className="w-4 h-4 mr-1" />
                        Message
                      </Button>

                      {user.status === 'active' && (
                        <>
                          <Button 
                            variant="outline" 
                            size="sm"
                            className="border-orange-300 text-orange-600 hover:bg-orange-50"
                            onClick={() => handleSuspendUser(user.id)}
                          >
                            <AlertTriangle className="w-4 h-4 mr-1" />
                            Suspend
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            className="border-red-300 text-red-600 hover:bg-red-50"
                            onClick={() => handleBanUser(user.id)}
                          >
                            <Ban className="w-4 h-4 mr-1" />
                            Ban
                          </Button>
                        </>
                      )}

                      {user.status === 'suspended' && (
                        <Button 
                          variant="outline" 
                          size="sm"
                          className="border-green-300 text-green-600 hover:bg-green-50"
                          onClick={() => handleUnsuspendUser(user.id)}
                        >
                          <CheckCircle className="w-4 h-4 mr-1" />
                          Unsuspend
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredUsers.length === 0 && (
            <Card>
              <CardContent className="p-8 text-center">
                <User className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-4">No users found matching your criteria.</p>
                <Button variant="outline" onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('all');
                  setRoleFilter('all');
                }}>
                  Clear Filters
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="actions" className="space-y-4">
          <div className="space-y-4">
            {moderationActions.map((action) => (
              <Card key={action.id}>
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="font-semibold">{action.userName}</h4>
                        <Badge className={
                          action.action === 'ban' ? 'bg-red-100 text-red-800' :
                          action.action === 'suspension' ? 'bg-orange-100 text-orange-800' :
                          action.action === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }>
                          {action.action}
                        </Badge>
                      </div>
                      
                      <p className="text-sm text-gray-700 mb-2">
                        <strong>Reason:</strong> {action.reason}
                      </p>
                      
                      {action.duration && (
                        <p className="text-sm text-gray-700 mb-2">
                          <strong>Duration:</strong> {action.duration}
                        </p>
                      )}
                      
                      <div className="text-sm text-gray-600">
                        <p>By: {action.moderatorName} • {formatDateTime(action.timestamp)}</p>
                      </div>

                      {action.notes && (
                        <div className="mt-2 p-2 bg-gray-50 rounded text-sm">
                          <strong>Notes:</strong> {action.notes}
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {moderationActions.length === 0 && (
            <Card>
              <CardContent className="p-8 text-center">
                <Shield className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No moderation actions recorded yet.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default UserModerationPage;
