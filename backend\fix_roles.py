#!/usr/bin/env python3
"""
Fix User Roles Script - Backend Version
This script checks and fixes the user role assignments in the database
"""

import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from django.contrib.auth.models import User
from users.models import UserProfile, UserRole, UserRoleAssignment

def check_and_fix_user_roles():
    """Check and fix user role assignments"""
    print("🔧 Checking and fixing user role assignments...")
    print("=" * 50)
    
    # Expected user roles
    expected_roles = {
        'testuser': {'role': 'user', 'is_staff': False, 'is_superuser': False},
        'testmentor': {'role': 'mentor', 'is_staff': False, 'is_superuser': False},
        'testinvestor': {'role': 'investor', 'is_staff': False, 'is_superuser': <PERSON>alse},
        'testmoderator': {'role': 'moderator', 'is_staff': False, 'is_superuser': False},
        'testadmin': {'role': 'admin', 'is_staff': True, 'is_superuser': False},
        'testsuperadmin': {'role': 'super_admin', 'is_staff': True, 'is_superuser': True},
    }
    
    for username, expected in expected_roles.items():
        try:
            user = User.objects.get(username=username)
            print(f"\n👤 Checking user: {username}")
            
            # Check current state
            print(f"   Current: is_staff={user.is_staff}, is_superuser={user.is_superuser}")
            print(f"   Expected: is_staff={expected['is_staff']}, is_superuser={expected['is_superuser']}")
            
            # Fix Django user fields if needed
            needs_update = False
            if user.is_staff != expected['is_staff']:
                print(f"   ❌ Fixing is_staff: {user.is_staff} → {expected['is_staff']}")
                user.is_staff = expected['is_staff']
                needs_update = True
                
            if user.is_superuser != expected['is_superuser']:
                print(f"   ❌ Fixing is_superuser: {user.is_superuser} → {expected['is_superuser']}")
                user.is_superuser = expected['is_superuser']
                needs_update = True
                
            if needs_update:
                user.save()
                print(f"   ✅ Updated Django user fields for {username}")
            else:
                print(f"   ✅ Django user fields are correct for {username}")
            
        except User.DoesNotExist:
            print(f"   ❌ User {username} does not exist")
        except Exception as e:
            print(f"   ❌ Error processing {username}: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 User role fix completed!")

def test_role_determination():
    """Test the role determination logic"""
    print("\n🧪 Testing role determination logic...")
    print("=" * 50)
    
    from users.serializers import UserSerializer
    
    test_users = ['testuser', 'testmentor', 'testinvestor', 'testadmin', 'testsuperadmin']
    
    for username in test_users:
        try:
            user = User.objects.get(username=username)
            serializer = UserSerializer()
            determined_role = serializer.get_user_role(user)
            
            print(f"👤 {username}:")
            print(f"   is_staff: {user.is_staff}")
            print(f"   is_superuser: {user.is_superuser}")
            print(f"   Determined role: {determined_role}")
            
        except User.DoesNotExist:
            print(f"   ❌ User {username} does not exist")
        except Exception as e:
            print(f"   ❌ Error testing {username}: {e}")

if __name__ == '__main__':
    check_and_fix_user_roles()
    test_role_determination()
