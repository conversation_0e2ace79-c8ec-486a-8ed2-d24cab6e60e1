import React from 'react';

interface DashboardLayoutProps {
  children: React.ReactNode;
  currentPage?: string;
}

/**
 * TEMPORARY DASHBOARD LAYOUT WRAPPER
 * This is a temporary component to prevent import errors.
 * All DashboardLayout usage should be removed and replaced with proper routing layouts.
 * 
 * TODO: Remove this component and update all components to not use DashboardLayout
 */
const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children, currentPage }) => {
  // Simple wrapper that just renders children
  // The actual layout is handled by AuthenticatedLayout in the routing system
  return <div className="dashboard-layout-wrapper">{children}</div>;
};

export default DashboardLayout;
