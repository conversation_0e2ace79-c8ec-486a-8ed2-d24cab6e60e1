# Cypress E2E Testing Suite

A comprehensive end-to-end testing suite built with Cypress for professional-grade application testing.

## 🚀 Quick Start

### Prerequisites

- Node.js 16+ installed
- Application running on `http://localhost:3000`
- Backend API running on `http://localhost:8000`

### Installation

```bash
# Install Cypress and dependencies
npm install

# Verify installation
npx cypress verify
```

### Running Tests

```bash
# Open Cypress Test Runner (Interactive)
npm run cypress:open

# Run all tests headlessly
npm run cypress:run

# Run tests with development server
npm run cypress:test

# Run specific test suite
node cypress/scripts/run-e2e-tests.js smoke
```

## 📁 Project Structure

```
cypress/
├── e2e/                          # Test files
│   ├── 01-authentication.cy.ts   # Login/logout flows
│   ├── 02-role-based-access.cy.ts # RBAC testing
│   ├── 03-business-plans-crud.cy.ts # CRUD operations
│   ├── 04-sidebar-navigation.cy.ts # Navigation testing
│   ├── 05-api-integration.cy.ts  # API endpoint testing
│   └── 06-accessibility-performance.cy.ts # A11y & performance
├── fixtures/                     # Test data
│   ├── user.json                # User test data
│   ├── admin.json               # Admin test data
│   ├── business-plan.json       # Business plan data
│   └── login-response.json      # Auth response data
├── support/                      # Support files
│   ├── commands.ts              # Custom commands
│   ├── e2e.ts                   # Global configuration
│   ├── component.ts             # Component testing setup
│   └── page-objects/            # Page Object Models
│       ├── LoginPage.ts         # Login page actions
│       └── DashboardPage.ts     # Dashboard page actions
├── scripts/                      # Utility scripts
│   └── run-e2e-tests.js         # Test runner script
└── README.md                     # This file
```

## 🧪 Test Suites

### 1. Authentication Tests (`01-authentication.cy.ts`)
- ✅ Login form validation
- ✅ Successful login flows for different roles
- ✅ Logout functionality
- ✅ Session management
- ✅ Security testing (XSS, CSRF)
- ✅ Accessibility compliance

### 2. Role-Based Access Control (`02-role-based-access.cy.ts`)
- ✅ User role permissions
- ✅ Admin role permissions
- ✅ Super admin role permissions
- ✅ Mentor role permissions
- ✅ Dynamic role changes
- ✅ Permission enforcement

### 3. CRUD Operations (`03-business-plans-crud.cy.ts`)
- ✅ Create business plans
- ✅ Read/list business plans
- ✅ Update business plans
- ✅ Delete business plans
- ✅ Form validation
- ✅ File uploads
- ✅ Error handling

### 4. Navigation Testing (`04-sidebar-navigation.cy.ts`)
- ✅ Role-based navigation
- ✅ Responsive behavior
- ✅ Keyboard navigation
- ✅ Mobile menu functionality
- ✅ Internationalization
- ✅ State persistence

### 5. API Integration (`05-api-integration.cy.ts`)
- ✅ Authentication endpoints
- ✅ CRUD API endpoints
- ✅ Error handling
- ✅ Request/response validation
- ✅ Performance testing
- ✅ Security headers

### 6. Accessibility & Performance (`06-accessibility-performance.cy.ts`)
- ✅ WCAG 2.1 compliance
- ✅ Keyboard navigation
- ✅ Screen reader support
- ✅ Color contrast
- ✅ Performance budgets
- ✅ Responsive design
- ✅ Cross-browser compatibility

## 🛠 Custom Commands

### Authentication Commands
```typescript
cy.loginAs('admin')           // Login as specific role
cy.getByTestId('element')     // Get element by test ID
cy.waitForPageLoad()          // Wait for page to fully load
```

### Form Commands
```typescript
cy.fillForm({                // Fill form with data
  title: 'Test Title',
  description: 'Test Description'
})
cy.uploadFile('selector', 'filename') // Upload file
```

### Accessibility Commands
```typescript
cy.checkA11y()               // Run accessibility audit
cy.checkResponsive()         // Test responsive design
```

### API Commands
```typescript
cy.apiRequest('GET', '/api/users') // Make authenticated API request
cy.waitForApi('@alias')      // Wait for API response
```

### Redux Commands
```typescript
cy.getReduxState('auth')     // Get Redux state slice
cy.dispatchAction(action)    // Dispatch Redux action
```

## 📊 Page Object Models

### LoginPage
```typescript
const loginPage = new LoginPage()
loginPage.visit()
  .enterEmail('<EMAIL>')
  .enterPassword('password')
  .clickLogin()
  .shouldRedirectToDashboard()
```

### DashboardPage
```typescript
const dashboardPage = new DashboardPage()
dashboardPage.visit('admin')
  .shouldBeVisible()
  .shouldShowCorrectRoleBasedContent('admin')
  .navigateToBusinessPlans()
```

## 🔧 Configuration

### Environment Variables
```bash
CYPRESS_BASE_URL=http://localhost:3000    # Application URL
CYPRESS_API_URL=http://localhost:8000     # API URL
CYPRESS_BROWSER=chrome                    # Browser to use
CYPRESS_HEADLESS=true                     # Run headlessly
CYPRESS_RECORD=false                      # Record to Cypress Cloud
CYPRESS_PARALLEL=false                    # Parallel execution
```

### Cypress Configuration (`cypress.config.ts`)
- Base URL and API configuration
- Viewport settings
- Timeout configurations
- Reporter settings
- Browser launch options

## 📈 Reporting

### Test Reports
- **HTML Reports**: Generated in `cypress-reports/`
- **Screenshots**: Captured on failure in `cypress/screenshots/`
- **Videos**: Recorded for failed tests in `cypress/videos/`
- **JSON Reports**: Machine-readable results

### Coverage Reports
- Code coverage integration with `@cypress/code-coverage`
- Coverage thresholds and reporting
- Integration with CI/CD pipelines

## 🚀 CI/CD Integration

### GitHub Actions Example
```yaml
- name: Run E2E Tests
  run: |
    npm run cypress:ci
  env:
    CYPRESS_BASE_URL: ${{ secrets.BASE_URL }}
    CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
```

### Test Suites for CI
```bash
# Smoke tests (fast)
npm run cypress:test:smoke

# Full test suite
npm run cypress:test:full

# Accessibility only
npm run cypress:test:accessibility
```

## 🐛 Debugging

### Interactive Debugging
```bash
# Open Cypress Test Runner
npm run cypress:open

# Run with headed browser
npm run cypress:run:headed

# Debug specific test
npx cypress run --spec "cypress/e2e/01-authentication.cy.ts" --headed
```

### Debug Commands
```typescript
cy.debug()                   // Pause execution
cy.pause()                   // Interactive pause
cy.screenshot('debug-shot')  // Take screenshot
cy.log('Debug message')      // Log to command log
```

## 🔒 Security Testing

### Included Security Tests
- XSS prevention
- CSRF protection
- Authentication bypass attempts
- Authorization checks
- Input validation
- SQL injection prevention

## ♿ Accessibility Testing

### WCAG 2.1 Compliance
- Level AA compliance testing
- Keyboard navigation
- Screen reader compatibility
- Color contrast validation
- Focus management
- ARIA attributes

## 📱 Responsive Testing

### Viewport Testing
- Mobile devices (320px+)
- Tablet devices (768px+)
- Desktop devices (1024px+)
- Large screens (1920px+)

## 🌐 Internationalization Testing

### Language Support
- English (en) testing
- Arabic (ar) testing with RTL
- Language switching
- Content translation validation

## 📚 Best Practices

### Test Organization
- Use Page Object Models
- Implement custom commands
- Maintain test data in fixtures
- Follow naming conventions

### Performance
- Set appropriate timeouts
- Use efficient selectors
- Minimize test dependencies
- Implement proper cleanup

### Maintainability
- Keep tests independent
- Use descriptive test names
- Document complex test logic
- Regular test review and updates

## 🤝 Contributing

1. Follow the existing test patterns
2. Add tests for new features
3. Update documentation
4. Ensure all tests pass
5. Review accessibility compliance

## 📞 Support

For questions or issues:
1. Check existing test documentation
2. Review Cypress documentation
3. Check test logs and screenshots
4. Contact the development team
