/**
 * ADMIN ROLE CONFIGURATION
 * Dedicated file for admin role - no duplicates, single source of truth
 */

import { UserRole, PermissionLevel } from '../utils/unifiedRoleManager';

export const ADMIN_ROLE: UserRole = 'admin';

export const ADMIN_PERMISSIONS: PermissionLevel[] = ['read', 'write', 'admin'];

export const ADMIN_NAVIGATION_ITEMS = [
  {
    id: 'dashboard',
    name: 'dashboard.title',
    path: '/dashboard',
    icon: 'Home',
    category: 'main' as const
  },
  {
    id: 'admin-dashboard',
    name: 'admin.dashboard.title',
    path: '/admin',
    icon: 'Shield',
    category: 'system' as const
  },
  {
    id: 'users-management',
    name: 'admin.users.title',
    path: '/admin/users',
    icon: 'Users',
    category: 'system' as const
  },
  {
    id: 'admin-analytics',
    name: 'admin.analytics.title',
    path: '/admin/analytics',
    icon: 'BarChart3',
    category: 'system' as const
  },
  {
    id: 'events-management',
    name: 'admin.events.title',
    path: '/admin/events',
    icon: 'Calendar',
    category: 'content' as const
  },
  {
    id: 'resources-management',
    name: 'admin.resources.title',
    path: '/admin/resources',
    icon: 'BookOpen',
    category: 'content' as const
  },
  {
    id: 'posts-management',
    name: 'admin.posts.title',
    path: '/admin/posts',
    icon: 'MessageSquare',
    category: 'content' as const
  },
  {
    id: 'moderation-dashboard',
    name: 'admin.moderation.title',
    path: '/admin/moderation',
    icon: 'Shield',
    category: 'system' as const
  },
  {
    id: 'communication-center',
    name: 'admin.communication.title',
    path: '/admin/communication',
    icon: 'MessageCircle',
    category: 'system' as const
  },
  {
    id: 'api-management',
    name: 'admin.api.title',
    path: '/admin/api',
    icon: 'Database',
    category: 'system' as const
  },
  {
    id: 'performance-center',
    name: 'admin.performance.title',
    path: '/admin/performance',
    icon: 'Activity',
    category: 'system' as const
  },
  {
    id: 'admin-settings',
    name: 'admin.settings.title',
    path: '/admin/settings',
    icon: 'Settings',
    category: 'system' as const
  },
  {
    id: 'ai-system',
    name: 'admin.ai.title',
    path: '/admin/ai-system',
    icon: 'Bot',
    category: 'ai' as const
  },
  {
    id: 'profile',
    name: 'profile.title',
    path: '/profile',
    icon: 'User',
    category: 'main' as const
  },
  {
    id: 'settings',
    name: 'settings.title',
    path: '/settings',
    icon: 'Settings',
    category: 'main' as const
  }
];

export const ADMIN_ROUTES = [
  '/dashboard',
  '/admin',
  '/admin/users',
  '/admin/analytics',
  '/admin/events',
  '/admin/resources',
  '/admin/posts',
  '/admin/moderation',
  '/admin/communication',
  '/admin/api',
  '/admin/performance',
  '/admin/settings',
  '/admin/ai-system',
  '/profile',
  '/settings'
];

export const ADMIN_DASHBOARD_CONFIG = {
  defaultRoute: '/admin',
  welcomeMessage: 'Welcome to the admin dashboard',
  features: [
    'user_management',
    'content_management',
    'analytics',
    'moderation',
    'communication',
    'api_management',
    'performance_monitoring',
    'ai_system_management'
  ]
};

/**
 * Check if a user object represents an admin
 */
export function isAdminRole(user: any): boolean {
  if (!user) return false;
  
  // Explicit admin role
  if (user.user_role === 'admin') return true;
  
  // Django staff flag
  if (user.is_staff && !user.is_superuser) return true;
  
  // Admin flag
  if (user.is_admin) return true;
  
  // Check profile-based role
  if (user.profile?.primary_role?.name === 'admin') return true;
  
  return false;
}

/**
 * Get admin-specific dashboard route
 */
export function getAdminDashboardRoute(): string {
  return ADMIN_DASHBOARD_CONFIG.defaultRoute;
}

/**
 * Check if admin can access specific admin feature
 */
export function canAccessAdminFeature(user: any, feature: string): boolean {
  if (!isAdminRole(user)) return false;
  
  return ADMIN_DASHBOARD_CONFIG.features.includes(feature);
}

export default {
  role: ADMIN_ROLE,
  permissions: ADMIN_PERMISSIONS,
  navigationItems: ADMIN_NAVIGATION_ITEMS,
  routes: ADMIN_ROUTES,
  dashboardConfig: ADMIN_DASHBOARD_CONFIG,
  isRole: isAdminRole,
  getDashboardRoute: getAdminDashboardRoute,
  canAccessFeature: canAccessAdminFeature
};
