// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

/// <reference types="cypress" />

// Custom command to select elements by data-testid
Cypress.Commands.add('getByTestId', (testId: string) => {
  return cy.get(`[data-testid="${testId}"]`)
})

// Custom command for login with different roles
Cypress.Commands.add('loginAs', (role: 'admin' | 'superadmin' | 'user' | 'mentor') => {
  const users = {
    admin: {
      email: '<EMAIL>',
      password: 'admin123',
      expectedRoute: '/admin',
      fixture: 'admin'
    },
    superadmin: {
      email: '<EMAIL>',
      password: 'superadmin123',
      expectedRoute: '/superadmin',
      fixture: 'admin'
    },
    user: {
      email: '<EMAIL>',
      password: 'user123',
      expectedRoute: '/dashboard',
      fixture: 'user'
    },
    mentor: {
      email: '<EMAIL>',
      password: 'mentor123',
      expectedRoute: '/dashboard',
      fixture: 'user'
    }
  }

  const user = users[role]

  // Mock the authentication API calls
  cy.intercept('POST', '**/api/auth/login/', { fixture: 'login-response.json' }).as('loginRequest')
  cy.intercept('POST', '**/api/auth/token/', { fixture: 'login-response.json' }).as('tokenRequest')
  cy.intercept('GET', '**/api/auth/user/', { fixture: `${user.fixture}.json` }).as('getUserRequest')

  // Set up authentication state directly for faster testing
  cy.window().then((win) => {
    // Set tokens in localStorage
    win.localStorage.setItem('access_token', 'test-access-token')
    win.localStorage.setItem('refresh_token', 'test-refresh-token')
    win.localStorage.setItem('token', 'test-access-token')

    // Set user data in localStorage
    cy.fixture(`${user.fixture}.json`).then((userData) => {
      win.localStorage.setItem('user', JSON.stringify(userData))
    })
  })

  // Visit the expected route directly (faster than going through login flow)
  cy.visit(user.expectedRoute)

  // Verify authentication state
  cy.window().its('localStorage').should('have.property', 'token')
})

// Custom command to wait for page load
Cypress.Commands.add('waitForPageLoad', () => {
  cy.window().should('have.property', 'document')
  cy.document().should('have.property', 'readyState', 'complete')
  
  // Wait for React to be ready
  cy.window().should('have.property', 'React')
  
  // Wait for any loading spinners to disappear
  cy.get('[data-testid="loading-spinner"]', { timeout: 10000 }).should('not.exist')
  cy.get('.loading', { timeout: 10000 }).should('not.exist')
  
  // Wait for main content to be visible
  cy.get('main, [role="main"], #root > div', { timeout: 10000 }).should('be.visible')
})

// Custom command for accessibility testing
Cypress.Commands.add('checkA11y', () => {
  cy.checkAxe(null, null, (violations) => {
    if (violations.length > 0) {
      cy.log('Accessibility violations found:')
      violations.forEach((violation) => {
        cy.log(`${violation.impact}: ${violation.description}`)
        violation.nodes.forEach((node) => {
          cy.log(`  - ${node.target}`)
        })
      })
    }
  })
})

// Custom command to seed test data
Cypress.Commands.add('seedTestData', (dataType: string) => {
  cy.task('seedDatabase', dataType)
})

// Custom command to clean test data
Cypress.Commands.add('cleanTestData', () => {
  cy.task('cleanDatabase')
})

// Custom command for API requests with authentication
Cypress.Commands.add('apiRequest', (method: string, url: string, body?: any) => {
  return cy.window().then((win) => {
    const token = win.localStorage.getItem('token')
    
    return cy.request({
      method,
      url: `${Cypress.env('apiUrl')}${url}`,
      body,
      headers: {
        'Authorization': token ? `Bearer ${token}` : '',
        'Content-Type': 'application/json'
      },
      failOnStatusCode: false
    })
  })
})

// Custom command to wait for API response
Cypress.Commands.add('waitForApi', (alias: string, timeout: number = 10000) => {
  cy.wait(alias, { timeout })
})

// Custom command to check Redux state
Cypress.Commands.add('getReduxState', (path?: string) => {
  return cy.window().then((win) => {
    const store = (win as any).__REDUX_STORE__ || (win as any).store
    if (!store) {
      throw new Error('Redux store not found on window object')
    }
    
    const state = store.getState()
    return path ? state[path] : state
  })
})

// Custom command to dispatch Redux action
Cypress.Commands.add('dispatchAction', (action: any) => {
  return cy.window().then((win) => {
    const store = (win as any).__REDUX_STORE__ || (win as any).store
    if (!store) {
      throw new Error('Redux store not found on window object')
    }
    
    store.dispatch(action)
  })
})

// Custom command to fill form fields
Cypress.Commands.add('fillForm', (formData: { [key: string]: string }) => {
  Object.entries(formData).forEach(([field, value]) => {
    cy.getByTestId(`${field}-input`).clear().type(value)
  })
})

// Custom command to upload file
Cypress.Commands.add('uploadFile', (selector: string, fileName: string) => {
  cy.get(selector).selectFile(`cypress/fixtures/${fileName}`)
})

// Custom command to check toast messages
Cypress.Commands.add('checkToast', (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'success') => {
  cy.get(`[data-testid="toast-${type}"]`, { timeout: 5000 })
    .should('be.visible')
    .and('contain.text', message)
})

// Custom command to navigate using sidebar
Cypress.Commands.add('navigateViaSidebar', (menuItem: string) => {
  // Try multiple selectors for sidebar navigation
  cy.get('body').then(($body) => {
    if ($body.find('[data-testid="sidebar"]').length > 0) {
      cy.getByTestId('sidebar').within(() => {
        cy.contains(menuItem).click()
      })
    } else if ($body.find('[data-testid="sidebar-menu"]').length > 0) {
      cy.getByTestId('sidebar-menu').within(() => {
        cy.contains(menuItem).click()
      })
    } else {
      // Fallback: look for navigation links anywhere
      cy.contains('a', menuItem).click()
    }
  })
})

// Custom command for performance timing
Cypress.Commands.add('startPerformanceTimer', (timerName: string) => {
  cy.window().then((win) => {
    (win as any).__performanceTimers = (win as any).__performanceTimers || {}
    ;(win as any).__performanceTimers[timerName] = performance.now()
  })
})

Cypress.Commands.add('endPerformanceTimer', (timerName: string, maxTime: number) => {
  cy.window().then((win) => {
    const timers = (win as any).__performanceTimers || {}
    const startTime = timers[timerName]
    if (startTime) {
      const duration = performance.now() - startTime
      cy.log(`Performance timer "${timerName}": ${duration.toFixed(2)}ms`)
      expect(duration).to.be.lessThan(maxTime)
    }
  })
})

// Custom command to switch language
Cypress.Commands.add('switchLanguage', (language: 'en' | 'ar') => {
  cy.getByTestId('language-selector').click()
  cy.getByTestId(`language-${language}`).click()
  
  // Wait for language to change
  cy.get('html').should('have.attr', 'lang', language)
})

// Custom command to check responsive design
Cypress.Commands.add('checkResponsive', () => {
  const viewports = [
    { width: 320, height: 568 },  // Mobile
    { width: 768, height: 1024 }, // Tablet
    { width: 1024, height: 768 }, // Desktop small
    { width: 1920, height: 1080 } // Desktop large
  ]
  
  viewports.forEach((viewport) => {
    cy.viewport(viewport.width, viewport.height)
    cy.wait(500) // Allow time for responsive changes
    
    // Check that main content is visible
    cy.get('main, [role="main"]').should('be.visible')
    
    // Check that navigation is accessible
    if (viewport.width < 768) {
      // Mobile: check hamburger menu
      cy.getByTestId('mobile-menu-button').should('be.visible')
    } else {
      // Desktop: check sidebar
      cy.getByTestId('sidebar').should('be.visible')
    }
  })
})

// Declare custom commands for TypeScript
declare global {
  namespace Cypress {
    interface Chainable {
      getByTestId(testId: string): Chainable<JQuery<HTMLElement>>
      loginAs(role: 'admin' | 'superadmin' | 'user' | 'mentor'): Chainable<void>
      waitForPageLoad(): Chainable<void>
      checkA11y(): Chainable<void>
      seedTestData(dataType: string): Chainable<void>
      cleanTestData(): Chainable<void>
      apiRequest(method: string, url: string, body?: any): Chainable<any>
      waitForApi(alias: string, timeout?: number): Chainable<any>
      getReduxState(path?: string): Chainable<any>
      dispatchAction(action: any): Chainable<void>
      fillForm(formData: { [key: string]: string }): Chainable<void>
      uploadFile(selector: string, fileName: string): Chainable<void>
      checkToast(message: string, type?: 'success' | 'error' | 'warning' | 'info'): Chainable<void>
      navigateViaSidebar(menuItem: string): Chainable<void>
      switchLanguage(language: 'en' | 'ar'): Chainable<void>
      checkResponsive(): Chainable<void>
      startPerformanceTimer(timerName: string): Chainable<void>
      endPerformanceTimer(timerName: string, maxTime: number): Chainable<void>
    }
  }
}
