"""
Custom authentication views that include user data with JWT tokens
"""
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth import get_user_model

User = get_user_model()


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    """Custom serializer that includes user data with tokens"""
    
    def validate(self, attrs):
        data = super().validate(attrs)
        
        # Add user data to the response
        user = self.user
        data['user'] = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'is_admin': user.is_staff,
            'is_superuser': user.is_superuser,
        }
        
        # Add user profile data with EXCLUSIVE role system
        if hasattr(user, 'profile'):
            try:
                profile = user.profile

                # ✅ EXCLUSIVE ROLE DETERMINATION
                # Determine user's single primary role
                user_role = 'user'  # Default

                if user.is_superuser:
                    user_role = 'super_admin'
                elif user.is_staff:
                    user_role = 'admin'
                elif profile.primary_role and profile.primary_role.name in ['mentor', 'investor', 'moderator']:
                    user_role = profile.primary_role.name
                else:
                    # Check active roles for specialized roles
                    active_roles = profile.get_active_roles()
                    for role in active_roles:
                        if role.name in ['mentor', 'investor', 'moderator']:
                            user_role = role.name
                            break

                # Get active roles for display (but primary role is exclusive)
                active_roles = profile.get_active_roles()
                active_roles_data = []
                for role in active_roles:
                    active_roles_data.append({
                        'name': role.name,
                        'permission_level': role.permission_level,
                        'display_name': role.display_name
                    })

                # Get primary role data
                primary_role_data = None
                if profile.primary_role:
                    primary_role_data = {
                        'name': profile.primary_role.name,
                        'permission_level': profile.primary_role.permission_level,
                        'display_name': profile.primary_role.display_name
                    }

                data['user']['profile'] = {
                    'role': user_role,  # ✅ Single exclusive role
                    'bio': getattr(profile, 'bio', ''),
                    'location': getattr(profile, 'location', ''),
                    'website': getattr(profile, 'website', ''),
                    'avatar': getattr(profile, 'avatar', None),
                    'active_roles': active_roles_data,
                    'primary_role': primary_role_data,
                    'highest_permission_level': profile.get_highest_permission_level(),
                }
            except Exception as e:
                print(f"Error getting profile data: {e}")
                # Profile doesn't exist or has issues - determine role from Django fields
                user_role = 'user'
                if user.is_superuser:
                    user_role = 'super_admin'
                elif user.is_staff:
                    user_role = 'admin'

                data['user']['profile'] = {
                    'role': user_role,
                    'bio': '',
                    'location': '',
                    'website': '',
                    'avatar': None,
                    'active_roles': [],
                    'primary_role': None,
                    'highest_permission_level': 'read',
                }
        else:
            # No profile model - determine role from Django fields
            user_role = 'user'
            if user.is_superuser:
                user_role = 'super_admin'
            elif user.is_staff:
                user_role = 'admin'

            data['user']['profile'] = {
                'role': user_role,
                'bio': '',
                'location': '',
                'website': '',
                'avatar': None,
                'active_roles': [],
                'primary_role': None,
                'highest_permission_level': 'read',
            }
        
        return data


class CustomTokenObtainPairView(TokenObtainPairView):
    """Custom token view that includes user data"""
    serializer_class = CustomTokenObtainPairSerializer
    
    def post(self, request, *args, **kwargs):
        try:
            response = super().post(request, *args, **kwargs)
            if response.status_code == 200:
                print(f"Login successful for user: {response.data.get('user', {}).get('username', 'unknown')}")
            return response
        except Exception as e:
            print(f"Login failed: {e}")
            return Response(
                {'detail': 'Invalid credentials'},
                status=status.HTTP_401_UNAUTHORIZED
            )
