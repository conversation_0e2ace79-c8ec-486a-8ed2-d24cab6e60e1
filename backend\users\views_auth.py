"""
Custom authentication views that include user data with JWT tokens
"""
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth import get_user_model
from .serializers import UserSerializer

User = get_user_model()


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    """Custom serializer that includes user data with tokens"""

    def validate(self, attrs):
        data = super().validate(attrs)

        # Use the UserSerializer to get consistent user data
        user = self.user
        user_serializer = UserSerializer(user)
        data['user'] = user_serializer.data

        return data


class CustomTokenObtainPairView(TokenObtainPairView):
    """Custom token view that includes user data"""
    serializer_class = CustomTokenObtainPairSerializer
    
    def post(self, request, *args, **kwargs):
        try:
            response = super().post(request, *args, **kwargs)
            if response.status_code == 200:
                print(f"Login successful for user: {response.data.get('user', {}).get('username', 'unknown')}")
            return response
        except Exception as e:
            print(f"Login failed: {e}")
            return Response(
                {'detail': 'Invalid credentials'},
                status=status.HTTP_401_UNAUTHORIZED
            )
