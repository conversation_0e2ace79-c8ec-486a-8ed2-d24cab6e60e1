/**
 * Comprehensive Error Handler
 * Replaces mock data fallbacks with proper error handling throughout the application
 */

import { useCallback, useState } from 'react';

export interface AppError {
  message: string;
  status?: number;
  code?: string;
  details?: any;
  timestamp: string;
  context?: string;
}

export interface ErrorState {
  hasError: boolean;
  error: AppError | null;
  isRetryable: boolean;
  retryCount: number;
}

export interface UseErrorHandlerReturn {
  errorState: ErrorState;
  handleError: (error: any, context?: string) => void;
  clearError: () => void;
  retry: (retryFn: () => Promise<void>) => Promise<void>;
  isRetryable: boolean;
}

/**
 * Comprehensive error processing
 */
export class ErrorProcessor {
  static processError(error: any, context: string = 'Operation'): AppError {
    const timestamp = new Date().toISOString();

    // Handle API response errors
    if (error?.response) {
      const { status, data } = error.response;
      return {
        message: data?.error || data?.message || this.getStatusMessage(status),
        status,
        code: data?.code || `HTTP_${status}`,
        details: data?.details,
        timestamp,
        context
      };
    }

    // Handle network errors
    if (error?.request) {
      return {
        message: 'Unable to connect to the server. Please check your internet connection.',
        code: 'NETWORK_ERROR',
        details: 'No response received from server',
        timestamp,
        context
      };
    }

    // Handle JavaScript errors
    if (error instanceof Error) {
      return {
        message: error.message,
        code: 'CLIENT_ERROR',
        details: error.stack,
        timestamp,
        context
      };
    }

    // Handle string errors
    if (typeof error === 'string') {
      return {
        message: error,
        code: 'UNKNOWN_ERROR',
        timestamp,
        context
      };
    }

    // Fallback for unknown error types
    return {
      message: 'An unexpected error occurred',
      code: 'UNKNOWN_ERROR',
      details: error,
      timestamp,
      context
    };
  }

  static getStatusMessage(status: number): string {
    switch (status) {
      case 400: return 'Invalid request. Please check your input.';
      case 401: return 'Authentication required. Please log in.';
      case 403: return 'You do not have permission for this action.';
      case 404: return 'The requested resource was not found.';
      case 409: return 'Conflict with current state. Please refresh and try again.';
      case 422: return 'Invalid data provided. Please check your input.';
      case 429: return 'Too many requests. Please wait and try again.';
      case 500: return 'Server error. Please try again later.';
      case 502: return 'Bad gateway. Please try again later.';
      case 503: return 'Service temporarily unavailable.';
      case 504: return 'Request timeout. Please try again.';
      default: return `Server responded with status ${status}`;
    }
  }

  static isRetryable(error: AppError): boolean {
    // Network errors are retryable
    if (error.code === 'NETWORK_ERROR') return true;
    
    // Server errors (5xx) are retryable
    if (error.status && error.status >= 500) return true;
    
    // Rate limiting is retryable
    if (error.status === 429) return true;
    
    // Timeout is retryable
    if (error.status === 408 || error.status === 504) return true;
    
    return false;
  }
}

/**
 * React hook for comprehensive error handling
 */
export function useErrorHandler(): UseErrorHandlerReturn {
  const [errorState, setErrorState] = useState<ErrorState>({
    hasError: false,
    error: null,
    isRetryable: false,
    retryCount: 0
  });

  const handleError = useCallback((error: any, context: string = 'Operation') => {
    const processedError = ErrorProcessor.processError(error, context);
    const isRetryable = ErrorProcessor.isRetryable(processedError);

    setErrorState(prev => ({
      hasError: true,
      error: processedError,
      isRetryable,
      retryCount: prev.retryCount + 1
    }));

    // Log error for debugging
    console.error(`[${context}] Error:`, {
      error: processedError,
      originalError: error
    });
  }, []);

  const clearError = useCallback(() => {
    setErrorState({
      hasError: false,
      error: null,
      isRetryable: false,
      retryCount: 0
    });
  }, []);

  const retry = useCallback(async (retryFn: () => Promise<void>) => {
    if (!errorState.isRetryable) {
      throw new Error('This error is not retryable');
    }

    clearError();
    
    try {
      await retryFn();
    } catch (error) {
      handleError(error, errorState.error?.context || 'Retry operation');
      throw error;
    }
  }, [errorState.isRetryable, errorState.error?.context, clearError, handleError]);

  return {
    errorState,
    handleError,
    clearError,
    retry,
    isRetryable: errorState.isRetryable
  };
}

/**
 * Error boundary wrapper for async operations
 */
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  context: string = 'Operation'
): Promise<{ data?: T; error?: AppError }> {
  try {
    const data = await operation();
    return { data };
  } catch (error) {
    const processedError = ErrorProcessor.processError(error, context);
    return { error: processedError };
  }
}

/**
 * Retry wrapper with exponential backoff
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000,
  context: string = 'Operation'
): Promise<T> {
  let lastError: any;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      const processedError = ErrorProcessor.processError(error, context);
      
      // Don't retry if error is not retryable
      if (!ErrorProcessor.isRetryable(processedError)) {
        throw error;
      }
      
      // Don't delay on last attempt
      if (attempt < maxRetries) {
        const delay = baseDelay * Math.pow(2, attempt - 1); // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  throw lastError;
}

/**
 * Loading state hook with error handling
 */
export function useAsyncOperation<T>() {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<T | null>(null);
  const { errorState, handleError, clearError } = useErrorHandler();

  const execute = useCallback(async (
    operation: () => Promise<T>,
    context: string = 'Operation'
  ) => {
    setLoading(true);
    clearError();
    
    try {
      const result = await operation();
      setData(result);
      return result;
    } catch (error) {
      handleError(error, context);
      setData(null);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [handleError, clearError]);

  const reset = useCallback(() => {
    setLoading(false);
    setData(null);
    clearError();
  }, [clearError]);

  return {
    loading,
    data,
    error: errorState.error,
    hasError: errorState.hasError,
    isRetryable: errorState.isRetryable,
    execute,
    reset,
    clearError
  };
}
