import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '../../../../store/hooks';
// import { fetchDashboardStats, fetchRecentActivity } from '../../../../store/adminSlice'; // TODO: Re-enable when admin slice is restored

/**
 * Custom hook to fetch dashboard data (stats and recent activity)
 * @returns Object containing dashboard stats, recent activity, and loading state
 */
const useDashboardData = () => {
  const dispatch = useAppDispatch();
  // Temporary fallback while admin slice is disabled
  const adminState = useAppSelector(state => (state as any).admin) || {};
  const dashboardStats = adminState.dashboardStats || null;
  const recentActivity = adminState.recentActivity || [];
  const isLoading = adminState.isLoading || false;
  const error = adminState.error || null;

  useEffect(() => {
    // Fetch dashboard stats and recent activity
    const loadDashboardData = async () => {
      try {
        // Re-enabled dashboard data fetching
        console.log("🔄 Loading dashboard data...");

        // For now, use mock data until admin slice is fully restored
        // TODO: Replace with actual Redux actions when admin slice is restored
        // dispatch(fetchDashboardStats());
        // dispatch(fetchRecentActivity());

        console.log("✅ Dashboard data loading completed");
      } catch (error) {
        console.error("❌ Dashboard data loading failed:", error);
      }
    };

    loadDashboardData();
  }, [dispatch]);

  return {
    stats: dashboardStats,
    recentActivity,
    isLoading,
    error
  };
};

export default useDashboardData;
