/**
 * RBAC System End-to-End Tests
 * Comprehensive testing of role-based access control system
 */

import { test, expect, Page } from '@playwright/test';

// Test user credentials for different roles
const testUsers = {
  superAdmin: { username: 'superadmin', password: 'admin123', expectedRole: 'super_admin' },
  admin: { username: 'admin', password: 'admin123', expectedRole: 'admin' },
  moderator: { username: 'moderator', password: 'mod123', expectedRole: 'moderator' },
  mentor: { username: 'mentor', password: 'mentor123', expectedRole: 'mentor' },
  investor: { username: 'investor', password: 'investor123', expectedRole: 'investor' },
  user: { username: 'testuser', password: 'user123', expectedRole: 'user' }
};

// Expected navigation items for each role
const expectedNavigation = {
  super_admin: [
    'Dashboard', 'Business Ideas', 'Business Plans', 'Templates', 'Posts', 'Events', 
    'Resources', 'Analytics', 'System Management', 'User Management', 'Super Admin Dashboard'
  ],
  admin: [
    'Dashboard', 'Business Ideas', 'Business Plans', 'Templates', 'Posts', 'Events', 
    'Resources', 'Analytics', 'Admin Dashboard', 'User Management'
  ],
  moderator: [
    'Dashboard', 'Templates', 'Posts', 'Events', 'Resources', 'Analytics', 
    'Moderator Dashboard', 'Content Moderation'
  ],
  mentor: [
    'Dashboard', 'Business Ideas', 'Business Plans', 'Templates', 'Posts', 'Events', 
    'Resources', 'Analytics', 'Mentor Dashboard'
  ],
  investor: [
    'Dashboard', 'Business Ideas', 'Business Plans', 'Templates', 'Posts', 'Events', 
    'Resources', 'Analytics', 'Investor Dashboard', 'Portfolio'
  ],
  user: [
    'Dashboard', 'Business Ideas', 'Business Plans', 'Templates', 'Posts', 'Events', 
    'Resources', 'Analytics'
  ]
};

// Expected dashboard routes for each role
const expectedDashboardRoutes = {
  super_admin: '/super_admin',
  admin: '/admin',
  moderator: '/dashboard/moderator',
  mentor: '/dashboard/mentor',
  investor: '/dashboard/investor',
  user: '/dashboard'
};

async function loginUser(page: Page, username: string, password: string) {
  await page.goto('/login');
  await page.fill('[data-testid="username-input"]', username);
  await page.fill('[data-testid="password-input"]', password);
  await page.click('[data-testid="login-button"]');
  
  // Wait for login to complete
  await page.waitForURL(/\/dashboard|\/admin|\/super_admin/, { timeout: 10000 });
}

async function getSidebarNavigationItems(page: Page): Promise<string[]> {
  // Wait for sidebar to load
  await page.waitForSelector('[data-testid="sidebar-navigation"]', { timeout: 5000 });
  
  // Get all navigation items
  const navItems = await page.$$eval('[data-testid="nav-item"]', elements => 
    elements.map(el => el.textContent?.trim()).filter(Boolean)
  );
  
  return navItems as string[];
}

test.describe('RBAC System Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // Set up any necessary test data or mocks
    await page.goto('/');
  });

  test('Super Admin - Full Access', async ({ page }) => {
    const user = testUsers.superAdmin;
    
    // Login as super admin
    await loginUser(page, user.username, user.password);
    
    // Verify correct dashboard route
    await expect(page).toHaveURL(expectedDashboardRoutes.super_admin);
    
    // Verify navigation items
    const navItems = await getSidebarNavigationItems(page);
    const expectedItems = expectedNavigation.super_admin;
    
    // Super admin should see all navigation items
    for (const item of expectedItems) {
      expect(navItems).toContain(item);
    }
    
    // Test access to restricted pages
    await page.goto('/super_admin/system-management');
    await expect(page).not.toHaveURL('/login'); // Should not be redirected to login
    
    await page.goto('/admin');
    await expect(page).not.toHaveURL('/login'); // Should have access to admin pages
  });

  test('Admin - Administrative Access', async ({ page }) => {
    const user = testUsers.admin;
    
    await loginUser(page, user.username, user.password);
    
    // Verify correct dashboard route
    await expect(page).toHaveURL(expectedDashboardRoutes.admin);
    
    // Verify navigation items
    const navItems = await getSidebarNavigationItems(page);
    const expectedItems = expectedNavigation.admin;
    
    for (const item of expectedItems) {
      expect(navItems).toContain(item);
    }
    
    // Should NOT see super admin items
    expect(navItems).not.toContain('System Management');
    expect(navItems).not.toContain('Super Admin Dashboard');
    
    // Test access restrictions
    await page.goto('/super_admin');
    await expect(page).toHaveURL('/login'); // Should be redirected to login
    
    await page.goto('/admin');
    await expect(page).not.toHaveURL('/login'); // Should have access to admin pages
  });

  test('Moderator - Content Moderation Access', async ({ page }) => {
    const user = testUsers.moderator;
    
    await loginUser(page, user.username, user.password);
    
    // Verify correct dashboard route
    await expect(page).toHaveURL(expectedDashboardRoutes.moderator);
    
    // Verify navigation items
    const navItems = await getSidebarNavigationItems(page);
    const expectedItems = expectedNavigation.moderator;
    
    for (const item of expectedItems) {
      expect(navItems).toContain(item);
    }
    
    // Should NOT see admin or super admin items
    expect(navItems).not.toContain('Admin Dashboard');
    expect(navItems).not.toContain('Super Admin Dashboard');
    expect(navItems).not.toContain('System Management');
    
    // Test access restrictions
    await page.goto('/admin');
    await expect(page).toHaveURL('/login'); // Should be redirected
    
    await page.goto('/dashboard/moderation/content');
    await expect(page).not.toHaveURL('/login'); // Should have access to moderation
  });

  test('Mentor - Mentorship Access', async ({ page }) => {
    const user = testUsers.mentor;
    
    await loginUser(page, user.username, user.password);
    
    // Verify correct dashboard route
    await expect(page).toHaveURL(expectedDashboardRoutes.mentor);
    
    // Verify navigation items
    const navItems = await getSidebarNavigationItems(page);
    const expectedItems = expectedNavigation.mentor;
    
    for (const item of expectedItems) {
      expect(navItems).toContain(item);
    }
    
    // Should NOT see admin items
    expect(navItems).not.toContain('Admin Dashboard');
    expect(navItems).not.toContain('Moderator Dashboard');
    
    // Test access restrictions
    await page.goto('/admin');
    await expect(page).toHaveURL('/login'); // Should be redirected
    
    await page.goto('/dashboard/mentorship/sessions');
    await expect(page).not.toHaveURL('/login'); // Should have access to mentorship
  });

  test('Investor - Investment Access', async ({ page }) => {
    const user = testUsers.investor;
    
    await loginUser(page, user.username, user.password);
    
    // Verify correct dashboard route
    await expect(page).toHaveURL(expectedDashboardRoutes.investor);
    
    // Verify navigation items
    const navItems = await getSidebarNavigationItems(page);
    const expectedItems = expectedNavigation.investor;
    
    for (const item of expectedItems) {
      expect(navItems).toContain(item);
    }
    
    // Should see investor-specific items
    expect(navItems).toContain('Portfolio');
    expect(navItems).toContain('Investor Dashboard');
    
    // Test access restrictions
    await page.goto('/admin');
    await expect(page).toHaveURL('/login'); // Should be redirected
    
    await page.goto('/dashboard/investments/portfolio');
    await expect(page).not.toHaveURL('/login'); // Should have access to investments
  });

  test('Regular User - Basic Access', async ({ page }) => {
    const user = testUsers.user;
    
    await loginUser(page, user.username, user.password);
    
    // Verify correct dashboard route
    await expect(page).toHaveURL(expectedDashboardRoutes.user);
    
    // Verify navigation items
    const navItems = await getSidebarNavigationItems(page);
    const expectedItems = expectedNavigation.user;
    
    for (const item of expectedItems) {
      expect(navItems).toContain(item);
    }
    
    // Should NOT see any admin or specialized role items
    expect(navItems).not.toContain('Admin Dashboard');
    expect(navItems).not.toContain('Super Admin Dashboard');
    expect(navItems).not.toContain('Moderator Dashboard');
    expect(navItems).not.toContain('Mentor Dashboard');
    expect(navItems).not.toContain('Investor Dashboard');
    
    // Test access restrictions
    await page.goto('/admin');
    await expect(page).toHaveURL('/login'); // Should be redirected
    
    await page.goto('/super_admin');
    await expect(page).toHaveURL('/login'); // Should be redirected
    
    await page.goto('/dashboard/business-ideas');
    await expect(page).not.toHaveURL('/login'); // Should have access to basic features
  });

  test('Unauthenticated User - No Access', async ({ page }) => {
    // Try to access protected routes without authentication
    const protectedRoutes = [
      '/dashboard',
      '/admin',
      '/super_admin',
      '/dashboard/business-ideas',
      '/dashboard/moderator'
    ];
    
    for (const route of protectedRoutes) {
      await page.goto(route);
      await expect(page).toHaveURL(/\/login/); // Should be redirected to login
    }
  });

  test('Role-Based Route Protection', async ({ page }) => {
    // Test that each role can only access their designated routes
    const roleRouteTests = [
      { role: 'admin', allowedRoutes: ['/admin', '/dashboard'], deniedRoutes: ['/super_admin'] },
      { role: 'moderator', allowedRoutes: ['/dashboard/moderator'], deniedRoutes: ['/admin', '/super_admin'] },
      { role: 'mentor', allowedRoutes: ['/dashboard/mentor'], deniedRoutes: ['/admin', '/super_admin'] },
      { role: 'investor', allowedRoutes: ['/dashboard/investor'], deniedRoutes: ['/admin', '/super_admin'] },
      { role: 'user', allowedRoutes: ['/dashboard'], deniedRoutes: ['/admin', '/super_admin', '/dashboard/moderator'] }
    ];
    
    for (const test of roleRouteTests) {
      const user = testUsers[test.role as keyof typeof testUsers];
      
      // Login as the specific role
      await loginUser(page, user.username, user.password);
      
      // Test allowed routes
      for (const route of test.allowedRoutes) {
        await page.goto(route);
        await expect(page).not.toHaveURL('/login');
      }
      
      // Test denied routes
      for (const route of test.deniedRoutes) {
        await page.goto(route);
        await expect(page).toHaveURL('/login');
      }
      
      // Logout for next test
      await page.goto('/logout');
    }
  });
});
