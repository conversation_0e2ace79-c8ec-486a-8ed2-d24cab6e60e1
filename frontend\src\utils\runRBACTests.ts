/**
 * RBAC Test Runner
 * Simple script to run and validate the role-based access control system
 */

import { runRBACValidationTests, testRouteAccess } from './rbacValidationTest';

/**
 * Main test runner function
 */
export function runAllRBACTests(): void {
  console.log('🚀 Starting Comprehensive RBAC Testing...\n');
  console.log('=' .repeat(60));
  
  try {
    // Run basic RBAC validation tests
    const validationResults = runRBACValidationTests();
    
    // Run route access tests
    testRouteAccess();
    
    console.log('\n' + '='.repeat(60));
    console.log('🎯 FINAL RESULTS:');
    console.log(`✅ Tests Passed: ${validationResults.passed}`);
    console.log(`❌ Tests Failed: ${validationResults.failed}`);
    console.log(`📊 Overall Success Rate: ${((validationResults.passed / (validationResults.passed + validationResults.failed)) * 100).toFixed(1)}%`);
    
    if (validationResults.failed === 0) {
      console.log('\n🎉 ALL TESTS PASSED! RBAC system is working correctly.');
    } else {
      console.log('\n⚠️ Some tests failed. Please review the results above.');
      
      // Show failed tests
      const failedTests = validationResults.results.filter(r => !r.passed);
      if (failedTests.length > 0) {
        console.log('\n❌ Failed Tests:');
        failedTests.forEach(test => {
          console.log(`  - ${test.test}: expected ${test.expected}, got ${test.actual}`);
        });
      }
    }
    
    console.log('\n' + '='.repeat(60));
    
  } catch (error) {
    console.error('💥 Error running RBAC tests:', error);
  }
}

// Auto-run tests if this file is executed directly
if (typeof window !== 'undefined' && (window as any).runRBACTests) {
  runAllRBACTests();
}

// Export for manual testing
export { runRBACValidationTests, testRouteAccess };
