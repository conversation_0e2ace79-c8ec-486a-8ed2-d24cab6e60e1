export class LoginPage {
  // Selectors
  private selectors = {
    emailInput: '[data-testid="email-input"]',
    passwordInput: '[data-testid="password-input"]',
    loginButton: '[data-testid="login-button"]',
    forgotPasswordLink: '[data-testid="forgot-password-link"]',
    signupLink: '[data-testid="signup-link"]',
    errorMessage: '[data-testid="error-message"]',
    loadingSpinner: '[data-testid="loading-spinner"]',
    languageSelector: '[data-testid="language-selector"]',
    rememberMeCheckbox: '[data-testid="remember-me-checkbox"]'
  }

  // Navigation
  visit() {
    cy.visit('/login')
    cy.waitForPageLoad()
    return this
  }

  // Actions
  enterEmail(email: string) {
    cy.get(this.selectors.emailInput).clear().type(email)
    return this
  }

  enterPassword(password: string) {
    cy.get(this.selectors.passwordInput).clear().type(password)
    return this
  }

  clickLogin() {
    cy.get(this.selectors.loginButton).click()
    return this
  }

  clickForgotPassword() {
    cy.get(this.selectors.forgotPasswordLink).click()
    return this
  }

  clickSignup() {
    cy.get(this.selectors.signupLink).click()
    return this
  }

  toggleRememberMe() {
    cy.get(this.selectors.rememberMeCheckbox).click()
    return this
  }

  switchLanguage(language: 'en' | 'ar') {
    cy.get(this.selectors.languageSelector).click()
    cy.getByTestId(`language-${language}`).click()
    return this
  }

  // Complete login flow
  login(email: string, password: string, rememberMe: boolean = false) {
    this.enterEmail(email)
    this.enterPassword(password)
    
    if (rememberMe) {
      this.toggleRememberMe()
    }
    
    this.clickLogin()
    return this
  }

  // Assertions
  shouldShowErrorMessage(message?: string) {
    cy.get(this.selectors.errorMessage).should('be.visible')
    
    if (message) {
      cy.get(this.selectors.errorMessage).should('contain.text', message)
    }
    
    return this
  }

  shouldShowLoadingSpinner() {
    cy.get(this.selectors.loadingSpinner).should('be.visible')
    return this
  }

  shouldHideLoadingSpinner() {
    cy.get(this.selectors.loadingSpinner).should('not.exist')
    return this
  }

  shouldRedirectToDashboard(role: string = 'user') {
    const expectedPaths = {
      admin: '/admin/dashboard',
      superadmin: '/superadmin/dashboard',
      mentor: '/mentor/dashboard',
      user: '/dashboard'
    }
    
    const expectedPath = expectedPaths[role as keyof typeof expectedPaths] || '/dashboard'
    cy.url().should('include', expectedPath)
    return this
  }

  shouldHaveValidationErrors() {
    // Check for form validation errors
    cy.get(this.selectors.emailInput).should('have.class', 'error')
      .or('have.attr', 'aria-invalid', 'true')
    
    cy.get(this.selectors.passwordInput).should('have.class', 'error')
      .or('have.attr', 'aria-invalid', 'true')
    
    return this
  }

  shouldBeAccessible() {
    // Check accessibility
    cy.checkA11y()
    
    // Check form labels
    cy.get(this.selectors.emailInput).should('have.attr', 'aria-label')
      .or('have.attr', 'aria-labelledby')
    
    cy.get(this.selectors.passwordInput).should('have.attr', 'aria-label')
      .or('have.attr', 'aria-labelledby')
    
    // Check button accessibility
    cy.get(this.selectors.loginButton).should('have.attr', 'type', 'submit')
      .and('not.have.attr', 'disabled')
    
    return this
  }

  shouldSupportKeyboardNavigation() {
    // Test tab navigation
    cy.get(this.selectors.emailInput).focus()
    cy.realPress('Tab')
    cy.get(this.selectors.passwordInput).should('be.focused')
    
    cy.realPress('Tab')
    cy.get(this.selectors.loginButton).should('be.focused')
    
    return this
  }

  shouldHandleInvalidCredentials() {
    this.login('<EMAIL>', 'wrongpassword')
    this.shouldShowErrorMessage('Invalid credentials')
    this.shouldHideLoadingSpinner()
    
    // Should remain on login page
    cy.url().should('include', '/login')
    
    return this
  }

  shouldValidateEmailFormat() {
    this.enterEmail('invalid-email')
    this.enterPassword('password123')
    this.clickLogin()
    
    // Should show email validation error
    cy.get(this.selectors.emailInput).should('have.attr', 'aria-invalid', 'true')
    
    return this
  }

  shouldRequirePassword() {
    this.enterEmail('<EMAIL>')
    this.clickLogin()
    
    // Should show password required error
    cy.get(this.selectors.passwordInput).should('have.attr', 'aria-invalid', 'true')
    
    return this
  }

  // Performance testing
  shouldLoadQuickly() {
    cy.startPerformanceTimer('login-page-load')
    this.visit()
    cy.endPerformanceTimer('login-page-load', 3000) // Should load within 3 seconds
    
    return this
  }

  // Security testing
  shouldNotExposePasswordInDOM() {
    this.enterPassword('secretpassword')
    
    // Password should be masked
    cy.get(this.selectors.passwordInput).should('have.attr', 'type', 'password')
    
    // Password should not be visible in DOM
    cy.get('body').should('not.contain.text', 'secretpassword')
    
    return this
  }
}
