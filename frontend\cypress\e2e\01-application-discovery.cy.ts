describe('Application Discovery', () => {
  it('should explore the application structure', () => {
    cy.visit('/')
    
    // Log the page title
    cy.title().then((title) => {
      cy.log(`Page title: ${title}`)
    })
    
    // Check what's in the body
    cy.get('body').then(($body) => {
      cy.log(`Body classes: ${$body.attr('class')}`)
    })
    
    // Look for common navigation elements
    cy.get('nav, [role="navigation"]').then(($nav) => {
      if ($nav.length > 0) {
        cy.log('Found navigation element')
        cy.wrap($nav).should('be.visible')
      }
    })
    
    // Look for login-related elements
    cy.get('body').then(($body) => {
      const loginElements = $body.find('a[href*="login"], button:contains("Login"), input[type="email"], input[type="password"]')
      if (loginElements.length > 0) {
        cy.log(`Found ${loginElements.length} login-related elements`)
      }
    })
    
    // Look for common page elements
    cy.get('header, main, footer, aside, .sidebar, .navbar').then(($elements) => {
      if ($elements.length > 0) {
        cy.log(`Found ${$elements.length} structural elements`)
      }
    })
    
    // Check for React components
    cy.get('[data-reactroot], #root').should('exist')
    
    // Look for any forms
    cy.get('form').then(($forms) => {
      if ($forms.length > 0) {
        cy.log(`Found ${$forms.length} forms`)
      }
    })
    
    // Check for buttons and links
    cy.get('button, a').then(($interactive) => {
      cy.log(`Found ${$interactive.length} interactive elements`)
    })
  })

  it('should check for login page', () => {
    // Try common login routes
    const loginRoutes = ['/login', '/signin', '/auth', '/auth/login']
    
    loginRoutes.forEach((route) => {
      cy.visit(route, { failOnStatusCode: false })
      cy.url().then((url) => {
        cy.log(`Visited ${route}, current URL: ${url}`)
      })
      
      // Check if this looks like a login page
      cy.get('body').then(($body) => {
        const hasEmailInput = $body.find('input[type="email"], input[name*="email"], input[placeholder*="email"]').length > 0
        const hasPasswordInput = $body.find('input[type="password"], input[name*="password"]').length > 0
        const hasLoginButton = $body.find('button:contains("Login"), button:contains("Sign"), input[type="submit"]').length > 0
        
        if (hasEmailInput && hasPasswordInput) {
          cy.log(`${route} appears to be a login page`)
        }
      })
    })
  })

  it('should check for dashboard or main app pages', () => {
    const appRoutes = ['/dashboard', '/home', '/app', '/main', '/admin']
    
    appRoutes.forEach((route) => {
      cy.visit(route, { failOnStatusCode: false })
      cy.url().then((url) => {
        cy.log(`Visited ${route}, current URL: ${url}`)
      })
      
      // Check if this looks like an app page
      cy.get('body').then(($body) => {
        const hasSidebar = $body.find('.sidebar, nav, [role="navigation"]').length > 0
        const hasMainContent = $body.find('main, .main, .content').length > 0
        
        if (hasSidebar || hasMainContent) {
          cy.log(`${route} appears to be an application page`)
        }
      })
    })
  })

  it('should identify available routes', () => {
    cy.visit('/')
    
    // Look for all links on the page
    cy.get('a[href]').then(($links) => {
      const routes = new Set()
      $links.each((index, link) => {
        const href = link.getAttribute('href')
        if (href && href.startsWith('/')) {
          routes.add(href)
        }
      })
      
      cy.log(`Found routes: ${Array.from(routes).join(', ')}`)
    })
  })
})
