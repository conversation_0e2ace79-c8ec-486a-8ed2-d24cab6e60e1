/**
 * Analytics Hook
 * Custom hook for fetching analytics data
 */

import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '../services/api';

export interface AnalyticsData {
  overview: {
    totalBusinessIdeas: number;
    totalBusinessPlans: number;
    completedPlans: number;
    timeSpent: number; // in hours
  };
  progress: {
    weeklyProgress: number[];
    monthlyGoals: {
      target: number;
      achieved: number;
    };
  };
  activity: {
    recentActions: Array<{
      id: number;
      action: string;
      timestamp: string;
      type: 'idea' | 'plan' | 'template' | 'ai';
    }>;
  };
  charts: {
    businessIdeasByCategory: Array<{
      category: string;
      count: number;
    }>;
    planCompletionRate: Array<{
      month: string;
      completed: number;
      total: number;
    }>;
  };
}

/**
 * Hook for fetching user analytics data
 */
export function useAnalytics() {
  const {
    data: analyticsData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['analytics', 'dashboard'],
    queryFn: async () => {
      try {
        return await apiRequest<AnalyticsData>('/api/analytics/dashboard/', 'GET');
      } catch (error) {
        console.error('Failed to fetch analytics data:', error);
        console.warn('Analytics API not available, returning fallback data');
        return getFallbackAnalyticsData();
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  return {
    analyticsData,
    isLoading,
    error,
    refetch,
  };
}

/**
 * Hook for business ideas analytics
 */
export function useBusinessIdeasAnalytics() {
  const {
    data: ideasAnalytics,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['analytics', 'business-ideas'],
    queryFn: async () => {
      try {
        return await apiRequest('/api/analytics/business-ideas/', 'GET');
      } catch (error) {
        console.error('Failed to fetch business ideas analytics:', error);
        return null;
      }
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  return {
    ideasAnalytics,
    isLoading,
    error,
    refetch,
  };
}

/**
 * Hook for business plans analytics
 */
export function useBusinessPlansAnalytics() {
  const {
    data: plansAnalytics,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['analytics', 'business-plans'],
    queryFn: async () => {
      try {
        return await apiRequest('/api/analytics/business-plans/', 'GET');
      } catch (error) {
        console.error('Failed to fetch business plans analytics:', error);
        return null;
      }
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  return {
    plansAnalytics,
    isLoading,
    error,
    refetch,
  };
}

/**
 * Hook for time tracking analytics
 */
export function useTimeTrackingAnalytics() {
  const {
    data: timeData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['analytics', 'time-tracking'],
    queryFn: async () => {
      try {
        return await apiRequest('/api/analytics/time-tracking/', 'GET');
      } catch (error) {
        console.error('Failed to fetch time tracking data:', error);
        return null;
      }
    },
    staleTime: 15 * 60 * 1000, // 15 minutes
  });

  return {
    timeData,
    isLoading,
    error,
    refetch,
  };
}

/**
 * Fallback analytics data when API is not available
 */
function getFallbackAnalyticsData(): AnalyticsData {
  return {
    overview: {
      totalBusinessIdeas: 0,
      totalBusinessPlans: 0,
      completedPlans: 0,
      timeSpent: 0
    },
    progress: {
      weeklyProgress: [0, 0, 0, 0, 0, 0, 0],
      monthlyGoals: {
        target: 3,
        achieved: 0
      }
    },
    activity: {
      recentActions: [
        {
          id: 1,
          action: 'Welcome to the analytics dashboard!',
          timestamp: new Date().toISOString(),
          type: 'plan'
        }
      ]
    },
    charts: {
      businessIdeasByCategory: [
        { category: 'Technology', count: 0 },
        { category: 'Healthcare', count: 0 },
        { category: 'Education', count: 0 },
        { category: 'Finance', count: 0 }
      ],
      planCompletionRate: [
        { month: 'Jan', completed: 0, total: 0 },
        { month: 'Feb', completed: 0, total: 0 },
        { month: 'Mar', completed: 0, total: 0 },
        { month: 'Apr', completed: 0, total: 0 },
        { month: 'May', completed: 0, total: 0 },
        { month: 'Jun', completed: 0, total: 0 }
      ]
    }
  };
}

export default useAnalytics;
