import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  BookOpen,
  Download,
  Video,
  FileText,
  Users,
  Star,
  Clock,
  Search,
  Filter,
  ArrowRight,
  Play,
  ExternalLink,
  AlertCircle,
  Tag,
  Calendar,
  Eye
} from 'lucide-react';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { getUserRole } from '../../utils/unifiedRoleManager';

const MentorshipResourcesPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  
  const { user, isAuthenticated } = useAppSelector(state => state.auth);
  const userRole = getUserRole(user);

  // Redirect if not a mentor
  useEffect(() => {
    if (isAuthenticated && userRole !== 'mentor') {
      console.warn('Access denied: User is not a mentor');
      navigate('/dashboard');
    }
  }, [isAuthenticated, userRole, navigate]);

  // Mock resources data - replace with real API calls
  const [resources, setResources] = useState([
    {
      id: 1,
      title: 'Effective Mentoring Techniques',
      description: 'Learn proven strategies for guiding entrepreneurs to success',
      type: 'guide',
      category: 'Best Practices',
      duration: '15 min read',
      rating: 4.8,
      downloads: 1234,
      tags: ['mentoring', 'techniques', 'guidance'],
      url: '/resources/mentoring-techniques.pdf',
      thumbnail: null,
      isNew: true
    },
    {
      id: 2,
      title: 'Business Plan Evaluation Framework',
      description: 'Comprehensive framework for reviewing and providing feedback on business plans',
      type: 'template',
      category: 'Templates',
      duration: 'Template',
      rating: 4.9,
      downloads: 856,
      tags: ['business plan', 'evaluation', 'framework'],
      url: '/resources/business-plan-framework.docx',
      thumbnail: null,
      isNew: false
    },
    {
      id: 3,
      title: 'Conducting Virtual Mentoring Sessions',
      description: 'Best practices for effective online mentoring and video calls',
      type: 'video',
      category: 'Training',
      duration: '25 min',
      rating: 4.7,
      downloads: 642,
      tags: ['virtual', 'online', 'video calls'],
      url: '/resources/virtual-mentoring-video.mp4',
      thumbnail: null,
      isNew: false
    },
    {
      id: 4,
      title: 'Startup Funding Landscape 2024',
      description: 'Current trends and opportunities in startup funding',
      type: 'report',
      category: 'Market Insights',
      duration: '30 min read',
      rating: 4.6,
      downloads: 423,
      tags: ['funding', 'startups', '2024', 'trends'],
      url: '/resources/funding-landscape-2024.pdf',
      thumbnail: null,
      isNew: true
    },
    {
      id: 5,
      title: 'Mentee Goal Setting Worksheet',
      description: 'Structured worksheet to help mentees define and track their goals',
      type: 'worksheet',
      category: 'Tools',
      duration: 'Worksheet',
      rating: 4.5,
      downloads: 789,
      tags: ['goals', 'worksheet', 'tracking'],
      url: '/resources/goal-setting-worksheet.pdf',
      thumbnail: null,
      isNew: false
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');

  const categories = ['All', 'Best Practices', 'Templates', 'Training', 'Market Insights', 'Tools'];
  const types = ['All', 'Guide', 'Template', 'Video', 'Report', 'Worksheet'];

  const filteredResources = resources.filter(resource => {
    const matchesSearch = resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         resource.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         resource.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = categoryFilter === 'all' || resource.category.toLowerCase() === categoryFilter.toLowerCase();
    const matchesType = typeFilter === 'all' || resource.type.toLowerCase() === typeFilter.toLowerCase();
    return matchesSearch && matchesCategory && matchesType;
  });

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'guide': return FileText;
      case 'template': return FileText;
      case 'video': return Video;
      case 'report': return BookOpen;
      case 'worksheet': return FileText;
      default: return FileText;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'guide': return 'bg-blue-600/20 text-blue-400';
      case 'template': return 'bg-green-600/20 text-green-400';
      case 'video': return 'bg-red-600/20 text-red-400';
      case 'report': return 'bg-purple-600/20 text-purple-400';
      case 'worksheet': return 'bg-yellow-600/20 text-yellow-400';
      default: return 'bg-gray-600/20 text-gray-400';
    }
  };

  // Don't render if not authenticated or not a mentor
  if (!isAuthenticated || userRole !== 'mentor') {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 flex items-center justify-center">
        <div className="text-center text-white">
          <AlertCircle size={48} className="mx-auto mb-4 text-red-400" />
          <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
          <p className="text-gray-300">This page is only accessible to mentors.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <h1 className="text-3xl font-bold text-white">Mentorship Resources</h1>
            <p className="text-gray-300 mt-1">
              Tools, guides, and materials to enhance your mentoring effectiveness
            </p>
          </div>
          <div className={`flex space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <Link
              to="/dashboard/mentorship"
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white flex items-center"
            >
              <ArrowRight size={18} className={`mr-2 ${isRTL ? "ml-2 mr-0" : ""}`} />
              Back to Mentorship
            </Link>
          </div>
        </div>

        {/* Search and Filters */}
        <div className={`flex flex-col lg:flex-row gap-4 mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="relative flex-1">
            <Search size={20} className={`absolute top-3 text-gray-400 ${isRTL ? "right-3" : "left-3"}`} />
            <input
              type="text"
              placeholder="Search resources..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full bg-white/10 border border-white/20 rounded-lg py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 ${isRTL ? "pr-10 pl-4" : "pl-10 pr-4"}`}
            />
          </div>
          <select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            className="bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            {categories.map(category => (
              <option key={category} value={category.toLowerCase()}>
                {category}
              </option>
            ))}
          </select>
          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            {types.map(type => (
              <option key={type} value={type.toLowerCase()}>
                {type}
              </option>
            ))}
          </select>
        </div>

        {/* Resources Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredResources.map(resource => {
            const TypeIcon = getTypeIcon(resource.type);
            return (
              <div key={resource.id} className="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 hover:bg-white/15 transition-colors">
                {/* Resource Header */}
                <div className="p-6">
                  <div className={`flex items-start justify-between mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className="w-10 h-10 rounded-lg bg-purple-600 flex items-center justify-center mr-3">
                        <TypeIcon size={20} className="text-white" />
                      </div>
                      <div>
                        <span className={`text-xs px-2 py-1 rounded ${getTypeColor(resource.type)}`}>
                          {resource.type}
                        </span>
                        {resource.isNew && (
                          <span className="text-xs bg-green-600/20 text-green-400 px-2 py-1 rounded ml-2">
                            New
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  <h3 className="text-lg font-semibold text-white mb-2">{resource.title}</h3>
                  <p className="text-gray-400 text-sm mb-3 line-clamp-2">{resource.description}</p>

                  {/* Tags */}
                  <div className={`flex flex-wrap gap-1 mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                    {resource.tags.slice(0, 3).map(tag => (
                      <span key={tag} className="text-xs bg-white/10 text-gray-300 px-2 py-1 rounded">
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* Stats */}
                  <div className={`flex justify-between items-center text-sm text-gray-400 mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                      <span className="flex items-center">
                        <Clock size={14} className={`mr-1 ${isRTL ? "ml-1 mr-0" : ""}`} />
                        {resource.duration}
                      </span>
                      <span className="flex items-center">
                        <Download size={14} className={`mr-1 ${isRTL ? "ml-1 mr-0" : ""}`} />
                        {resource.downloads}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <Star size={14} className="text-yellow-400 mr-1" />
                      <span className="text-white">{resource.rating}</span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                    <button className="flex-1 bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded text-sm flex items-center justify-center">
                      {resource.type === 'video' ? (
                        <>
                          <Play size={16} className={`mr-1 ${isRTL ? "ml-1 mr-0" : ""}`} />
                          Watch
                        </>
                      ) : (
                        <>
                          <Download size={16} className={`mr-1 ${isRTL ? "ml-1 mr-0" : ""}`} />
                          Download
                        </>
                      )}
                    </button>
                    <button className="bg-white/20 hover:bg-white/30 text-white p-2 rounded">
                      <ExternalLink size={16} />
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Empty State */}
        {filteredResources.length === 0 && (
          <div className="text-center py-12">
            <BookOpen size={48} className="mx-auto text-gray-400 mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">No resources found</h3>
            <p className="text-gray-400">
              {searchTerm || categoryFilter !== 'all' || typeFilter !== 'all'
                ? 'Try adjusting your search or filter criteria'
                : 'No mentorship resources are available at the moment'
              }
            </p>
          </div>
        )}

        {/* Resource Categories */}
        <div className="mt-12">
          <h2 className="text-xl font-semibold text-white mb-6">Resource Categories</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-center mb-3">
                <FileText className="text-blue-400 mr-3" size={24} />
                <h3 className="text-lg font-medium text-white">Guides & Best Practices</h3>
              </div>
              <p className="text-gray-400 text-sm">Comprehensive guides on effective mentoring techniques and strategies</p>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-center mb-3">
                <Video className="text-red-400 mr-3" size={24} />
                <h3 className="text-lg font-medium text-white">Training Videos</h3>
              </div>
              <p className="text-gray-400 text-sm">Video tutorials and training sessions for mentor development</p>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-center mb-3">
                <BookOpen className="text-green-400 mr-3" size={24} />
                <h3 className="text-lg font-medium text-white">Templates & Tools</h3>
              </div>
              <p className="text-gray-400 text-sm">Ready-to-use templates and tools for mentoring sessions</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MentorshipResourcesPage;
