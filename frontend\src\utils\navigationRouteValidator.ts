/**
 * NAVIGATION-ROUTE VALIDATOR
 * Ensures perfect alignment between navigation items and route definitions
 */

import { NAVIGATION_ITEMS, NavItem } from '../config/navigationConfig';
import { ROUTE_ROLE_MAPPINGS, RouteRoleMapping } from '../config/centralizedRoleRouteMapping';
import { UserRole } from './unifiedRoleManager';

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  summary: ValidationSummary;
}

export interface ValidationError {
  type: 'MISSING_ROUTE' | 'MISSING_NAVIGATION' | 'ROLE_MISMATCH' | 'PERMISSION_MISMATCH';
  severity: 'CRITICAL' | 'HIGH' | 'MEDIUM';
  item: string;
  details: string;
  fix: string;
}

export interface ValidationWarning {
  type: 'OVERLY_PERMISSIVE' | 'OVERLY_RESTRICTIVE' | 'INCONSISTENT_CATEGORY';
  item: string;
  details: string;
  recommendation: string;
}

export interface ValidationSummary {
  totalNavigationItems: number;
  totalRoutes: number;
  matchedItems: number;
  missingRoutes: number;
  missingNavigation: number;
  roleMismatches: number;
  consistencyScore: number;
}

/**
 * Comprehensive validation of navigation-route alignment
 */
export function validateNavigationRouteAlignment(): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationWarning[] = [];
  
  // Get all navigation paths and route paths
  const navigationPaths = NAVIGATION_ITEMS.map(item => item.path);
  const routePaths = ROUTE_ROLE_MAPPINGS.map(route => route.path);
  
  // Find missing routes (navigation items without corresponding routes)
  const missingRoutes = navigationPaths.filter(path => !routePaths.includes(path));
  missingRoutes.forEach(path => {
    const navItem = NAVIGATION_ITEMS.find(item => item.path === path);
    errors.push({
      type: 'MISSING_ROUTE',
      severity: 'CRITICAL',
      item: path,
      details: `Navigation item "${navItem?.id}" has no corresponding route`,
      fix: `Add route definition for ${path} in consolidatedRoutes.ts`
    });
  });
  
  // Find missing navigation (routes without navigation items)
  const protectedRoutePaths = ROUTE_ROLE_MAPPINGS
    .filter(route => route.requireAuth && !route.path.startsWith('/login') && !route.path.startsWith('/register') && route.path !== '/')
    .map(route => route.path);
  
  const missingNavigation = protectedRoutePaths.filter(path => !navigationPaths.includes(path));
  missingNavigation.forEach(path => {
    const route = ROUTE_ROLE_MAPPINGS.find(r => r.path === path);
    if (route && route.category !== 'auth') { // Skip auth routes
      errors.push({
        type: 'MISSING_NAVIGATION',
        severity: 'MEDIUM',
        item: path,
        details: `Route "${path}" has no navigation item`,
        fix: `Add navigation item for ${path} in navigationConfig.ts or remove route if not needed`
      });
    }
  });
  
  // Check role alignment for matched items
  let roleMismatches = 0;
  NAVIGATION_ITEMS.forEach(navItem => {
    const route = ROUTE_ROLE_MAPPINGS.find(r => r.path === navItem.path);
    if (route) {
      // Check if roles match exactly
      const navRoles = [...navItem.allowedRoles].sort();
      const routeRoles = [...route.allowedRoles].sort();
      
      if (JSON.stringify(navRoles) !== JSON.stringify(routeRoles)) {
        roleMismatches++;
        errors.push({
          type: 'ROLE_MISMATCH',
          severity: 'HIGH',
          item: navItem.path,
          details: `Navigation roles [${navRoles.join(', ')}] don't match route roles [${routeRoles.join(', ')}]`,
          fix: `Synchronize roles in navigationConfig.ts and centralizedRoleRouteMapping.ts`
        });
      }
    }
  });
  
  // Check for overly permissive or restrictive access
  NAVIGATION_ITEMS.forEach(navItem => {
    if (navItem.allowedRoles.length >= 5) {
      warnings.push({
        type: 'OVERLY_PERMISSIVE',
        item: navItem.id,
        details: `Item accessible to ${navItem.allowedRoles.length} roles: [${navItem.allowedRoles.join(', ')}]`,
        recommendation: 'Consider if all these roles really need access'
      });
    }
    
    if (navItem.allowedRoles.length === 1 && navItem.allowedRoles[0] !== 'super_admin') {
      warnings.push({
        type: 'OVERLY_RESTRICTIVE',
        item: navItem.id,
        details: `Item restricted to single role: ${navItem.allowedRoles[0]}`,
        recommendation: 'Consider if other roles should have access'
      });
    }
  });
  
  // Calculate summary
  const summary: ValidationSummary = {
    totalNavigationItems: NAVIGATION_ITEMS.length,
    totalRoutes: ROUTE_ROLE_MAPPINGS.filter(r => r.requireAuth).length,
    matchedItems: NAVIGATION_ITEMS.length - missingRoutes.length,
    missingRoutes: missingRoutes.length,
    missingNavigation: missingNavigation.length,
    roleMismatches,
    consistencyScore: Math.max(0, 100 - (errors.length * 10) - (warnings.length * 2))
  };
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    summary
  };
}

/**
 * Validate specific navigation item against its route
 */
export function validateNavigationItem(itemId: string): ValidationResult {
  const navItem = NAVIGATION_ITEMS.find(item => item.id === itemId);
  if (!navItem) {
    return {
      isValid: false,
      errors: [{
        type: 'MISSING_NAVIGATION',
        severity: 'CRITICAL',
        item: itemId,
        details: 'Navigation item not found',
        fix: 'Check if item ID is correct'
      }],
      warnings: [],
      summary: {
        totalNavigationItems: 0,
        totalRoutes: 0,
        matchedItems: 0,
        missingRoutes: 1,
        missingNavigation: 0,
        roleMismatches: 0,
        consistencyScore: 0
      }
    };
  }
  
  const route = ROUTE_ROLE_MAPPINGS.find(r => r.path === navItem.path);
  const errors: ValidationError[] = [];
  
  if (!route) {
    errors.push({
      type: 'MISSING_ROUTE',
      severity: 'CRITICAL',
      item: navItem.path,
      details: `No route found for navigation item "${itemId}"`,
      fix: `Add route definition for ${navItem.path}`
    });
  } else {
    // Check role alignment
    const navRoles = [...navItem.allowedRoles].sort();
    const routeRoles = [...route.allowedRoles].sort();
    
    if (JSON.stringify(navRoles) !== JSON.stringify(routeRoles)) {
      errors.push({
        type: 'ROLE_MISMATCH',
        severity: 'HIGH',
        item: navItem.path,
        details: `Roles don't match - Nav: [${navRoles.join(', ')}], Route: [${routeRoles.join(', ')}]`,
        fix: 'Synchronize role definitions'
      });
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings: [],
    summary: {
      totalNavigationItems: 1,
      totalRoutes: route ? 1 : 0,
      matchedItems: route ? 1 : 0,
      missingRoutes: route ? 0 : 1,
      missingNavigation: 0,
      roleMismatches: errors.filter(e => e.type === 'ROLE_MISMATCH').length,
      consistencyScore: errors.length === 0 ? 100 : 0
    }
  };
}

/**
 * Get navigation items accessible by a specific role
 */
export function getValidatedNavigationForRole(role: UserRole): NavItem[] {
  return NAVIGATION_ITEMS.filter(item => {
    // Check if navigation item allows this role
    if (!item.allowedRoles.includes(role)) return false;
    
    // Check if corresponding route also allows this role
    const route = ROUTE_ROLE_MAPPINGS.find(r => r.path === item.path);
    if (!route) return false; // Skip items without routes
    
    return route.allowedRoles.length === 0 || route.allowedRoles.includes(role);
  });
}

/**
 * Auto-fix navigation-route mismatches
 */
export function generateNavigationRouteFixes(): string[] {
  const validation = validateNavigationRouteAlignment();
  const fixes: string[] = [];
  
  validation.errors.forEach(error => {
    switch (error.type) {
      case 'MISSING_ROUTE':
        fixes.push(`// Add to consolidatedRoutes.ts:\ncreateUnifiedRoute('${error.item}', YourComponent, 'Loading...')`);
        break;
      case 'MISSING_NAVIGATION':
        fixes.push(`// Add to navigationConfig.ts:\n{\n  id: 'new-item',\n  name: 'item.title',\n  path: '${error.item}',\n  icon: 'Icon',\n  allowedRoles: ['user'], // Update as needed\n  category: 'main'\n}`);
        break;
      case 'ROLE_MISMATCH':
        fixes.push(`// Fix role mismatch for ${error.item}:\n// Update either navigationConfig.ts or centralizedRoleRouteMapping.ts to match`);
        break;
    }
  });
  
  return fixes;
}

/**
 * Browser console helper
 */
if (typeof window !== 'undefined') {
  (window as any).navigationRouteValidator = {
    validate: validateNavigationRouteAlignment,
    validateItem: validateNavigationItem,
    getNavigationForRole: getValidatedNavigationForRole,
    generateFixes: generateNavigationRouteFixes
  };
}
