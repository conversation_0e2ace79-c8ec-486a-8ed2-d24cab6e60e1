/**
 * Automated Performance Tracking System
 * Monitors application performance in real-time and sends alerts
 */

interface PerformanceMetrics {
  pageLoadTime: number;
  navigationTime: number;
  renderTime: number;
  memoryUsage: number;
  bundleSize: number;
  errorCount: number;
  userInteractions: number;
  timestamp: number;
}

interface PerformanceThresholds {
  pageLoadTime: number; // ms
  navigationTime: number; // ms
  renderTime: number; // ms
  memoryUsage: number; // MB
  errorRate: number; // percentage
}

class PerformanceTracker {
  private metrics: PerformanceMetrics[] = [];
  private thresholds: PerformanceThresholds = {
    pageLoadTime: 3000, // 3 seconds
    navigationTime: 1000, // 1 second
    renderTime: 16, // 16ms for 60fps
    memoryUsage: 50, // 50MB
    errorRate: 1 // 1%
  };
  private isTracking = false;
  private observer?: PerformanceObserver;

  constructor() {
    this.initializeTracking();
  }

  /**
   * Initialize performance tracking
   */
  private initializeTracking() {
    if (typeof window === 'undefined' || this.isTracking) return;

    this.isTracking = true;
    this.setupPerformanceObserver();
    this.trackPageLoad();
    this.trackMemoryUsage();
    this.trackUserInteractions();
    this.setupErrorTracking();
  }

  /**
   * Setup Performance Observer for detailed metrics
   */
  private setupPerformanceObserver() {
    if (!('PerformanceObserver' in window)) return;

    try {
      this.observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        
        entries.forEach((entry) => {
          if (entry.entryType === 'navigation') {
            this.recordNavigationMetrics(entry as PerformanceNavigationTiming);
          } else if (entry.entryType === 'paint') {
            this.recordPaintMetrics(entry);
          } else if (entry.entryType === 'measure') {
            this.recordCustomMetrics(entry);
          }
        });
      });

      this.observer.observe({ 
        entryTypes: ['navigation', 'paint', 'measure', 'largest-contentful-paint'] 
      });
    } catch (error) {
      console.warn('Performance Observer not supported:', error);
    }
  }

  /**
   * Record navigation performance metrics
   */
  private recordNavigationMetrics(entry: PerformanceNavigationTiming) {
    const metrics: PerformanceMetrics = {
      pageLoadTime: entry.loadEventEnd - entry.loadEventStart,
      navigationTime: entry.loadEventEnd - entry.fetchStart,
      renderTime: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
      memoryUsage: this.getMemoryUsage(),
      bundleSize: this.estimateBundleSize(),
      errorCount: this.getErrorCount(),
      userInteractions: this.getUserInteractionCount(),
      timestamp: Date.now()
    };

    this.metrics.push(metrics);
    this.checkThresholds(metrics);
    this.sendMetricsToAnalytics(metrics);
  }

  /**
   * Record paint metrics (FCP, LCP)
   */
  private recordPaintMetrics(entry: PerformanceEntry) {
    console.log(`${entry.name}: ${entry.startTime}ms`);
    
    // Track First Contentful Paint and Largest Contentful Paint
    if (entry.name === 'first-contentful-paint' && entry.startTime > 2000) {
      this.sendAlert('Slow First Contentful Paint', `FCP: ${entry.startTime}ms`);
    }
  }

  /**
   * Record custom performance metrics
   */
  private recordCustomMetrics(entry: PerformanceEntry) {
    if (entry.duration > this.thresholds.renderTime) {
      console.warn(`Slow operation detected: ${entry.name} took ${entry.duration}ms`);
    }
  }

  /**
   * Track page load performance
   */
  private trackPageLoad() {
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        if (navigation) {
          const loadTime = navigation.loadEventEnd - navigation.loadEventStart;
          
          if (loadTime > this.thresholds.pageLoadTime) {
            this.sendAlert('Slow Page Load', `Load time: ${loadTime}ms`);
          }
        }
      }, 0);
    });
  }

  /**
   * Track memory usage
   */
  private trackMemoryUsage() {
    setInterval(() => {
      const memoryUsage = this.getMemoryUsage();
      
      if (memoryUsage > this.thresholds.memoryUsage) {
        this.sendAlert('High Memory Usage', `Memory: ${memoryUsage}MB`);
      }
    }, 30000); // Check every 30 seconds
  }

  /**
   * Track user interactions
   */
  private trackUserInteractions() {
    let interactionCount = 0;
    
    ['click', 'keydown', 'scroll', 'touchstart'].forEach(eventType => {
      document.addEventListener(eventType, () => {
        interactionCount++;
      }, { passive: true });
    });

    // Store interaction count for metrics
    (window as any).__interactionCount = () => interactionCount;
  }

  /**
   * Setup error tracking
   */
  private setupErrorTracking() {
    let errorCount = 0;

    window.addEventListener('error', (event) => {
      errorCount++;
      this.sendAlert('JavaScript Error', event.error?.message || 'Unknown error');
    });

    window.addEventListener('unhandledrejection', (event) => {
      errorCount++;
      this.sendAlert('Unhandled Promise Rejection', event.reason);
    });

    // Store error count for metrics
    (window as any).__errorCount = () => errorCount;
  }

  /**
   * Get current memory usage
   */
  private getMemoryUsage(): number {
    const memory = (performance as any).memory;
    return memory ? memory.usedJSHeapSize / 1024 / 1024 : 0;
  }

  /**
   * Estimate bundle size
   */
  private estimateBundleSize(): number {
    const scripts = Array.from(document.querySelectorAll('script[src]'));
    return scripts.length * 100; // Rough estimate in KB
  }

  /**
   * Get error count
   */
  private getErrorCount(): number {
    return (window as any).__errorCount?.() || 0;
  }

  /**
   * Get user interaction count
   */
  private getUserInteractionCount(): number {
    return (window as any).__interactionCount?.() || 0;
  }

  /**
   * Check if metrics exceed thresholds
   */
  private checkThresholds(metrics: PerformanceMetrics) {
    const alerts: string[] = [];

    if (metrics.pageLoadTime > this.thresholds.pageLoadTime) {
      alerts.push(`Page load time: ${metrics.pageLoadTime}ms`);
    }

    if (metrics.navigationTime > this.thresholds.navigationTime) {
      alerts.push(`Navigation time: ${metrics.navigationTime}ms`);
    }

    if (metrics.memoryUsage > this.thresholds.memoryUsage) {
      alerts.push(`Memory usage: ${metrics.memoryUsage}MB`);
    }

    if (alerts.length > 0) {
      this.sendAlert('Performance Threshold Exceeded', alerts.join(', '));
    }
  }

  /**
   * Send performance alert
   */
  private sendAlert(type: string, message: string) {
    if (process.env.NODE_ENV === 'development') {
      console.warn(`🚨 Performance Alert - ${type}: ${message}`);
    }

    // In production, send to monitoring service
    if (process.env.NODE_ENV === 'production') {
      this.sendToMonitoringService({ type, message, timestamp: Date.now() });
    }
  }

  /**
   * Send metrics to analytics service
   */
  private sendMetricsToAnalytics(metrics: PerformanceMetrics) {
    if (process.env.NODE_ENV === 'production') {
      // Send to analytics service (e.g., Google Analytics, DataDog, etc.)
      fetch('/api/analytics/performance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(metrics)
      }).catch(error => console.warn('Failed to send metrics:', error));
    }
  }

  /**
   * Send to monitoring service
   */
  private sendToMonitoringService(alert: { type: string; message: string; timestamp: number }) {
    // Send to monitoring service (e.g., Sentry, DataDog, New Relic)
    fetch('/api/monitoring/alerts', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(alert)
    }).catch(error => console.warn('Failed to send alert:', error));
  }

  /**
   * Get performance summary
   */
  public getPerformanceSummary() {
    if (this.metrics.length === 0) return null;

    const latest = this.metrics[this.metrics.length - 1];
    const average = this.calculateAverageMetrics();

    return {
      latest,
      average,
      totalMetrics: this.metrics.length,
      thresholds: this.thresholds
    };
  }

  /**
   * Calculate average metrics
   */
  private calculateAverageMetrics(): Partial<PerformanceMetrics> {
    if (this.metrics.length === 0) return {};

    const totals = this.metrics.reduce((acc, metric) => ({
      pageLoadTime: acc.pageLoadTime + metric.pageLoadTime,
      navigationTime: acc.navigationTime + metric.navigationTime,
      renderTime: acc.renderTime + metric.renderTime,
      memoryUsage: acc.memoryUsage + metric.memoryUsage
    }), { pageLoadTime: 0, navigationTime: 0, renderTime: 0, memoryUsage: 0 });

    const count = this.metrics.length;
    return {
      pageLoadTime: totals.pageLoadTime / count,
      navigationTime: totals.navigationTime / count,
      renderTime: totals.renderTime / count,
      memoryUsage: totals.memoryUsage / count
    };
  }

  /**
   * Cleanup tracking
   */
  public cleanup() {
    if (this.observer) {
      this.observer.disconnect();
    }
    this.isTracking = false;
  }
}

// Create global instance
const performanceTracker = new PerformanceTracker();

// Export for use in components
export default performanceTracker;

// Expose to window for debugging
if (process.env.NODE_ENV === 'development') {
  (window as any).__performanceTracker = performanceTracker;
}
