/**
 * Manual Authentication Testing Script
 * This script tests the authentication system by making API calls directly
 * and verifying the responses for different user roles.
 */

// Import fetch for Node.js environment
let fetch;
if (typeof window === 'undefined') {
  // Node.js environment - use node-fetch or built-in fetch
  try {
    fetch = globalThis.fetch || require('node-fetch');
  } catch (e) {
    console.error('❌ fetch is not available. Please install node-fetch: npm install node-fetch');
    process.exit(1);
  }
}

const BASE_URL = 'http://localhost:8000';
const FRONTEND_URL = 'http://localhost:3000';

// Test users from the setup
const TEST_USERS = {
  user: { username: 'testuser', password: 'testpass123', expectedRole: 'user' },
  mentor: { username: 'testmentor', password: 'testpass123', expectedRole: 'mentor' },
  investor: { username: 'testinvestor', password: 'testpass123', expectedRole: 'investor' },
  moderator: { username: 'testmoderator', password: 'testpass123', expectedRole: 'moderator' },
  admin: { username: 'testadmin', password: 'testpass123', expectedRole: 'admin' },
  superadmin: { username: 'testsuperadmin', password: 'testpass123', expectedRole: 'super_admin' }
};

// Test results storage
const testResults = {
  passed: 0,
  failed: 0,
  errors: [],
  details: []
};

// Helper function to make API requests
async function makeRequest(url, method = 'GET', data = null, token = null) {
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
    }
  };

  if (token) {
    options.headers['Authorization'] = `Bearer ${token}`;
  }

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);
    const responseData = await response.json();
    return {
      status: response.status,
      ok: response.ok,
      data: responseData
    };
  } catch (error) {
    return {
      status: 0,
      ok: false,
      error: error.message
    };
  }
}

// Test authentication for a specific user
async function testUserAuthentication(userType, userData) {
  console.log(`\n🧪 Testing authentication for ${userType}...`);
  
  try {
    // Test login
    const loginResponse = await makeRequest(
      `${BASE_URL}/api/auth/token/`,
      'POST',
      {
        username: userData.username,
        password: userData.password
      }
    );

    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${JSON.stringify(loginResponse.data)}`);
    }

    console.log(`✅ Login successful for ${userType}`);
    
    // Verify token and user data
    const { access, refresh, user } = loginResponse.data;
    
    if (!access || !refresh || !user) {
      throw new Error('Missing required authentication data');
    }

    console.log(`✅ Received tokens and user data for ${userType}`);
    
    // Test token verification
    const verifyResponse = await makeRequest(
      `${BASE_URL}/api/auth/token/verify/`,
      'POST',
      { token: access }
    );

    if (!verifyResponse.ok) {
      throw new Error(`Token verification failed: ${JSON.stringify(verifyResponse.data)}`);
    }

    console.log(`✅ Token verification successful for ${userType}`);
    
    // Test user profile endpoint
    const profileResponse = await makeRequest(
      `${BASE_URL}/api/auth/user/`,
      'GET',
      null,
      access
    );

    if (!profileResponse.ok) {
      throw new Error(`Profile fetch failed: ${JSON.stringify(profileResponse.data)}`);
    }

    console.log(`✅ Profile fetch successful for ${userType}`);
    
    // Verify role assignment
    const userRole = user.user_role || user.role || (user.profile && user.profile.primary_role && user.profile.primary_role.name);
    
    if (userRole !== userData.expectedRole) {
      throw new Error(`Role mismatch: expected ${userData.expectedRole}, got ${userRole}`);
    }

    console.log(`✅ Role verification successful for ${userType}: ${userRole}`);
    
    // Test logout
    const logoutResponse = await makeRequest(
      `${BASE_URL}/api/auth/logout/`,
      'POST',
      null,
      access
    );

    // Note: Logout might return 200 or 204, both are acceptable
    if (logoutResponse.status !== 200 && logoutResponse.status !== 204) {
      console.warn(`⚠️  Logout returned status ${logoutResponse.status} for ${userType}`);
    } else {
      console.log(`✅ Logout successful for ${userType}`);
    }

    testResults.passed++;
    testResults.details.push({
      userType,
      status: 'PASSED',
      role: userRole,
      message: 'All authentication tests passed'
    });

  } catch (error) {
    console.error(`❌ Authentication test failed for ${userType}: ${error.message}`);
    testResults.failed++;
    testResults.errors.push(`${userType}: ${error.message}`);
    testResults.details.push({
      userType,
      status: 'FAILED',
      error: error.message
    });
  }
}

// Test invalid credentials
async function testInvalidCredentials() {
  console.log('\n🧪 Testing invalid credentials...');
  
  try {
    const response = await makeRequest(
      `${BASE_URL}/api/auth/token/`,
      'POST',
      {
        username: 'invaliduser',
        password: 'wrongpassword'
      }
    );

    if (response.ok) {
      throw new Error('Login should have failed with invalid credentials');
    }

    console.log('✅ Invalid credentials properly rejected');
    testResults.passed++;
    
  } catch (error) {
    console.error(`❌ Invalid credentials test failed: ${error.message}`);
    testResults.failed++;
    testResults.errors.push(`Invalid credentials: ${error.message}`);
  }
}

// Test API endpoints accessibility by role
async function testRoleBasedAccess() {
  console.log('\n🧪 Testing role-based API access...');
  
  // Login as admin to test admin endpoints
  const adminLogin = await makeRequest(
    `${BASE_URL}/api/auth/token/`,
    'POST',
    {
      username: TEST_USERS.admin.username,
      password: TEST_USERS.admin.password
    }
  );

  if (adminLogin.ok) {
    const adminToken = adminLogin.data.access;
    
    // Test admin dashboard endpoint
    const dashboardResponse = await makeRequest(
      `${BASE_URL}/api/admin/dashboard/`,
      'GET',
      null,
      adminToken
    );

    if (dashboardResponse.ok) {
      console.log('✅ Admin can access admin dashboard');
      testResults.passed++;
    } else {
      console.error('❌ Admin cannot access admin dashboard');
      testResults.failed++;
      testResults.errors.push('Admin dashboard access failed');
    }
  }

  // Login as regular user and test they can't access admin endpoints
  const userLogin = await makeRequest(
    `${BASE_URL}/api/auth/token/`,
    'POST',
    {
      username: TEST_USERS.user.username,
      password: TEST_USERS.user.password
    }
  );

  if (userLogin.ok) {
    const userToken = userLogin.data.access;
    
    // Test that regular user cannot access admin dashboard
    const dashboardResponse = await makeRequest(
      `${BASE_URL}/api/admin/dashboard/`,
      'GET',
      null,
      userToken
    );

    if (!dashboardResponse.ok && (dashboardResponse.status === 403 || dashboardResponse.status === 401)) {
      console.log('✅ Regular user properly denied admin dashboard access');
      testResults.passed++;
    } else {
      console.error('❌ Regular user should not have admin dashboard access');
      testResults.failed++;
      testResults.errors.push('User has unauthorized admin access');
    }
  }
}

// Main test runner
async function runAuthenticationTests() {
  console.log('🚀 Starting Authentication System Tests');
  console.log('=' * 50);
  
  // Test each user type
  for (const [userType, userData] of Object.entries(TEST_USERS)) {
    await testUserAuthentication(userType, userData);
  }
  
  // Test invalid credentials
  await testInvalidCredentials();
  
  // Test role-based access
  await testRoleBasedAccess();
  
  // Print results
  console.log('\n📊 Test Results Summary');
  console.log('=' * 30);
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
  
  if (testResults.errors.length > 0) {
    console.log('\n🚨 Errors:');
    testResults.errors.forEach(error => console.log(`   - ${error}`));
  }
  
  console.log('\n📋 Detailed Results:');
  testResults.details.forEach(detail => {
    const status = detail.status === 'PASSED' ? '✅' : '❌';
    console.log(`   ${status} ${detail.userType}: ${detail.message || detail.error}`);
  });
  
  return testResults;
}

// Export for use in browser console or Node.js
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runAuthenticationTests, TEST_USERS };
} else if (typeof window !== 'undefined') {
  // Make available in browser console
  window.authTests = { runAuthenticationTests, TEST_USERS };
  console.log('🔧 Authentication tests loaded. Run authTests.runAuthenticationTests() to start testing.');
}

// Auto-run if in Node.js environment
if (typeof module !== 'undefined' && module.exports && require.main === module) {
  runAuthenticationTests().catch(console.error);
}
