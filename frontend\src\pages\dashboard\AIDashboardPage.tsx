import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  <PERSON><PERSON>,
  MessageSquare,
  BarChart3,
  FileText,
  Lightbulb,
  TrendingUp,
  Clock,
  Star,
  ArrowRight,
  Zap,
  Brain,
  Target,
  AlertCircle,
  Sparkles,
  Cpu,
  Database
} from 'lucide-react';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { getUserRole } from '../../utils/unifiedRoleManager';

const AIDashboardPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();
  
  const { user, isAuthenticated } = useAppSelector(state => state.auth);
  const userRole = getUserRole(user);

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
    }
  }, [isAuthenticated, navigate]);

  // Mock AI usage data - replace with real API calls
  const [aiStats, setAiStats] = useState({
    totalQueries: 156,
    templatesGenerated: 23,
    analysisReports: 12,
    recommendationsReceived: 45,
    timesSaved: 24 // hours
  });

  const [recentActivity, setRecentActivity] = useState([
    {
      id: 1,
      type: 'analysis',
      title: 'Business Plan Analysis',
      description: 'AI analyzed your e-commerce business plan',
      timestamp: '2024-01-20T14:30:00Z',
      status: 'completed',
      icon: BarChart3,
      color: 'bg-blue-500'
    },
    {
      id: 2,
      type: 'template',
      title: 'Marketing Template Generated',
      description: 'Created social media marketing template',
      timestamp: '2024-01-20T12:15:00Z',
      status: 'completed',
      icon: FileText,
      color: 'bg-green-500'
    },
    {
      id: 3,
      type: 'recommendation',
      title: 'Growth Recommendations',
      description: 'AI suggested 5 growth strategies for your startup',
      timestamp: '2024-01-20T10:45:00Z',
      status: 'completed',
      icon: TrendingUp,
      color: 'bg-purple-500'
    }
  ]);

  const [aiFeatures, setAiFeatures] = useState([
    {
      id: 1,
      name: 'AI Chat Assistant',
      description: 'Get instant answers and guidance for your business questions',
      icon: MessageSquare,
      path: '/chat',
      color: 'bg-blue-600',
      usage: 89,
      isPopular: true
    },
    {
      id: 2,
      name: 'Business Analysis',
      description: 'AI-powered analysis of your business plans and strategies',
      icon: BarChart3,
      path: '/dashboard/ai/analysis',
      color: 'bg-green-600',
      usage: 67,
      isPopular: false
    },
    {
      id: 3,
      name: 'Template Generator',
      description: 'Generate customized business templates and documents',
      icon: FileText,
      path: '/dashboard/ai/templates',
      color: 'bg-purple-600',
      usage: 45,
      isPopular: true
    },
    {
      id: 4,
      name: 'Smart Recommendations',
      description: 'Personalized recommendations for business growth',
      icon: Lightbulb,
      path: '/dashboard/ai/recommendations',
      color: 'bg-yellow-600',
      usage: 78,
      isPopular: false
    }
  ]);

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  // Don't render if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 flex items-center justify-center">
        <div className="text-center text-white">
          <AlertCircle size={48} className="mx-auto mb-4 text-red-400" />
          <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
          <p className="text-gray-300">Please log in to access AI features.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <h1 className="text-3xl font-bold text-white flex items-center">
              <Bot size={32} className={`mr-3 text-purple-400 ${isRTL ? "ml-3 mr-0" : ""}`} />
              AI Assistant Dashboard
            </h1>
            <p className="text-gray-300 mt-1">
              Harness the power of AI to accelerate your business growth
            </p>
          </div>
          <div className={`flex space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <Link
              to="/chat"
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-white flex items-center"
            >
              <MessageSquare size={18} className={`mr-2 ${isRTL ? "ml-2 mr-0" : ""}`} />
              Start Chat
            </Link>
          </div>
        </div>

        {/* AI Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Queries</p>
                <p className="text-2xl font-bold text-white">{aiStats.totalQueries}</p>
              </div>
              <MessageSquare className="text-blue-400" size={24} />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Templates</p>
                <p className="text-2xl font-bold text-white">{aiStats.templatesGenerated}</p>
              </div>
              <FileText className="text-green-400" size={24} />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Analysis</p>
                <p className="text-2xl font-bold text-white">{aiStats.analysisReports}</p>
              </div>
              <BarChart3 className="text-purple-400" size={24} />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Recommendations</p>
                <p className="text-2xl font-bold text-white">{aiStats.recommendationsReceived}</p>
              </div>
              <Lightbulb className="text-yellow-400" size={24} />
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Time Saved</p>
                <p className="text-2xl font-bold text-white">{aiStats.timesSaved}h</p>
              </div>
              <Clock className="text-orange-400" size={24} />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* AI Features */}
          <div className="lg:col-span-2">
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
              <Sparkles size={20} className={`mr-2 text-purple-400 ${isRTL ? "ml-2 mr-0" : ""}`} />
              AI Features
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {aiFeatures.map(feature => (
                <Link
                  key={feature.id}
                  to={feature.path}
                  className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/15 transition-colors group"
                >
                  <div className={`flex items-center justify-between mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className={`w-12 h-12 rounded-lg ${feature.color} flex items-center justify-center`}>
                      <feature.icon size={24} className="text-white" />
                    </div>
                    <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                      {feature.isPopular && (
                        <span className="text-xs bg-yellow-600/20 text-yellow-400 px-2 py-1 rounded">
                          Popular
                        </span>
                      )}
                      <ArrowRight size={16} className="text-gray-400 group-hover:text-white transition-colors" />
                    </div>
                  </div>
                  
                  <h3 className="text-lg font-semibold text-white mb-2">{feature.name}</h3>
                  <p className="text-gray-400 text-sm mb-3">{feature.description}</p>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-400">Usage: {feature.usage}%</span>
                    <div className="w-16 bg-gray-700 rounded-full h-1">
                      <div 
                        className="bg-purple-500 h-1 rounded-full"
                        style={{ width: `${feature.usage}%` }}
                      ></div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>

          {/* Recent Activity */}
          <div className="lg:col-span-1">
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
              <Clock size={20} className={`mr-2 text-blue-400 ${isRTL ? "ml-2 mr-0" : ""}`} />
              Recent Activity
            </h2>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
              <div className="divide-y divide-white/10">
                {recentActivity.map(activity => (
                  <div key={activity.id} className="p-4 hover:bg-white/5 transition-colors">
                    <div className={`flex items-start ${isRTL ? "flex-row-reverse" : ""}`}>
                      <div className={`w-10 h-10 rounded-lg ${activity.color} flex items-center justify-center mr-3 ${isRTL ? "ml-3 mr-0" : ""}`}>
                        <activity.icon size={20} className="text-white" />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-white font-medium text-sm mb-1">
                          {activity.title}
                        </h4>
                        <p className="text-gray-400 text-xs mb-2">
                          {activity.description}
                        </p>
                        <div className={`flex justify-between items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                          <span className="text-xs bg-green-600/20 text-green-400 px-2 py-1 rounded">
                            {activity.status}
                          </span>
                          <span className="text-xs text-gray-400">
                            {formatTimeAgo(activity.timestamp)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* AI Tips */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 mt-6 p-4">
              <h3 className="font-semibold text-white mb-3 flex items-center">
                <Brain size={16} className={`mr-2 text-purple-400 ${isRTL ? "ml-2 mr-0" : ""}`} />
                AI Tips
              </h3>
              <div className="space-y-3 text-sm">
                <div className="bg-white/5 rounded-lg p-3">
                  <p className="text-gray-300">
                    💡 Ask specific questions to get more accurate AI responses
                  </p>
                </div>
                <div className="bg-white/5 rounded-lg p-3">
                  <p className="text-gray-300">
                    🎯 Use AI templates to save time on common business documents
                  </p>
                </div>
                <div className="bg-white/5 rounded-lg p-3">
                  <p className="text-gray-300">
                    📊 Regular AI analysis helps identify growth opportunities
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIDashboardPage;
