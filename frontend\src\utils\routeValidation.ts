import { RouteConfig } from '../routes/routeConfig';
import { allRoutes } from '../routes/consolidatedRoutes';
import { User } from '../services/api';
import { canAccessRoute } from './unifiedRoleManager';

/**
 * Route validation utilities to prevent unnecessary redirects
 * and provide better route matching logic
 */

/**
 * Get all route definitions from consolidated routes
 */
export function getAllRoutes(): RouteConfig[] {
  return allRoutes;
}

/**
 * Check if a given path exists in any of the route definitions
 * This helps prevent SmartFallback from redirecting valid routes
 */
export function isValidRoute(path: string): boolean {
  const allRoutes = getAllRoutes();
  
  // Normalize the path (remove trailing slashes, handle query params)
  const normalizedPath = normalizePath(path);
  
  // Check for exact matches first
  const exactMatch = allRoutes.some(route => {
    const routePath = normalizePath(route.path);
    return routePath === normalizedPath;
  });
  
  if (exactMatch) {
    return true;
  }
  
  // Check for dynamic routes (routes with parameters)
  const dynamicMatch = allRoutes.some(route => {
    const routePath = normalizePath(route.path);
    
    // Handle React Router dynamic segments (:id, :slug, etc.)
    if (routePath.includes(':')) {
      const routePattern = routePath.replace(/:[^/]+/g, '[^/]+');
      const regex = new RegExp(`^${routePattern}$`);
      return regex.test(normalizedPath);
    }
    
    // Handle wildcard routes
    if (routePath.includes('*')) {
      const routePattern = routePath.replace(/\*/g, '.*');
      const regex = new RegExp(`^${routePattern}`);
      return regex.test(normalizedPath);
    }
    
    return false;
  });
  
  return dynamicMatch;
}

/**
 * Check if a user can access a specific route
 * Combines route existence check with permission validation
 */
export function canUserAccessRoute(path: string, user: User | null): {
  exists: boolean;
  canAccess: boolean;
  reason?: string;
} {
  const allRoutes = getAllRoutes();
  const normalizedPath = normalizePath(path);
  
  // Find the matching route
  const matchingRoute = allRoutes.find(route => {
    const routePath = normalizePath(route.path);
    
    // Exact match
    if (routePath === normalizedPath) {
      return true;
    }
    
    // Dynamic route match
    if (routePath.includes(':')) {
      const routePattern = routePath.replace(/:[^/]+/g, '[^/]+');
      const regex = new RegExp(`^${routePattern}$`);
      return regex.test(normalizedPath);
    }
    
    return false;
  });
  
  if (!matchingRoute) {
    return {
      exists: false,
      canAccess: false,
      reason: 'Route does not exist'
    };
  }
  
  // Check if route requires authentication
  if (matchingRoute.requireAuth && !user) {
    return {
      exists: true,
      canAccess: false,
      reason: 'Authentication required'
    };
  }
  
  // Check role-based access using unified role manager
  const hasAccess = canAccessRoute(
    user,
    matchingRoute.roles,
    matchingRoute.permissions,
    matchingRoute.requireAuth !== false
  );

  if (!hasAccess) {
    return {
      exists: true,
      canAccess: false,
      reason: 'Insufficient permissions'
    };
  }
  
  return {
    exists: true,
    canAccess: true
  };
}

/**
 * Get the appropriate redirect path for a user based on their authentication status and roles
 */
export function getAppropriateRedirectPath(user: User | null, currentPath: string): string | null {
  // Don't redirect if user is already on a valid path they can access
  const accessCheck = canUserAccessRoute(currentPath, user);
  
  if (accessCheck.exists && accessCheck.canAccess) {
    return null; // No redirect needed
  }
  
  // If not authenticated, redirect to login (unless already there)
  if (!user) {
    if (currentPath === '/login' || currentPath === '/register') {
      return null;
    }
    return '/login';
  }
  
  // If authenticated but can't access current route, redirect to appropriate dashboard
  if (user.is_superuser || (user.profile?.active_roles?.some(role => role.name === 'super_admin'))) {
    return '/super_admin';
  } else if (user.is_admin) {
    return '/admin';
  } else {
    return '/dashboard';
  }
}

/**
 * Check if a path should trigger a redirect
 * This is used by SmartFallback to determine when to redirect
 */
export function shouldRedirect(path: string, user: User | null): {
  shouldRedirect: boolean;
  redirectTo?: string;
  reason?: string;
} {
  const accessCheck = canUserAccessRoute(path, user);
  
  // If route doesn't exist, redirect to appropriate dashboard
  if (!accessCheck.exists) {
    const redirectTo = getAppropriateRedirectPath(user, path);
    return {
      shouldRedirect: true,
      redirectTo: redirectTo || '/',
      reason: 'Route not found'
    };
  }
  
  // If route exists but user can't access it
  if (!accessCheck.canAccess) {
    const redirectTo = getAppropriateRedirectPath(user, path);
    return {
      shouldRedirect: true,
      redirectTo: redirectTo || '/',
      reason: accessCheck.reason
    };
  }
  
  // No redirect needed
  return {
    shouldRedirect: false
  };
}

/**
 * Normalize a path for comparison
 * Removes trailing slashes, query parameters, and fragments
 */
function normalizePath(path: string): string {
  // Remove query parameters and fragments
  const cleanPath = path.split('?')[0].split('#')[0];
  
  // Remove trailing slash (except for root)
  if (cleanPath.length > 1 && cleanPath.endsWith('/')) {
    return cleanPath.slice(0, -1);
  }
  
  return cleanPath;
}

/**
 * Development helper to debug route matching
 */
export function debugRouteMatching(path: string, user: User | null): void {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }
  
  console.group(`🔍 Route Debug: ${path}`);
  console.log('User:', user?.username || 'Not authenticated');
  console.log('User roles:', user?.profile?.active_roles?.map(r => r.name) || []);
  console.log('Is admin:', user?.is_admin);
  console.log('Is superuser:', user?.is_superuser);
  
  const accessCheck = canUserAccessRoute(path, user);
  console.log('Route exists:', accessCheck.exists);
  console.log('Can access:', accessCheck.canAccess);
  if (accessCheck.reason) {
    console.log('Reason:', accessCheck.reason);
  }
  
  const redirectCheck = shouldRedirect(path, user);
  console.log('Should redirect:', redirectCheck.shouldRedirect);
  if (redirectCheck.redirectTo) {
    console.log('Redirect to:', redirectCheck.redirectTo);
  }
  if (redirectCheck.reason) {
    console.log('Redirect reason:', redirectCheck.reason);
  }
  
  console.groupEnd();
}
