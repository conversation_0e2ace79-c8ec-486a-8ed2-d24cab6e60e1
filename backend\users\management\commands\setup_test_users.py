from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.db import transaction
from users.models import UserProfile, UserRole, UserRoleAssignment
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Setup comprehensive test users for role-based navigation testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Delete existing test users before creating new ones'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force creation without confirmation'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔧 Setting up comprehensive test users'))
        self.stdout.write(self.style.SUCCESS('=' * 60))
        
        if options['reset']:
            self._reset_test_users()
        
        if not options['force']:
            confirm = input('Create/update test users? (y/N): ').lower().strip()
            if confirm != 'y':
                self.stdout.write(self.style.WARNING('Operation cancelled'))
                return
        
        self._setup_roles()
        self._create_test_users()
        self._verify_users()
        
        self.stdout.write(self.style.SUCCESS('\n🎉 Test user setup completed!'))

    def _reset_test_users(self):
        """Delete existing test users"""
        self.stdout.write(self.style.WARNING('🗑️  Resetting test users...'))
        
        test_usernames = [
            'testuser', 'testmentor', 'testinvestor', 
            'testmoderator', 'testadmin', 'testsuperadmin'
        ]
        
        deleted_count = 0
        for username in test_usernames:
            try:
                user = User.objects.get(username=username)
                user.delete()
                deleted_count += 1
                self.stdout.write(f'   ✅ Deleted {username}')
            except User.DoesNotExist:
                pass
        
        self.stdout.write(f'   🗑️  Deleted {deleted_count} test users')

    def _setup_roles(self):
        """Ensure all required roles exist"""
        self.stdout.write(self.style.SUCCESS('\n🎭 Setting up user roles...'))
        
        roles_data = [
            {'name': 'user', 'display_name': 'User', 'permission_level': 'read'},
            {'name': 'mentor', 'display_name': 'Mentor', 'permission_level': 'write'},
            {'name': 'investor', 'display_name': 'Investor', 'permission_level': 'write'},
            {'name': 'moderator', 'display_name': 'Moderator', 'permission_level': 'moderate'},
            {'name': 'admin', 'display_name': 'Administrator', 'permission_level': 'admin'},
            {'name': 'super_admin', 'display_name': 'Super Administrator', 'permission_level': 'super_admin'},
        ]
        
        for role_data in roles_data:
            role, created = UserRole.objects.get_or_create(
                name=role_data['name'],
                defaults={
                    'display_name': role_data['display_name'],
                    'permission_level': role_data['permission_level'],
                    'is_active': True,
                    'requires_approval': False
                }
            )
            
            if created:
                self.stdout.write(f'   ✅ Created role: {role.display_name}')
            else:
                self.stdout.write(f'   ✓ Role exists: {role.display_name}')

    def _create_test_users(self):
        """Create comprehensive test users"""
        self.stdout.write(self.style.SUCCESS('\n👥 Creating test users...'))
        
        # Test users with known passwords and proper role assignments
        test_users = [
            {
                'username': 'testuser',
                'email': '<EMAIL>',
                'password': 'testpass123',
                'first_name': 'Test',
                'last_name': 'User',
                'role': 'user',
                'is_staff': False,
                'is_superuser': False,
                'description': 'Regular user for testing business features'
            },
            {
                'username': 'testmentor',
                'email': '<EMAIL>',
                'password': 'testpass123',
                'first_name': 'Test',
                'last_name': 'Mentor',
                'role': 'mentor',
                'is_staff': False,
                'is_superuser': False,
                'description': 'Mentor user for testing mentorship features'
            },
            {
                'username': 'testinvestor',
                'email': '<EMAIL>',
                'password': 'testpass123',
                'first_name': 'Test',
                'last_name': 'Investor',
                'role': 'investor',
                'is_staff': False,
                'is_superuser': False,
                'description': 'Investor user for testing investment features'
            },
            {
                'username': 'testmoderator',
                'email': '<EMAIL>',
                'password': 'testpass123',
                'first_name': 'Test',
                'last_name': 'Moderator',
                'role': 'moderator',
                'is_staff': False,
                'is_superuser': False,
                'description': 'Moderator user for testing moderation features'
            },
            {
                'username': 'testadmin',
                'email': '<EMAIL>',
                'password': 'testpass123',
                'first_name': 'Test',
                'last_name': 'Admin',
                'role': 'admin',
                'is_staff': True,
                'is_superuser': False,
                'description': 'Admin user for testing administrative features'
            },
            {
                'username': 'testsuperadmin',
                'email': '<EMAIL>',
                'password': 'testpass123',
                'first_name': 'Test',
                'last_name': 'SuperAdmin',
                'role': 'super_admin',
                'is_staff': True,
                'is_superuser': True,
                'description': 'Super admin user for testing all features'
            }
        ]
        
        for user_data in test_users:
            self._create_single_user(user_data)

    def _create_single_user(self, user_data):
        """Create or update a single test user"""
        username = user_data['username']
        
        try:
            with transaction.atomic():
                # Create or update Django user
                user, created = User.objects.get_or_create(
                    username=username,
                    defaults={
                        'email': user_data['email'],
                        'first_name': user_data['first_name'],
                        'last_name': user_data['last_name'],
                        'is_staff': user_data['is_staff'],
                        'is_superuser': user_data['is_superuser'],
                        'is_active': True
                    }
                )
                
                # Set password (always update)
                user.set_password(user_data['password'])
                
                # Update fields if user already existed
                if not created:
                    user.email = user_data['email']
                    user.first_name = user_data['first_name']
                    user.last_name = user_data['last_name']
                    user.is_staff = user_data['is_staff']
                    user.is_superuser = user_data['is_superuser']
                    user.is_active = True
                
                user.save()
                
                # Create or update user profile
                profile, profile_created = UserProfile.objects.get_or_create(
                    user=user,
                    defaults={
                        'is_active': True,
                        'language': 'en'
                    }
                )
                
                # Set up role assignment
                role = UserRole.objects.get(name=user_data['role'])
                profile.primary_role = role
                profile.save()
                
                # Create role assignment
                assignment, assignment_created = UserRoleAssignment.objects.get_or_create(
                    user_profile=profile,
                    role=role,
                    defaults={
                        'is_active': True,
                        'is_approved': True,
                        'notes': f'Test user for {user_data["role"]} role'
                    }
                )
                
                if not assignment.is_active or not assignment.is_approved:
                    assignment.is_active = True
                    assignment.is_approved = True
                    assignment.save()
                
                status = 'Created' if created else 'Updated'
                self.stdout.write(
                    f'   ✅ {status} {username} ({user_data["role"]}) - {user_data["description"]}'
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'   ❌ Error with {username}: {e}')
            )

    def _verify_users(self):
        """Verify all test users are properly set up"""
        self.stdout.write(self.style.SUCCESS('\n🔍 Verifying test users...'))
        
        from users.serializers import UserSerializer
        
        test_usernames = [
            'testuser', 'testmentor', 'testinvestor',
            'testmoderator', 'testadmin', 'testsuperadmin'
        ]
        
        for username in test_usernames:
            try:
                user = User.objects.get(username=username)
                serializer = UserSerializer()
                determined_role = serializer.get_user_role(user)
                
                self.stdout.write(f'   👤 {username}:')
                self.stdout.write(f'      Django: is_staff={user.is_staff}, is_superuser={user.is_superuser}')
                self.stdout.write(f'      Role: {determined_role}')
                self.stdout.write(f'      Password: testpass123')
                
            except User.DoesNotExist:
                self.stdout.write(self.style.ERROR(f'   ❌ User {username} not found'))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'   ❌ Error verifying {username}: {e}'))
