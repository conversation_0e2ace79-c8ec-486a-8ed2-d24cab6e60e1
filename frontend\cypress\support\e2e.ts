// ***********************************************************
// This example support/e2e.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using CommonJS syntax:
require('./commands')

// Import additional plugins (commented out for initial setup)
// require('cypress-axe')
// require('cypress-real-events')
// require('@cypress/code-coverage/support')

// Global configuration
Cypress.on('uncaught:exception', (err, runnable) => {
  // Returning false here prevents <PERSON><PERSON> from failing the test
  // on uncaught exceptions. We can customize this based on error types.
  
  // Don't fail on ResizeObserver errors (common in React apps)
  if (err.message.includes('ResizeObserver loop limit exceeded')) {
    return false
  }
  
  // Don't fail on network errors during development
  if (err.message.includes('Network Error') || err.message.includes('fetch')) {
    return false
  }
  
  // Log the error for debugging
  console.error('Uncaught exception:', err)
  
  // Return true to fail the test, false to continue
  return true
})

// Global before hook
beforeEach(() => {
  // Clear local storage and session storage
  cy.clearLocalStorage()
  cy.clearCookies()

  // Set up viewport
  cy.viewport(1280, 720)

  // Inject axe for accessibility testing (commented out for initial setup)
  // cy.injectAxe()

  // Set up API interceptors for common endpoints
  cy.intercept('GET', '**/api/auth/user/', { fixture: 'user.json' }).as('getUser')
  cy.intercept('POST', '**/api/auth/login/', { fixture: 'login-response.json' }).as('login')
  cy.intercept('POST', '**/api/auth/logout/', { statusCode: 200 }).as('logout')
})

// Global after hook
afterEach(() => {
  // Take screenshot on failure
  cy.screenshot({ capture: 'viewport', onlyOnFailure: true })
  
  // Clean up any test data
  cy.task('cleanDatabase', null, { failOnStatusCode: false })
})

// Custom assertions
declare global {
  namespace Cypress {
    interface Chainable {
      /**
       * Custom command to select DOM element by data-testid attribute.
       * @example cy.getByTestId('submit-button')
       */
      getByTestId(testId: string): Chainable<JQuery<HTMLElement>>
      
      /**
       * Custom command to login with different user roles
       * @example cy.loginAs('admin')
       */
      loginAs(role: 'admin' | 'superadmin' | 'user' | 'mentor'): Chainable<void>
      
      /**
       * Custom command to wait for page to be fully loaded
       * @example cy.waitForPageLoad()
       */
      waitForPageLoad(): Chainable<void>
      
      /**
       * Custom command to check accessibility
       * @example cy.checkA11y()
       */
      checkA11y(): Chainable<void>
      
      /**
       * Custom command to seed test data
       * @example cy.seedTestData('business-plans')
       */
      seedTestData(dataType: string): Chainable<void>
      
      /**
       * Custom command to clean test data
       * @example cy.cleanTestData()
       */
      cleanTestData(): Chainable<void>
    }
  }
}

// Add custom matchers
chai.use((chai, utils) => {
  chai.Assertion.addMethod('toBeVisible', function () {
    const obj = this._obj
    this.assert(
      obj.is(':visible'),
      'expected #{this} to be visible',
      'expected #{this} not to be visible'
    )
  })
})

// Performance monitoring
let performanceMarks: { [key: string]: number } = {}

Cypress.Commands.add('startPerformanceTimer', (name: string) => {
  performanceMarks[name] = Date.now()
})

Cypress.Commands.add('endPerformanceTimer', (name: string, maxTime: number = 5000) => {
  const startTime = performanceMarks[name]
  if (startTime) {
    const duration = Date.now() - startTime
    expect(duration).to.be.lessThan(maxTime)
    cy.log(`Performance: ${name} took ${duration}ms`)
  }
})

// Error handling for API calls
Cypress.on('fail', (err, runnable) => {
  // Log additional context on failure
  cy.log('Test failed:', err.message)
  cy.log('Current URL:', window.location.href)
  
  // Take a screenshot with timestamp
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
  cy.screenshot(`failure-${timestamp}`)
  
  throw err
})
