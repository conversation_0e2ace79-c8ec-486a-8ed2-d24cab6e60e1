import React from 'react';
import { useLanguage } from '../../hooks/useLanguage';
import { RTLTextProps } from '../../types/rtl';
/**
 * RTL-aware text component that automatically aligns text based on language direction
 *
 * This component handles text alignment based on the current language direction.
 *
 * @example
 * ```tsx
 * <RTLText align="start">This text will be aligned to the start (right in RTL, left in LTR)</RTLText>
 * ```
 */
const RTLText: React.FC<RTLTextProps & any> = ({
  children,
  align = 'start',
  className = '',
  as: Component = 'div',
  reverseInRTL = false,
  ...htmlProps
}) => {
  const { isRTL } = useLanguage();

  // Helper function for text alignment
  const getTextAlignClass = (alignment: 'start' | 'end' | 'center') => {
    switch (alignment) {
      case 'start':
        return isRTL ? 'text-right' : 'text-left';
      case 'end':
        return isRTL ? 'text-left' : 'text-right';
      case 'center':
        return 'text-center';
      default:
        return 'text-left';
    }
  };

  const alignClass = align === 'center' ? 'text-center' : getTextAlignClass(align);
  const rtlClass = reverseInRTL && isRTL ? 'rtl-reverse' : '';

  return (
    <Component className={`${alignClass} ${rtlClass} ${className}`} {...htmlProps}>
      {children}
    </Component>
  );
};

export default RTLText;
