/**
 * RTL (Right-to-Left) Utilities
 * 
 * This module provides utilities for handling RTL languages and layouts.
 */

// Type definitions
export type SizeValue = string | number;
export type Direction = 'ltr' | 'rtl';

// RTL language codes
const RTL_LANGUAGES = ['ar', 'he', 'fa', 'ur', 'ku', 'dv'];

/**
 * Check if a language code represents an RTL language
 * @param languageCode - The language code to check (e.g., 'ar', 'en')
 * @returns true if the language is RTL, false otherwise
 */
export const isRTLLanguage = (languageCode: string): boolean => {
  if (!languageCode) return false;
  
  // Extract the base language code (e.g., 'ar' from 'ar-SA')
  const baseLanguage = languageCode.split('-')[0].toLowerCase();
  return RTL_LANGUAGES.includes(baseLanguage);
};

/**
 * Get the text direction for a given language
 * @param languageCode - The language code
 * @returns 'rtl' for RTL languages, 'ltr' for LTR languages
 */
export const getDirectionFromLanguage = (languageCode: string): Direction => {
  return isRTLLanguage(languageCode) ? 'rtl' : 'ltr';
};

/**
 * Get the opposite direction
 * @param direction - Current direction
 * @returns Opposite direction
 */
export const getOppositeDirection = (direction: Direction): Direction => {
  return direction === 'rtl' ? 'ltr' : 'rtl';
};

/**
 * Apply RTL-aware CSS properties
 * @param isRTL - Whether the current layout is RTL
 * @param ltrValue - Value for LTR layout
 * @param rtlValue - Value for RTL layout
 * @returns The appropriate value based on direction
 */
export const rtlValue = <T>(isRTL: boolean, ltrValue: T, rtlValue: T): T => {
  return isRTL ? rtlValue : ltrValue;
};

/**
 * Get RTL-aware margin/padding values
 * @param isRTL - Whether the current layout is RTL
 * @param start - Start value (left in LTR, right in RTL)
 * @param end - End value (right in LTR, left in RTL)
 * @returns Object with left and right values
 */
export const rtlSpacing = (
  isRTL: boolean,
  start?: SizeValue,
  end?: SizeValue
) => ({
  left: isRTL ? end : start,
  right: isRTL ? start : end,
});

/**
 * Get RTL-aware transform values for animations
 * @param isRTL - Whether the current layout is RTL
 * @param value - Transform value (will be negated for RTL)
 * @returns Transform value adjusted for direction
 */
export const rtlTransform = (isRTL: boolean, value: string): string => {
  if (!isRTL) return value;
  
  // Handle translateX values
  if (value.includes('translateX')) {
    return value.replace(/translateX\(([^)]+)\)/, (match, translateValue) => {
      // If the value contains a number, negate it
      if (translateValue.match(/-?\d+/)) {
        const negated = translateValue.startsWith('-') 
          ? translateValue.substring(1) 
          : `-${translateValue}`;
        return `translateX(${negated})`;
      }
      return match;
    });
  }
  
  return value;
};

/**
 * Get RTL-aware flex direction
 * @param isRTL - Whether the current layout is RTL
 * @param direction - Base flex direction
 * @returns Adjusted flex direction for RTL
 */
export const rtlFlexDirection = (
  isRTL: boolean,
  direction: 'row' | 'row-reverse' | 'column' | 'column-reverse'
): string => {
  if (!isRTL) return direction;
  
  switch (direction) {
    case 'row':
      return 'row-reverse';
    case 'row-reverse':
      return 'row';
    default:
      return direction;
  }
};

/**
 * Get RTL-aware text alignment
 * @param isRTL - Whether the current layout is RTL
 * @param alignment - Base text alignment
 * @returns Adjusted text alignment for RTL
 */
export const rtlTextAlign = (
  isRTL: boolean,
  alignment: 'left' | 'right' | 'center' | 'justify'
): string => {
  if (!isRTL || alignment === 'center' || alignment === 'justify') {
    return alignment;
  }
  
  return alignment === 'left' ? 'right' : 'left';
};

/**
 * Get RTL-aware border radius values
 * @param isRTL - Whether the current layout is RTL
 * @param topLeft - Top-left radius
 * @param topRight - Top-right radius
 * @param bottomRight - Bottom-right radius
 * @param bottomLeft - Bottom-left radius
 * @returns Object with adjusted border radius values
 */
export const rtlBorderRadius = (
  isRTL: boolean,
  topLeft?: SizeValue,
  topRight?: SizeValue,
  bottomRight?: SizeValue,
  bottomLeft?: SizeValue
) => {
  if (!isRTL) {
    return {
      borderTopLeftRadius: topLeft,
      borderTopRightRadius: topRight,
      borderBottomRightRadius: bottomRight,
      borderBottomLeftRadius: bottomLeft,
    };
  }
  
  return {
    borderTopLeftRadius: topRight,
    borderTopRightRadius: topLeft,
    borderBottomRightRadius: bottomLeft,
    borderBottomLeftRadius: bottomRight,
  };
};

/**
 * Utility class for RTL-aware CSS generation
 */
export class RTLUtils {
  private isRTL: boolean;
  
  constructor(isRTL: boolean) {
    this.isRTL = isRTL;
  }
  
  /**
   * Get margin values
   */
  margin(start?: SizeValue, end?: SizeValue, top?: SizeValue, bottom?: SizeValue) {
    return {
      marginLeft: this.isRTL ? end : start,
      marginRight: this.isRTL ? start : end,
      marginTop: top,
      marginBottom: bottom,
    };
  }
  
  /**
   * Get padding values
   */
  padding(start?: SizeValue, end?: SizeValue, top?: SizeValue, bottom?: SizeValue) {
    return {
      paddingLeft: this.isRTL ? end : start,
      paddingRight: this.isRTL ? start : end,
      paddingTop: top,
      paddingBottom: bottom,
    };
  }
  
  /**
   * Get position values
   */
  position(start?: SizeValue, end?: SizeValue, top?: SizeValue, bottom?: SizeValue) {
    return {
      left: this.isRTL ? end : start,
      right: this.isRTL ? start : end,
      top,
      bottom,
    };
  }
  
  /**
   * Get text alignment
   */
  textAlign(alignment: 'left' | 'right' | 'center' | 'justify') {
    return rtlTextAlign(this.isRTL, alignment);
  }
  
  /**
   * Get flex direction
   */
  flexDirection(direction: 'row' | 'row-reverse' | 'column' | 'column-reverse') {
    return rtlFlexDirection(this.isRTL, direction);
  }
}

// Export CSS utilities
export * from './cssUtils';

// Default export
export default {
  isRTLLanguage,
  getDirectionFromLanguage,
  getOppositeDirection,
  rtlValue,
  rtlSpacing,
  rtlTransform,
  rtlFlexDirection,
  rtlTextAlign,
  rtlBorderRadius,
  RTLUtils,
};
