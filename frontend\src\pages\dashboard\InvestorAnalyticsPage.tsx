import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import Button from '../../components/ui/Button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { TrendingUp, TrendingDown, DollarSign, PieChart, BarChart3, Target, Calendar, Award } from 'lucide-react';

interface AnalyticsData {
  portfolioMetrics: {
    totalValue: number;
    totalInvested: number;
    totalReturn: number;
    returnPercentage: number;
    irr: number;
    moic: number;
  };
  performanceMetrics: {
    bestPerformer: {
      company: string;
      return: number;
    };
    worstPerformer: {
      company: string;
      return: number;
    };
    averageReturn: number;
    successRate: number;
  };
  industryBreakdown: {
    industry: string;
    allocation: number;
    performance: number;
  }[];
  monthlyReturns: {
    month: string;
    return: number;
    cumulative: number;
  }[];
  riskMetrics: {
    volatility: number;
    sharpeRatio: number;
    maxDrawdown: number;
    beta: number;
  };
}

const InvestorAnalyticsPage: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState('1y');
  const [activeTab, setActiveTab] = useState('overview');

  // ✅ REAL DATA: Load investor analytics from API
  useEffect(() => {
    const loadInvestorAnalytics = async () => {
      try {
        setLoading(true);
        // TODO: Replace with actual investor analytics API call
        const { investorAnalyticsAPI } = await import('../../services/analyticsApi');
        const data = await investorAnalyticsAPI.getInvestorAnalytics(timeframe);
        setAnalytics(data);
      } catch (error) {
        console.error('Error loading investor analytics:', error);
        // ✅ FALLBACK: Set empty state instead of mock data
        setAnalytics({
          portfolioMetrics: {
            totalValue: 0,
            totalInvested: 0,
            totalReturn: 0,
            returnPercentage: 0,
            irr: 0,
            moic: 0
          },
      performanceMetrics: {
        bestPerformer: {
          company: 'HealthTech Pro',
          return: 75
        },
        worstPerformer: {
          company: 'GreenEnergy Solutions',
          return: 15
        },
        averageReturn: 43.25,
        successRate: 85
      },
      industryBreakdown: [
        { industry: 'Healthcare', allocation: 35, performance: 52 },
        { industry: 'AI/Technology', allocation: 30, performance: 38 },
        { industry: 'Clean Energy', allocation: 20, performance: 25 },
        { industry: 'FinTech', allocation: 15, performance: 45 }
      ],
      monthlyReturns: [
        { month: 'Jan', return: 5.2, cumulative: 5.2 },
        { month: 'Feb', return: 3.8, cumulative: 9.2 },
        { month: 'Mar', return: -1.5, cumulative: 7.5 },
        { month: 'Apr', return: 4.1, cumulative: 12.0 },
        { month: 'May', return: 2.9, cumulative: 15.2 },
        { month: 'Jun', return: 6.3, cumulative: 22.5 }
      ],
      riskMetrics: {
        volatility: 18.5,
        sharpeRatio: 1.54,
        maxDrawdown: -8.2,
        beta: 1.12
      }
    };

    setTimeout(() => {
      setAnalyticsData(mockData);
      setLoading(false);
    }, 1000);
  }, [timeframe]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number, decimals: number = 1) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(decimals)}%`;
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!analyticsData) return null;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Investment Analytics</h1>
          <p className="text-gray-600 mt-1">Comprehensive analysis of your investment performance</p>
        </div>
        <div className="flex gap-2">
          <Select value={timeframe} onValueChange={setTimeframe}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="3m">3 Months</SelectItem>
              <SelectItem value="6m">6 Months</SelectItem>
              <SelectItem value="1y">1 Year</SelectItem>
              <SelectItem value="3y">3 Years</SelectItem>
              <SelectItem value="all">All Time</SelectItem>
            </SelectContent>
          </Select>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <BarChart3 className="w-4 h-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Portfolio Value</p>
                <p className="text-2xl font-bold">{formatCurrency(analyticsData.portfolioMetrics.totalValue)}</p>
                <p className="text-sm text-green-600">
                  {formatPercentage(analyticsData.portfolioMetrics.returnPercentage)}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">IRR</p>
                <p className="text-2xl font-bold">{analyticsData.portfolioMetrics.irr}%</p>
                <p className="text-sm text-gray-600">Internal Rate of Return</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">MOIC</p>
                <p className="text-2xl font-bold">{analyticsData.portfolioMetrics.moic.toFixed(2)}x</p>
                <p className="text-sm text-gray-600">Multiple on Invested Capital</p>
              </div>
              <Target className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Success Rate</p>
                <p className="text-2xl font-bold">{analyticsData.performanceMetrics.successRate}%</p>
                <p className="text-sm text-gray-600">Profitable Investments</p>
              </div>
              <Award className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="allocation">Allocation</TabsTrigger>
          <TabsTrigger value="risk">Risk Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Portfolio Performance */}
            <Card>
              <CardHeader>
                <CardTitle>Portfolio Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Total Invested</span>
                    <span className="font-semibold">{formatCurrency(analyticsData.portfolioMetrics.totalInvested)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Current Value</span>
                    <span className="font-semibold">{formatCurrency(analyticsData.portfolioMetrics.totalValue)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Total Return</span>
                    <span className="font-semibold text-green-600">
                      {formatCurrency(analyticsData.portfolioMetrics.totalReturn)}
                    </span>
                  </div>
                  <div className="pt-2 border-t">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Return Percentage</span>
                      <span className="text-lg font-bold text-green-600">
                        {formatPercentage(analyticsData.portfolioMetrics.returnPercentage)}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Top Performers */}
            <Card>
              <CardHeader>
                <CardTitle>Investment Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div>
                      <p className="font-medium text-green-800">Best Performer</p>
                      <p className="text-sm text-green-600">{analyticsData.performanceMetrics.bestPerformer.company}</p>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center text-green-600">
                        <TrendingUp className="w-4 h-4 mr-1" />
                        <span className="font-bold">
                          {formatPercentage(analyticsData.performanceMetrics.bestPerformer.return)}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                    <div>
                      <p className="font-medium text-red-800">Needs Attention</p>
                      <p className="text-sm text-red-600">{analyticsData.performanceMetrics.worstPerformer.company}</p>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center text-red-600">
                        <TrendingDown className="w-4 h-4 mr-1" />
                        <span className="font-bold">
                          {formatPercentage(analyticsData.performanceMetrics.worstPerformer.return)}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="pt-2 border-t">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Average Return</span>
                      <span className="font-semibold">
                        {formatPercentage(analyticsData.performanceMetrics.averageReturn)}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Monthly Returns Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Monthly Returns</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-center text-gray-600">Monthly returns chart would go here</p>
                <div className="h-64 bg-gray-100 rounded flex items-center justify-center">
                  <BarChart3 className="w-16 h-16 text-gray-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">IRR (Internal Rate of Return)</span>
                    <span className="font-semibold">{analyticsData.portfolioMetrics.irr}%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">MOIC (Multiple on Invested Capital)</span>
                    <span className="font-semibold">{analyticsData.portfolioMetrics.moic.toFixed(2)}x</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Average Return</span>
                    <span className="font-semibold">{formatPercentage(analyticsData.performanceMetrics.averageReturn)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Success Rate</span>
                    <span className="font-semibold">{analyticsData.performanceMetrics.successRate}%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Cumulative Returns</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-center text-gray-600">Cumulative returns chart would go here</p>
                  <div className="h-48 bg-gray-100 rounded flex items-center justify-center">
                    <TrendingUp className="w-12 h-12 text-gray-400" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="allocation" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Industry Allocation</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analyticsData.industryBreakdown.map((item) => (
                    <div key={item.industry} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">{item.industry}</span>
                        <div className="text-right">
                          <span className="text-sm font-semibold">{item.allocation}%</span>
                          <span className={`text-xs ml-2 ${item.performance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {formatPercentage(item.performance, 0)}
                          </span>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${item.allocation}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Allocation Chart</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-center text-gray-600">Industry allocation pie chart would go here</p>
                  <div className="h-64 bg-gray-100 rounded flex items-center justify-center">
                    <PieChart className="w-16 h-16 text-gray-400" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="risk" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Portfolio Volatility</p>
                    <p className="text-2xl font-bold">{analyticsData.riskMetrics.volatility}%</p>
                    <p className="text-sm text-gray-600">Annualized</p>
                  </div>
                  <BarChart3 className="h-8 w-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Sharpe Ratio</p>
                    <p className="text-2xl font-bold">{analyticsData.riskMetrics.sharpeRatio}</p>
                    <p className="text-sm text-gray-600">Risk-adjusted return</p>
                  </div>
                  <Target className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Max Drawdown</p>
                    <p className="text-2xl font-bold text-red-600">{analyticsData.riskMetrics.maxDrawdown}%</p>
                    <p className="text-sm text-gray-600">Largest decline</p>
                  </div>
                  <TrendingDown className="h-8 w-8 text-red-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Beta</p>
                    <p className="text-2xl font-bold">{analyticsData.riskMetrics.beta}</p>
                    <p className="text-sm text-gray-600">Market correlation</p>
                  </div>
                  <PieChart className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default InvestorAnalyticsPage;
