describe('Accessibility and Performance Tests', () => {
  beforeEach(() => {
    cy.loginAs('user')
  })

  describe('Accessibility (A11y) Tests', () => {
    it('should pass accessibility audit on login page', () => {
      cy.visit('/login')
      cy.injectAxe()
      cy.checkA11y(null, null, (violations) => {
        if (violations.length > 0) {
          cy.log('Accessibility violations found:')
          violations.forEach((violation) => {
            cy.log(`${violation.impact}: ${violation.description}`)
          })
        }
      })
    })

    it('should pass accessibility audit on dashboard', () => {
      cy.visit('/dashboard')
      cy.injectAxe()
      cy.checkA11y()
    })

    it('should pass accessibility audit on business plans page', () => {
      cy.intercept('GET', '**/api/business-plans/', { body: { results: [], count: 0 } }).as('getBusinessPlans')
      
      cy.visit('/business-plans')
      cy.wait('@getBusinessPlans')
      cy.injectAxe()
      cy.checkA11y()
    })

    it('should have proper heading hierarchy', () => {
      cy.visit('/dashboard')
      
      // Should have one h1
      cy.get('h1').should('have.length', 1)
      
      // Check heading order
      cy.get('h1, h2, h3, h4, h5, h6').then(($headings) => {
        const headingLevels = Array.from($headings).map(h => parseInt(h.tagName.charAt(1)))
        
        // Verify no heading levels are skipped
        for (let i = 1; i < headingLevels.length; i++) {
          const current = headingLevels[i]
          const previous = headingLevels[i - 1]
          expect(current - previous).to.be.at.most(1)
        }
      })
    })

    it('should have proper ARIA landmarks', () => {
      cy.visit('/dashboard')
      
      // Check for main landmark
      cy.get('main, [role="main"]').should('exist')
      
      // Check for navigation landmark
      cy.get('nav, [role="navigation"]').should('exist')
      
      // Check for banner (header)
      cy.get('header, [role="banner"]').should('exist')
      
      // Check for complementary content (sidebar)
      cy.get('aside, [role="complementary"]').should('exist')
    })

    it('should support keyboard navigation', () => {
      cy.visit('/dashboard')
      
      // Tab through interactive elements
      cy.get('body').tab()
      cy.focused().should('be.visible')
      
      // Should be able to navigate to sidebar
      cy.focused().tab()
      cy.focused().should('be.visible')
      
      // Should be able to activate links with Enter
      cy.getByTestId('nav-business-plans').focus()
      cy.focused().type('{enter}')
      cy.url().should('include', '/business-plans')
    })

    it('should have proper form labels and descriptions', () => {
      cy.visit('/business-plans/create')
      
      // All form inputs should have labels
      cy.get('input, textarea, select').each(($input) => {
        const id = $input.attr('id')
        const ariaLabel = $input.attr('aria-label')
        const ariaLabelledby = $input.attr('aria-labelledby')
        
        if (id) {
          cy.get(`label[for="${id}"]`).should('exist')
        } else {
          expect(ariaLabel || ariaLabelledby).to.exist
        }
      })
    })

    it('should provide error messages for form validation', () => {
      cy.visit('/business-plans/create')
      
      // Submit form without required fields
      cy.getByTestId('submit-button').click()
      
      // Check that error messages are associated with inputs
      cy.get('input[aria-invalid="true"]').each(($input) => {
        const ariaDescribedby = $input.attr('aria-describedby')
        expect(ariaDescribedby).to.exist
        
        // Error message should exist and be visible
        cy.get(`#${ariaDescribedby}`).should('be.visible')
      })
    })

    it('should have sufficient color contrast', () => {
      cy.visit('/dashboard')
      cy.injectAxe()
      
      // Check specifically for color contrast violations
      cy.checkA11y(null, {
        rules: {
          'color-contrast': { enabled: true }
        }
      })
    })

    it('should support screen readers', () => {
      cy.visit('/dashboard')
      
      // Check for screen reader announcements
      cy.get('[aria-live]').should('exist')
      
      // Check for descriptive text
      cy.getByTestId('sidebar').should('have.attr', 'aria-label')
      cy.getByTestId('main-content').should('have.attr', 'aria-label')
      
      // Check for status updates
      cy.navigateViaSidebar('Business Plans')
      cy.get('[aria-live="polite"]').should('contain.text', 'Business Plans')
    })

    it('should handle focus management in modals', () => {
      cy.intercept('GET', '**/api/business-plans/', { 
        body: { results: [{ id: 1, title: 'Test Plan' }], count: 1 }
      }).as('getBusinessPlans')
      
      cy.visit('/business-plans')
      cy.wait('@getBusinessPlans')
      
      // Open delete confirmation modal
      cy.getByTestId('delete-button').click()
      
      // Focus should be trapped in modal
      cy.getByTestId('confirm-delete-modal').should('be.visible')
      cy.focused().should('be.within', '[data-testid="confirm-delete-modal"]')
      
      // Tab should cycle within modal
      cy.focused().tab()
      cy.focused().should('be.within', '[data-testid="confirm-delete-modal"]')
      
      // Escape should close modal and restore focus
      cy.realPress('Escape')
      cy.getByTestId('confirm-delete-modal').should('not.exist')
      cy.getByTestId('delete-button').should('be.focused')
    })

    it('should support high contrast mode', () => {
      // Simulate high contrast mode
      cy.visit('/dashboard')
      
      cy.get('body').invoke('addClass', 'high-contrast')
      
      // Elements should still be visible and usable
      cy.getByTestId('sidebar').should('be.visible')
      cy.getByTestId('main-content').should('be.visible')
      
      // Interactive elements should have visible focus indicators
      cy.getByTestId('nav-dashboard').focus()
      cy.focused().should('have.css', 'outline-width').and('not.eq', '0px')
    })
  })

  describe('Performance Tests', () => {
    it('should load dashboard within performance budget', () => {
      cy.startPerformanceTimer('dashboard-load')
      cy.visit('/dashboard')
      cy.waitForPageLoad()
      cy.endPerformanceTimer('dashboard-load', 3000) // 3 second budget
    })

    it('should load business plans list efficiently', () => {
      const largePlans = Array.from({ length: 100 }, (_, i) => ({
        id: i + 1,
        title: `Business Plan ${i + 1}`,
        description: `Description ${i + 1}`,
        status: 'draft'
      }))

      cy.intercept('GET', '**/api/business-plans/', {
        body: { results: largePlans, count: 100 },
        delay: 200 // Simulate network delay
      }).as('getLargePlans')

      cy.startPerformanceTimer('large-list-load')
      cy.visit('/business-plans')
      cy.wait('@getLargePlans')
      cy.getByTestId('business-plans-list').should('be.visible')
      cy.endPerformanceTimer('large-list-load', 5000) // 5 second budget for large lists
    })

    it('should handle rapid navigation efficiently', () => {
      cy.visit('/dashboard')
      
      cy.startPerformanceTimer('rapid-navigation')
      
      // Navigate quickly between pages
      cy.navigateViaSidebar('Business Plans')
      cy.navigateViaSidebar('Mentorship')
      cy.navigateViaSidebar('Funding')
      cy.navigateViaSidebar('Dashboard')
      
      cy.endPerformanceTimer('rapid-navigation', 4000)
    })

    it('should optimize image loading', () => {
      cy.visit('/dashboard')
      
      // Check for lazy loading attributes
      cy.get('img').each(($img) => {
        const loading = $img.attr('loading')
        const src = $img.attr('src')
        
        // Images below the fold should be lazy loaded
        if (!$img.is(':in-viewport')) {
          expect(loading).to.eq('lazy')
        }
        
        // Images should have proper alt text
        expect($img.attr('alt')).to.exist
      })
    })

    it('should minimize bundle size impact', () => {
      cy.visit('/dashboard')
      
      // Check for code splitting
      cy.window().then((win) => {
        const performanceEntries = win.performance.getEntriesByType('navigation')
        const navigationEntry = performanceEntries[0] as PerformanceNavigationTiming
        
        // Total transfer size should be reasonable
        const transferSize = navigationEntry.transferSize
        expect(transferSize).to.be.lessThan(2000000) // 2MB budget
      })
    })

    it('should handle memory efficiently', () => {
      cy.visit('/dashboard')
      
      // Navigate to different pages and back
      cy.navigateViaSidebar('Business Plans')
      cy.navigateViaSidebar('Mentorship')
      cy.navigateViaSidebar('Dashboard')
      
      // Check for memory leaks
      cy.window().then((win) => {
        if (win.performance.memory) {
          const memoryInfo = win.performance.memory
          const usedJSHeapSize = memoryInfo.usedJSHeapSize
          const totalJSHeapSize = memoryInfo.totalJSHeapSize
          
          // Memory usage should be reasonable
          expect(usedJSHeapSize / totalJSHeapSize).to.be.lessThan(0.8)
        }
      })
    })

    it('should optimize API request patterns', () => {
      let requestCount = 0
      
      cy.intercept('**', (req) => {
        requestCount++
      }).as('allRequests')
      
      cy.visit('/dashboard')
      cy.waitForPageLoad()
      
      // Should not make excessive API requests
      expect(requestCount).to.be.lessThan(10)
    })

    it('should cache resources effectively', () => {
      cy.visit('/dashboard')
      
      // Navigate away and back
      cy.navigateViaSidebar('Business Plans')
      cy.navigateViaSidebar('Dashboard')
      
      // Check for cached resources
      cy.window().then((win) => {
        const performanceEntries = win.performance.getEntriesByType('resource')
        const cachedResources = performanceEntries.filter(entry => 
          entry.transferSize === 0 && entry.decodedBodySize > 0
        )
        
        // Should have some cached resources
        expect(cachedResources.length).to.be.greaterThan(0)
      })
    })

    it('should handle offline scenarios gracefully', () => {
      cy.visit('/dashboard')
      
      // Simulate offline
      cy.window().then((win) => {
        Object.defineProperty(win.navigator, 'onLine', {
          writable: true,
          value: false
        })
        
        win.dispatchEvent(new Event('offline'))
      })
      
      // Should show offline indicator
      cy.getByTestId('offline-indicator').should('be.visible')
      
      // Should queue actions for when online
      cy.navigateViaSidebar('Business Plans')
      cy.getByTestId('offline-message').should('contain.text', 'You are offline')
    })
  })

  describe('Responsive Design Tests', () => {
    it('should be responsive across different viewport sizes', () => {
      const viewports = [
        { width: 320, height: 568, name: 'iPhone SE' },
        { width: 375, height: 667, name: 'iPhone 8' },
        { width: 768, height: 1024, name: 'iPad' },
        { width: 1024, height: 768, name: 'iPad Landscape' },
        { width: 1440, height: 900, name: 'Desktop' },
        { width: 1920, height: 1080, name: 'Large Desktop' }
      ]

      viewports.forEach((viewport) => {
        cy.viewport(viewport.width, viewport.height)
        cy.visit('/dashboard')
        
        // Main content should be visible
        cy.getByTestId('main-content').should('be.visible')
        
        // No horizontal scrolling
        cy.get('body').should('have.css', 'overflow-x', 'hidden')
        
        // Touch targets should be large enough on mobile
        if (viewport.width < 768) {
          cy.get('button, a, [role="button"]').each(($el) => {
            const rect = $el[0].getBoundingClientRect()
            expect(Math.min(rect.width, rect.height)).to.be.at.least(44) // 44px minimum touch target
          })
        }
      })
    })

    it('should adapt navigation for mobile', () => {
      cy.viewport(375, 667) // Mobile viewport
      cy.visit('/dashboard')
      
      // Mobile menu should be present
      cy.getByTestId('mobile-menu-button').should('be.visible')
      cy.getByTestId('sidebar').should('not.be.visible')
      
      // Open mobile menu
      cy.getByTestId('mobile-menu-button').click()
      cy.getByTestId('mobile-sidebar').should('be.visible')
      
      // Should support swipe gestures
      cy.getByTestId('mobile-sidebar')
        .trigger('touchstart', { touches: [{ clientX: 0, clientY: 100 }] })
        .trigger('touchmove', { touches: [{ clientX: -100, clientY: 100 }] })
        .trigger('touchend')
      
      cy.getByTestId('mobile-sidebar').should('not.be.visible')
    })

    it('should handle orientation changes', () => {
      cy.viewport(768, 1024) // Portrait tablet
      cy.visit('/dashboard')
      
      cy.getByTestId('sidebar').should('be.visible')
      
      // Change to landscape
      cy.viewport(1024, 768)
      
      // Layout should adapt
      cy.getByTestId('sidebar').should('be.visible')
      cy.getByTestId('main-content').should('be.visible')
    })
  })

  describe('Cross-Browser Compatibility', () => {
    it('should work in different browsers', () => {
      // This test would run in CI with different browsers
      cy.visit('/dashboard')
      
      // Check for browser-specific features
      cy.window().then((win) => {
        // Check for modern browser features
        expect(win.fetch).to.exist
        expect(win.Promise).to.exist
        expect(win.localStorage).to.exist
        expect(win.sessionStorage).to.exist
      })
      
      // Check CSS features
      cy.get('body').should('have.css', 'display', 'block')
      
      // Check JavaScript features
      cy.getByTestId('sidebar').should('be.visible')
    })

    it('should handle browser-specific quirks', () => {
      cy.visit('/dashboard')
      
      // Check for polyfills
      cy.window().then((win) => {
        // Should have polyfills for older browsers
        if (!win.IntersectionObserver) {
          expect(win.IntersectionObserver).to.exist // Should be polyfilled
        }
      })
    })
  })
})
