describe('Business Plans CRUD Operations', () => {
  beforeEach(() => {
    // Setup authentication
    cy.intercept('POST', '**/api/auth/login/', { fixture: 'login-response.json' }).as('login')
    cy.intercept('GET', '**/api/auth/user/', { fixture: 'user.json' }).as('getUser')
    
    cy.loginAs('user')
  })

  describe('Create Business Plan', () => {
    beforeEach(() => {
      cy.intercept('GET', '**/api/business-plans/', { body: [] }).as('getBusinessPlans')
      cy.visit('/business-plans')
      cy.wait('@getBusinessPlans')
    })

    it('should create a new business plan successfully', () => {
      cy.intercept('POST', '**/api/business-plans/', { 
        statusCode: 201,
        body: { id: 1, ...require('../fixtures/business-plan.json') }
      }).as('createBusinessPlan')

      cy.getByTestId('create-business-plan-button').click()
      cy.url().should('include', '/business-plans/create')

      // Fill out the form
      cy.fillForm({
        title: 'Test Business Plan',
        description: 'A comprehensive business plan for testing',
        industry: 'Technology',
        stage: 'idea',
        'funding-required': '100000',
        'team-size': '5',
        location: 'Test City'
      })

      // Fill out sections
      cy.getByTestId('executive-summary-editor').type('This is a test executive summary')
      cy.getByTestId('market-analysis-editor').type('Detailed market analysis')
      cy.getByTestId('financial-projections-editor').type('Financial projections')

      // Submit the form
      cy.getByTestId('submit-button').click()

      cy.wait('@createBusinessPlan')
      cy.checkToast('Business plan created successfully', 'success')
      cy.url().should('include', '/business-plans/1')
    })

    it('should validate required fields', () => {
      cy.getByTestId('create-business-plan-button').click()
      
      // Try to submit without filling required fields
      cy.getByTestId('submit-button').click()

      // Should show validation errors
      cy.getByTestId('title-input').should('have.attr', 'aria-invalid', 'true')
      cy.getByTestId('description-input').should('have.attr', 'aria-invalid', 'true')
      cy.getByTestId('industry-input').should('have.attr', 'aria-invalid', 'true')

      cy.get('[data-testid*="error"]').should('be.visible')
    })

    it('should handle API errors gracefully', () => {
      cy.intercept('POST', '**/api/business-plans/', { 
        statusCode: 400,
        body: { error: 'Invalid data provided' }
      }).as('createBusinessPlanError')

      cy.getByTestId('create-business-plan-button').click()
      
      cy.fillForm({
        title: 'Test Plan',
        description: 'Test description',
        industry: 'Technology'
      })

      cy.getByTestId('submit-button').click()

      cy.wait('@createBusinessPlanError')
      cy.checkToast('Invalid data provided', 'error')
    })

    it('should save draft automatically', () => {
      cy.intercept('POST', '**/api/business-plans/draft/', { 
        statusCode: 200,
        body: { id: 'draft-1', saved_at: new Date().toISOString() }
      }).as('saveDraft')

      cy.getByTestId('create-business-plan-button').click()
      
      cy.getByTestId('title-input').type('Draft Business Plan')
      
      // Wait for auto-save
      cy.wait('@saveDraft')
      cy.getByTestId('draft-saved-indicator').should('be.visible')
    })

    it('should support file uploads', () => {
      cy.getByTestId('create-business-plan-button').click()
      
      // Upload business plan template
      cy.uploadFile('[data-testid="file-upload"]', 'business-plan-template.pdf')
      
      cy.getByTestId('uploaded-file').should('contain.text', 'business-plan-template.pdf')
      cy.getByTestId('remove-file-button').should('be.visible')
    })
  })

  describe('Read Business Plans', () => {
    beforeEach(() => {
      const businessPlans = [
        { id: 1, ...require('../fixtures/business-plan.json') },
        { 
          id: 2, 
          title: 'Second Business Plan',
          description: 'Another test plan',
          status: 'published'
        }
      ]
      
      cy.intercept('GET', '**/api/business-plans/', { body: businessPlans }).as('getBusinessPlans')
    })

    it('should display list of business plans', () => {
      cy.visit('/business-plans')
      cy.wait('@getBusinessPlans')

      cy.getByTestId('business-plans-list').should('be.visible')
      cy.getByTestId('business-plan-card').should('have.length', 2)
      
      // Check first business plan
      cy.getByTestId('business-plan-card').first().within(() => {
        cy.should('contain.text', 'Test Business Plan')
        cy.should('contain.text', 'Technology')
        cy.should('contain.text', 'draft')
      })
    })

    it('should filter business plans by status', () => {
      cy.visit('/business-plans')
      cy.wait('@getBusinessPlans')

      cy.getByTestId('status-filter').select('draft')
      cy.getByTestId('business-plan-card').should('have.length', 1)
      cy.getByTestId('business-plan-card').should('contain.text', 'Test Business Plan')

      cy.getByTestId('status-filter').select('published')
      cy.getByTestId('business-plan-card').should('have.length', 1)
      cy.getByTestId('business-plan-card').should('contain.text', 'Second Business Plan')
    })

    it('should search business plans', () => {
      cy.visit('/business-plans')
      cy.wait('@getBusinessPlans')

      cy.getByTestId('search-input').type('Test')
      cy.getByTestId('business-plan-card').should('have.length', 1)
      cy.getByTestId('business-plan-card').should('contain.text', 'Test Business Plan')

      cy.getByTestId('search-input').clear().type('Second')
      cy.getByTestId('business-plan-card').should('have.length', 1)
      cy.getByTestId('business-plan-card').should('contain.text', 'Second Business Plan')
    })

    it('should view business plan details', () => {
      cy.intercept('GET', '**/api/business-plans/1/', { 
        body: require('../fixtures/business-plan.json')
      }).as('getBusinessPlan')

      cy.visit('/business-plans')
      cy.wait('@getBusinessPlans')

      cy.getByTestId('business-plan-card').first().click()
      cy.wait('@getBusinessPlan')

      cy.url().should('include', '/business-plans/1')
      cy.getByTestId('business-plan-title').should('contain.text', 'Test Business Plan')
      cy.getByTestId('business-plan-description').should('be.visible')
      cy.getByTestId('business-plan-sections').should('be.visible')
    })

    it('should handle empty state', () => {
      cy.intercept('GET', '**/api/business-plans/', { body: [] }).as('getEmptyBusinessPlans')

      cy.visit('/business-plans')
      cy.wait('@getEmptyBusinessPlans')

      cy.getByTestId('empty-state').should('be.visible')
      cy.getByTestId('empty-state').should('contain.text', 'No business plans found')
      cy.getByTestId('create-first-plan-button').should('be.visible')
    })

    it('should paginate results', () => {
      const manyPlans = Array.from({ length: 25 }, (_, i) => ({
        id: i + 1,
        title: `Business Plan ${i + 1}`,
        description: `Description ${i + 1}`,
        status: 'draft'
      }))

      cy.intercept('GET', '**/api/business-plans/?page=1', { 
        body: {
          results: manyPlans.slice(0, 10),
          count: 25,
          next: 'http://localhost:8000/api/business-plans/?page=2',
          previous: null
        }
      }).as('getPage1')

      cy.intercept('GET', '**/api/business-plans/?page=2', { 
        body: {
          results: manyPlans.slice(10, 20),
          count: 25,
          next: 'http://localhost:8000/api/business-plans/?page=3',
          previous: 'http://localhost:8000/api/business-plans/?page=1'
        }
      }).as('getPage2')

      cy.visit('/business-plans')
      cy.wait('@getPage1')

      cy.getByTestId('business-plan-card').should('have.length', 10)
      cy.getByTestId('pagination-next').click()

      cy.wait('@getPage2')
      cy.getByTestId('business-plan-card').should('have.length', 10)
      cy.getByTestId('pagination-previous').should('be.visible')
    })
  })

  describe('Update Business Plan', () => {
    beforeEach(() => {
      cy.intercept('GET', '**/api/business-plans/1/', { 
        body: require('../fixtures/business-plan.json')
      }).as('getBusinessPlan')

      cy.visit('/business-plans/1/edit')
      cy.wait('@getBusinessPlan')
    })

    it('should update business plan successfully', () => {
      cy.intercept('PUT', '**/api/business-plans/1/', { 
        statusCode: 200,
        body: { 
          ...require('../fixtures/business-plan.json'),
          title: 'Updated Business Plan'
        }
      }).as('updateBusinessPlan')

      // Update the title
      cy.getByTestId('title-input').clear().type('Updated Business Plan')
      cy.getByTestId('description-input').clear().type('Updated description')

      cy.getByTestId('save-button').click()

      cy.wait('@updateBusinessPlan')
      cy.checkToast('Business plan updated successfully', 'success')
    })

    it('should handle concurrent edits', () => {
      // Simulate another user editing the same plan
      cy.intercept('PUT', '**/api/business-plans/1/', { 
        statusCode: 409,
        body: { error: 'Plan has been modified by another user' }
      }).as('conflictError')

      cy.getByTestId('title-input').clear().type('My Updated Title')
      cy.getByTestId('save-button').click()

      cy.wait('@conflictError')
      cy.checkToast('Plan has been modified by another user', 'error')
      cy.getByTestId('resolve-conflict-modal').should('be.visible')
    })

    it('should auto-save changes', () => {
      cy.intercept('PATCH', '**/api/business-plans/1/', { 
        statusCode: 200,
        body: { saved_at: new Date().toISOString() }
      }).as('autoSave')

      cy.getByTestId('title-input').clear().type('Auto-saved Title')
      
      // Wait for auto-save
      cy.wait('@autoSave')
      cy.getByTestId('auto-save-indicator').should('contain.text', 'Saved')
    })

    it('should track version history', () => {
      cy.intercept('GET', '**/api/business-plans/1/versions/', { 
        body: [
          { id: 1, version: 1, created_at: '2024-01-01T00:00:00Z', changes: 'Initial version' },
          { id: 2, version: 2, created_at: '2024-01-02T00:00:00Z', changes: 'Updated title' }
        ]
      }).as('getVersions')

      cy.getByTestId('version-history-button').click()
      cy.wait('@getVersions')

      cy.getByTestId('version-history-modal').should('be.visible')
      cy.getByTestId('version-item').should('have.length', 2)
    })
  })

  describe('Delete Business Plan', () => {
    beforeEach(() => {
      cy.intercept('GET', '**/api/business-plans/', { 
        body: [require('../fixtures/business-plan.json')]
      }).as('getBusinessPlans')

      cy.visit('/business-plans')
      cy.wait('@getBusinessPlans')
    })

    it('should delete business plan with confirmation', () => {
      cy.intercept('DELETE', '**/api/business-plans/1/', { statusCode: 204 }).as('deleteBusinessPlan')

      cy.getByTestId('business-plan-card').within(() => {
        cy.getByTestId('delete-button').click()
      })

      // Confirm deletion
      cy.getByTestId('confirm-delete-modal').should('be.visible')
      cy.getByTestId('confirm-delete-button').click()

      cy.wait('@deleteBusinessPlan')
      cy.checkToast('Business plan deleted successfully', 'success')
      
      // Should remove from list
      cy.getByTestId('business-plan-card').should('not.exist')
    })

    it('should cancel deletion', () => {
      cy.getByTestId('business-plan-card').within(() => {
        cy.getByTestId('delete-button').click()
      })

      cy.getByTestId('confirm-delete-modal').should('be.visible')
      cy.getByTestId('cancel-delete-button').click()

      cy.getByTestId('confirm-delete-modal').should('not.exist')
      cy.getByTestId('business-plan-card').should('exist')
    })

    it('should handle delete errors', () => {
      cy.intercept('DELETE', '**/api/business-plans/1/', { 
        statusCode: 400,
        body: { error: 'Cannot delete published business plan' }
      }).as('deleteError')

      cy.getByTestId('business-plan-card').within(() => {
        cy.getByTestId('delete-button').click()
      })

      cy.getByTestId('confirm-delete-button').click()

      cy.wait('@deleteError')
      cy.checkToast('Cannot delete published business plan', 'error')
    })

    it('should soft delete instead of hard delete', () => {
      cy.intercept('PATCH', '**/api/business-plans/1/', { 
        statusCode: 200,
        body: { ...require('../fixtures/business-plan.json'), is_deleted: true }
      }).as('softDelete')

      cy.getByTestId('business-plan-card').within(() => {
        cy.getByTestId('archive-button').click()
      })

      cy.wait('@softDelete')
      cy.checkToast('Business plan archived', 'success')
    })
  })

  describe('Accessibility and Performance', () => {
    it('should be accessible', () => {
      cy.visit('/business-plans')
      cy.checkA11y()

      cy.getByTestId('create-business-plan-button').click()
      cy.checkA11y()
    })

    it('should be responsive', () => {
      cy.visit('/business-plans')
      cy.checkResponsive()
    })

    it('should load quickly', () => {
      cy.startPerformanceTimer('business-plans-load')
      cy.visit('/business-plans')
      cy.endPerformanceTimer('business-plans-load', 3000)
    })

    it('should handle large datasets efficiently', () => {
      const largePlans = Array.from({ length: 1000 }, (_, i) => ({
        id: i + 1,
        title: `Plan ${i + 1}`,
        description: `Description ${i + 1}`
      }))

      cy.intercept('GET', '**/api/business-plans/', { 
        body: { results: largePlans.slice(0, 20), count: 1000 }
      }).as('getLargePlans')

      cy.startPerformanceTimer('large-dataset-load')
      cy.visit('/business-plans')
      cy.wait('@getLargePlans')
      cy.endPerformanceTimer('large-dataset-load', 5000)

      // Should use virtual scrolling or pagination
      cy.getByTestId('business-plan-card').should('have.length.lessThan', 50)
    })
  })
})
