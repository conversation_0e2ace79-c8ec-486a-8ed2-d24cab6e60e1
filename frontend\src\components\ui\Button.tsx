import React, { memo, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { useAppSelector } from '../../store/hooks';
import { getButtonClass } from '../../utils/themeUtils';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'danger' | 'success';
type ButtonSize = 'sm' | 'md' | 'lg';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  isLoading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  href?: string;
  fullWidth?: boolean;
  'data-testid'?: string;
  debounceMs?: number;
}

const Button: React.FC<ButtonProps> = memo(({
  children,
  variant = 'primary',
  size = 'md',
  isLoading = false,
  leftIcon,
  rightIcon,
  href,
  fullWidth = false,
  className = '',
  disabled,
  'data-testid': testId,
  debounceMs = 300,
  onClick,
  ...props
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // Debounced click handler to prevent rapid clicking
  const debouncedClick = useCallback((event: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled || isLoading) return;

    // Simple debounce implementation
    const button = event.currentTarget;
    button.disabled = true;

    setTimeout(() => {
      button.disabled = disabled || isLoading;
    }, debounceMs);

    if (onClick) {
      onClick(event);
    }
  }, [onClick, disabled, isLoading, debounceMs]);

  // Get button classes from centralized utility
  const buttonClasses = getButtonClass(variant, size, fullWidth);

  // Disabled classes
  const disabledClasses = 'opacity-50 cursor-not-allowed';

  // Combine all classes
  const allClasses = `
    ${buttonClasses}
    ${disabled || isLoading ? disabledClasses : ''}
    ${className}
  `.trim().replace(/\s+/g, ' ');

  // Content to render inside the button
  const buttonContent = (
    <>
      {leftIcon && <span className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`}>{leftIcon}</span>}
      {isLoading ? t('common.loading') : children}
      {rightIcon && <span className={`ml-2 ${isRTL ? "space-x-reverse" : ""}`}>{rightIcon}</span>}
    </>
  );

  // If href is provided, render as Link
  if (href) {
    return (
      <Link
        to={href}
        className={allClasses}
        aria-disabled={disabled || isLoading}
      >
        {buttonContent}
      </Link>
    );
  }

  // Otherwise render as button
  return (
    <button
      className={allClasses}
      disabled={disabled || isLoading}
      onClick={debouncedClick}
      data-testid={testId}
      aria-busy={isLoading}
      aria-disabled={disabled || isLoading}
      {...props}
    >
      {buttonContent}
    </button>
  );
});

Button.displayName = 'Button';

export default Button;
