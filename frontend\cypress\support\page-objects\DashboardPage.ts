export class DashboardPage {
  // Selectors
  private selectors = {
    sidebar: '[data-testid="sidebar"]',
    mainContent: '[data-testid="main-content"]',
    userProfile: '[data-testid="user-profile"]',
    logoutButton: '[data-testid="logout-button"]',
    notificationBell: '[data-testid="notification-bell"]',
    searchInput: '[data-testid="search-input"]',
    
    // Dashboard widgets
    statsCards: '[data-testid="stats-card"]',
    recentActivity: '[data-testid="recent-activity"]',
    quickActions: '[data-testid="quick-actions"]',
    chartContainer: '[data-testid="chart-container"]',
    
    // Navigation items
    dashboardNav: '[data-testid="nav-dashboard"]',
    businessPlansNav: '[data-testid="nav-business-plans"]',
    mentorshipNav: '[data-testid="nav-mentorship"]',
    fundingNav: '[data-testid="nav-funding"]',
    profileNav: '[data-testid="nav-profile"]',
    settingsNav: '[data-testid="nav-settings"]',
    
    // Mobile
    mobileMenuButton: '[data-testid="mobile-menu-button"]',
    mobileMenu: '[data-testid="mobile-menu"]'
  }

  // Navigation
  visit(role: string = 'user') {
    const dashboardPaths = {
      admin: '/admin/dashboard',
      superadmin: '/superadmin/dashboard',
      mentor: '/mentor/dashboard',
      user: '/dashboard'
    }
    
    const path = dashboardPaths[role as keyof typeof dashboardPaths] || '/dashboard'
    cy.visit(path)
    cy.waitForPageLoad()
    return this
  }

  // Actions
  logout() {
    cy.get(this.selectors.userProfile).click()
    cy.get(this.selectors.logoutButton).click()
    return this
  }

  openNotifications() {
    cy.get(this.selectors.notificationBell).click()
    return this
  }

  search(query: string) {
    cy.get(this.selectors.searchInput).clear().type(query).type('{enter}')
    return this
  }

  navigateToBusinessPlans() {
    cy.get(this.selectors.businessPlansNav).click()
    return this
  }

  navigateToMentorship() {
    cy.get(this.selectors.mentorshipNav).click()
    return this
  }

  navigateToFunding() {
    cy.get(this.selectors.fundingNav).click()
    return this
  }

  navigateToProfile() {
    cy.get(this.selectors.profileNav).click()
    return this
  }

  navigateToSettings() {
    cy.get(this.selectors.settingsNav).click()
    return this
  }

  // Mobile actions
  openMobileMenu() {
    cy.get(this.selectors.mobileMenuButton).click()
    return this
  }

  closeMobileMenu() {
    cy.get(this.selectors.mobileMenu).within(() => {
      cy.get('[data-testid="close-menu"]').click()
    })
    return this
  }

  // Assertions
  shouldBeVisible() {
    cy.get(this.selectors.mainContent).should('be.visible')
    cy.get(this.selectors.sidebar).should('be.visible')
    return this
  }

  shouldShowUserProfile(userName?: string) {
    cy.get(this.selectors.userProfile).should('be.visible')
    
    if (userName) {
      cy.get(this.selectors.userProfile).should('contain.text', userName)
    }
    
    return this
  }

  shouldShowStatsCards(expectedCount?: number) {
    cy.get(this.selectors.statsCards).should('be.visible')
    
    if (expectedCount) {
      cy.get(this.selectors.statsCards).should('have.length', expectedCount)
    }
    
    return this
  }

  shouldShowRecentActivity() {
    cy.get(this.selectors.recentActivity).should('be.visible')
    return this
  }

  shouldShowQuickActions() {
    cy.get(this.selectors.quickActions).should('be.visible')
    return this
  }

  shouldShowCharts() {
    cy.get(this.selectors.chartContainer).should('be.visible')
    
    // Wait for charts to load
    cy.get(this.selectors.chartContainer).within(() => {
      cy.get('canvas, svg').should('be.visible')
    })
    
    return this
  }

  shouldHaveCorrectNavigation(role: string) {
    const roleNavigation = {
      admin: ['dashboard', 'users', 'business-plans', 'mentorship', 'funding', 'analytics', 'settings'],
      superadmin: ['dashboard', 'admin-management', 'system-settings', 'analytics', 'reports'],
      mentor: ['dashboard', 'mentees', 'sessions', 'resources', 'profile'],
      user: ['dashboard', 'business-plans', 'mentorship', 'funding', 'profile']
    }
    
    const expectedNavItems = roleNavigation[role as keyof typeof roleNavigation] || roleNavigation.user
    
    expectedNavItems.forEach(navItem => {
      cy.getByTestId(`nav-${navItem}`).should('be.visible')
    })
    
    return this
  }

  shouldBeAccessible() {
    cy.checkA11y()
    
    // Check ARIA landmarks
    cy.get('main').should('have.attr', 'role', 'main')
    cy.get('nav').should('have.attr', 'role', 'navigation')
    
    // Check heading hierarchy
    cy.get('h1').should('exist')
    
    return this
  }

  shouldSupportKeyboardNavigation() {
    // Test sidebar navigation with keyboard
    cy.get(this.selectors.dashboardNav).focus()
    cy.realPress('Tab')
    cy.get(this.selectors.businessPlansNav).should('be.focused')
    
    // Test main content accessibility
    cy.realPress('Tab')
    cy.get(this.selectors.mainContent).within(() => {
      cy.focused().should('exist')
    })
    
    return this
  }

  shouldBeResponsive() {
    // Test mobile view
    cy.viewport(375, 667) // iPhone SE
    cy.get(this.selectors.mobileMenuButton).should('be.visible')
    cy.get(this.selectors.sidebar).should('not.be.visible')
    
    // Test tablet view
    cy.viewport(768, 1024) // iPad
    cy.get(this.selectors.sidebar).should('be.visible')
    
    // Test desktop view
    cy.viewport(1920, 1080)
    cy.get(this.selectors.sidebar).should('be.visible')
    cy.get(this.selectors.mobileMenuButton).should('not.be.visible')
    
    return this
  }

  shouldLoadDataCorrectly() {
    // Check that data is loaded
    cy.get(this.selectors.statsCards).should('not.contain.text', 'Loading...')
    cy.get(this.selectors.recentActivity).should('not.contain.text', 'Loading...')
    
    // Check for error states
    cy.get('[data-testid="error-message"]').should('not.exist')
    
    return this
  }

  shouldHandleEmptyStates() {
    // Check for empty state messages when no data
    cy.get('[data-testid="empty-state"]').should('be.visible')
    cy.get('[data-testid="empty-state"]').should('contain.text', 'No data available')
    
    return this
  }

  // Performance testing
  shouldLoadQuickly() {
    cy.startPerformanceTimer('dashboard-load')
    this.visit()
    cy.endPerformanceTimer('dashboard-load', 5000) // Should load within 5 seconds
    
    return this
  }

  // Security testing
  shouldRequireAuthentication() {
    // Clear authentication
    cy.clearLocalStorage()
    cy.clearCookies()
    
    // Try to visit dashboard
    this.visit()
    
    // Should redirect to login
    cy.url().should('include', '/login')
    
    return this
  }

  shouldShowCorrectRoleBasedContent(role: string) {
    const roleSpecificContent = {
      admin: ['User Management', 'System Analytics', 'Admin Tools'],
      superadmin: ['System Configuration', 'Global Analytics', 'Super Admin Panel'],
      mentor: ['Mentee Management', 'Session Scheduling', 'Mentor Resources'],
      user: ['My Business Plans', 'Find Mentors', 'Funding Opportunities']
    }
    
    const expectedContent = roleSpecificContent[role as keyof typeof roleSpecificContent] || roleSpecificContent.user
    
    expectedContent.forEach(content => {
      cy.get(this.selectors.mainContent).should('contain.text', content)
    })
    
    return this
  }
}
