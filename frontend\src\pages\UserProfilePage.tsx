import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { useAppSelector } from '../store/hooks';
import { userAPI } from '../services/api';
import { User } from '../services/api';
import {
  User as UserIcon,
  Mail,
  MapPin,
  Globe,
  Linkedin,
  Twitter,
  Github,
  Calendar,
  MessageSquare,
  ThumbsUp,
  Award,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import UserForumStats from '../components/forum/UserForumStats';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../hooks/useLanguage';
import { RTLText, RTLFlex, RTLIcon } from '../components/common';
import { formatDate } from '../utils/dateTimeFormatter';

const UserProfilePage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { user: currentUser } = useAppSelector(state => state.auth);
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { language } = useAppSelector(state => state.language);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('profile');

  const isOwnProfile = currentUser && (!id || parseInt(id) === currentUser.id);
  const userId = isOwnProfile ? currentUser?.id : parseInt(id || '0');

  useEffect(() => {
    const fetchUserProfile = async () => {
      setLoading(true);
      try {
        if (isOwnProfile && currentUser) {
          setUser(currentUser);
        } else if (userId) {
          const userData = await userAPI.getUserProfile(userId);
          setUser(userData);
        }
      } catch (error) {
        console.error('Error fetching user profile:', error);
        setError(t('profile.failedToLoad'));
      } finally {
        setLoading(false);
      }
    };

    fetchUserProfile();
  }, [userId, isOwnProfile, currentUser]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 text-white py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-5xl mx-auto">
          <div className={`flex justify-center items-center py-12 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
            <RTLText as="div" className="mt-4 text-gray-300">{t('profile.loadingProfile')}</RTLText>
          </div>
        </div>
      </div>
    );
  }

  if (error || !user) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 text-white py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-5xl mx-auto">
          <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
            <RTLText as="h2" className="text-xl font-semibold mb-4">{t('profile.error')}</RTLText>
            <RTLText as="div">{error || t('profile.userNotFound')}</RTLText>
            <Link to="/" className="mt-4 inline-block px-4 py-2 bg-purple-600 rounded-lg hover:bg-purple-700">
              {t('profile.returnToHome')}
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 text-white py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-5xl mx-auto">
        {/* Profile Header */}
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50 mb-6">
          <div className={`flex flex-col md:flex-row items-center md:items-start gap-6 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`w-24 h-24 md:w-32 md:h-32 rounded-full bg-purple-600/30 flex items-center justify-center text-4xl font-bold ${isRTL ? "flex-row-reverse" : ""}`}>
              {user.username?.charAt(0).toUpperCase() || 'U'}
            </div>
            <div className={`flex-1 text-center md:text-left ${isRTL ? "flex-row-reverse" : ""}`}>
              <h1 className="text-2xl md:text-3xl font-bold">{user.first_name} {user.last_name}</h1>
              <div className="text-lg text-gray-300 mb-2">@{user.username}</div>

              {user.profile?.bio && (
                <p className="text-gray-300 mb-4">{user.profile.bio}</p>
              )}

              <div className={`flex flex-wrap gap-4 justify-center md:justify-start ${isRTL ? "flex-row-reverse" : ""}`}>
                {user.profile?.location && (
                  <div className={`flex items-center text-gray-300 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <MapPin size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                    <span>{user.profile.location}</span>
                  </div>
                )}

                <div className={`flex items-center text-gray-300 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <Mail size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                  <span>{user.email}</span>
                </div>

                {user.profile?.website && (
                  <div className={`flex items-center text-gray-300 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <Globe size={16} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                    <a href={user.profile.website} target="_blank" rel="noopener noreferrer" className="hover:text-purple-400">
                      {t('profile.website')}
                    </a>
                  </div>
                )}
              </div>

              <div className={`flex flex-wrap gap-4 mt-4 justify-center md:justify-start ${isRTL ? "flex-row-reverse" : ""}`}>
                {user.profile?.github && (
                  <a
                    href={`https://github.com/${user.profile.github}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-300 hover:text-purple-400"
                  >
                    <Github size={20} />
                  </a>
                )}

                {user.profile?.linkedin && (
                  <a
                    href={`https://linkedin.com/in/${user.profile.linkedin}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-300 hover:text-purple-400"
                  >
                    <Linkedin size={20} />
                  </a>
                )}

                {user.profile?.twitter && (
                  <a
                    href={`https://twitter.com/${user.profile.twitter}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-300 hover:text-purple-400"
                  >
                    <Twitter size={20} />
                  </a>
                )}
              </div>
            </div>

            {isOwnProfile && (
              <div className="mt-4 md:mt-0">
                <Link
                  to="/profile/edit"
                  className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg"
                >
                  {t('profile.editProfile')}
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* Profile Tabs */}
        <div className={`flex flex-wrap gap-2 mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
          <button
            className={`px-4 py-2 rounded-lg flex items-center gap-2 ${
              activeTab === 'profile'
                ? 'bg-purple-700/50 text-white'
                : 'bg-indigo-900/30 hover:bg-indigo-800/30 text-gray-300'}
            }`}
            onClick={() => setActiveTab('profile')}
          >
            <UserIcon size={16} />
            {t('profile.title')}
          </button>
          <button
            className={`px-4 py-2 rounded-lg flex items-center gap-2 ${
              activeTab === 'forum'
                ? 'bg-purple-700/50 text-white'
                : 'bg-indigo-900/30 hover:bg-indigo-800/30 text-gray-300'}
            }`}
            onClick={() => setActiveTab('forum')}
          >
            <MessageSquare size={16} />
            {t('profile.forumActivity')}
          </button>
        </div>

        {/* Tab Content */}
        {activeTab === 'profile' && (
          <div className="space-y-6">
            {/* Profile Details */}
            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
              <RTLText as="h2" className="text-xl font-semibold mb-4">{t('profile.profileDetails')}</RTLText>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <RTLText as="h3" className="text-lg font-medium mb-2">{t('profile.personalInformation')}</RTLText>
                  <div className="space-y-3">
                    <div>
                      <RTLText as="div" className="text-sm text-gray-400">{t('profile.fullName')}</RTLText>
                      <RTLText as="p">{user.first_name} {user.last_name}</RTLText>
                    </div>
                    <div>
                      <RTLText as="div" className="text-sm text-gray-400">{t('profile.username')}</RTLText>
                      <RTLText as="p">{user.username}</RTLText>
                    </div>
                    <div>
                      <RTLText as="div" className="text-sm text-gray-400">{t('profile.email')}</RTLText>
                      <RTLText as="p">{user.email}</RTLText>
                    </div>
                    {user.profile?.location && (
                      <div>
                        <RTLText as="div" className="text-sm text-gray-400">{t('profile.location')}</RTLText>
                        <RTLText as="p">{user.profile.location}</RTLText>
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <RTLText as="h3" className="text-lg font-medium mb-2">{t('profile.professionalInformation')}</RTLText>
                  <div className="space-y-3">
                    {user.profile?.expertise && (
                      <div>
                        <RTLText as="div" className="text-sm text-gray-400">{t('profile.expertise')}</RTLText>
                        <RTLText as="p">{user.profile.expertise}</RTLText>
                      </div>
                    )}
                    {user.profile?.website && (
                      <div>
                        <RTLText as="div" className="text-sm text-gray-400">{t('profile.website')}</RTLText>
                        <a
                          href={user.profile.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-purple-400 hover:text-purple-300"
                        >
                          {user.profile.website}
                        </a>
                      </div>
                    )}
                    <div>
                      <RTLText as="div" className="text-sm text-gray-400">{t('profile.memberSince')}</RTLText>
                      <RTLText as="p">{user.date_joined ? formatDate(user.date_joined) : t('profile.dateUnknown', 'Date unknown')}</RTLText>
                    </div>
                    {user.profile?.completion_percentage !== undefined && (
                      <div>
                        <RTLText as="div" className="text-sm text-gray-400">{t('profile.completion')}</RTLText>
                        <div className="w-full bg-indigo-900/50 rounded-full h-2.5 mt-1">
                          <div
                            className="bg-purple-600 h-2.5 rounded-full"
                            style={{ width: `${user.profile.completion_percentage}%` }}
                          ></div>
                        </div>
                        <RTLText as="p" className="text-xs mt-1">{user.profile.completion_percentage}% {t('profile.complete')}</RTLText>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'forum' && (
          <UserForumStats />
        )}
      </div>
    </div>
  );
};

export default UserProfilePage;
