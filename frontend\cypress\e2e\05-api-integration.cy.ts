describe('API Integration Tests', () => {
  const API_BASE_URL = Cypress.env('apiUrl') || 'http://localhost:8000'

  beforeEach(() => {
    // Clear any existing authentication
    cy.clearLocalStorage()
    cy.clearCookies()
  })

  describe('Authentication API', () => {
    it('should handle successful login', () => {
      cy.intercept('POST', `${API_BASE_URL}/api/auth/login/`, {
        statusCode: 200,
        body: {
          access: 'valid-access-token',
          refresh: 'valid-refresh-token',
          user: {
            id: 1,
            email: '<EMAIL>',
            first_name: 'Test',
            last_name: 'User',
            role: 'user'
          }
        }
      }).as('loginSuccess')

      cy.visit('/login')
      cy.getByTestId('email-input').type('<EMAIL>')
      cy.getByTestId('password-input').type('password123')
      cy.getByTestId('login-button').click()

      cy.wait('@loginSuccess').then((interception) => {
        expect(interception.request.body).to.deep.include({
          email: '<EMAIL>',
          password: 'password123'
        })
      })

      cy.url().should('include', '/dashboard')
    })

    it('should handle login errors', () => {
      cy.intercept('POST', `${API_BASE_URL}/api/auth/login/`, {
        statusCode: 401,
        body: {
          error: 'Invalid credentials'
        }
      }).as('loginError')

      cy.visit('/login')
      cy.getByTestId('email-input').type('<EMAIL>')
      cy.getByTestId('password-input').type('wrongpassword')
      cy.getByTestId('login-button').click()

      cy.wait('@loginError')
      cy.checkToast('Invalid credentials', 'error')
      cy.url().should('include', '/login')
    })

    it('should handle token refresh', () => {
      // Set up initial authentication
      cy.window().then((win) => {
        win.localStorage.setItem('token', 'expiring-token')
        win.localStorage.setItem('refreshToken', 'valid-refresh-token')
      })

      cy.intercept('POST', `${API_BASE_URL}/api/auth/refresh/`, {
        statusCode: 200,
        body: {
          access: 'new-access-token'
        }
      }).as('tokenRefresh')

      cy.intercept('GET', `${API_BASE_URL}/api/auth/user/`, {
        statusCode: 401
      }).as('unauthorizedRequest')

      cy.visit('/dashboard')

      cy.wait('@unauthorizedRequest')
      cy.wait('@tokenRefresh')

      // Should have new token
      cy.window().its('localStorage.token').should('eq', 'new-access-token')
    })

    it('should handle logout', () => {
      cy.intercept('POST', `${API_BASE_URL}/api/auth/logout/`, {
        statusCode: 200
      }).as('logout')

      cy.loginAs('user')
      cy.visit('/dashboard')

      cy.getByTestId('user-profile').click()
      cy.getByTestId('logout-button').click()

      cy.wait('@logout').then((interception) => {
        expect(interception.request.headers).to.have.property('authorization')
      })

      cy.url().should('include', '/login')
      cy.window().its('localStorage').should('not.have.property', 'token')
    })
  })

  describe('Business Plans API', () => {
    beforeEach(() => {
      cy.loginAs('user')
    })

    it('should fetch business plans list', () => {
      const mockBusinessPlans = [
        { id: 1, title: 'Plan 1', status: 'draft' },
        { id: 2, title: 'Plan 2', status: 'published' }
      ]

      cy.intercept('GET', `${API_BASE_URL}/api/business-plans/`, {
        statusCode: 200,
        body: {
          results: mockBusinessPlans,
          count: 2,
          next: null,
          previous: null
        }
      }).as('getBusinessPlans')

      cy.visit('/business-plans')

      cy.wait('@getBusinessPlans').then((interception) => {
        expect(interception.request.headers).to.have.property('authorization')
        expect(interception.request.headers.authorization).to.include('Bearer')
      })

      cy.getByTestId('business-plan-card').should('have.length', 2)
    })

    it('should create business plan', () => {
      const newBusinessPlan = {
        title: 'New Business Plan',
        description: 'Test description',
        industry: 'Technology',
        stage: 'idea'
      }

      cy.intercept('POST', `${API_BASE_URL}/api/business-plans/`, {
        statusCode: 201,
        body: {
          id: 3,
          ...newBusinessPlan,
          created_at: new Date().toISOString()
        }
      }).as('createBusinessPlan')

      cy.visit('/business-plans/create')

      cy.fillForm({
        title: newBusinessPlan.title,
        description: newBusinessPlan.description,
        industry: newBusinessPlan.industry,
        stage: newBusinessPlan.stage
      })

      cy.getByTestId('submit-button').click()

      cy.wait('@createBusinessPlan').then((interception) => {
        expect(interception.request.body).to.deep.include(newBusinessPlan)
        expect(interception.request.headers).to.have.property('authorization')
      })

      cy.checkToast('Business plan created successfully', 'success')
    })

    it('should update business plan', () => {
      const updatedData = {
        title: 'Updated Business Plan',
        description: 'Updated description'
      }

      cy.intercept('GET', `${API_BASE_URL}/api/business-plans/1/`, {
        statusCode: 200,
        body: { id: 1, title: 'Original Plan', description: 'Original description' }
      }).as('getBusinessPlan')

      cy.intercept('PUT', `${API_BASE_URL}/api/business-plans/1/`, {
        statusCode: 200,
        body: { id: 1, ...updatedData }
      }).as('updateBusinessPlan')

      cy.visit('/business-plans/1/edit')
      cy.wait('@getBusinessPlan')

      cy.getByTestId('title-input').clear().type(updatedData.title)
      cy.getByTestId('description-input').clear().type(updatedData.description)
      cy.getByTestId('save-button').click()

      cy.wait('@updateBusinessPlan').then((interception) => {
        expect(interception.request.body).to.deep.include(updatedData)
      })

      cy.checkToast('Business plan updated successfully', 'success')
    })

    it('should delete business plan', () => {
      cy.intercept('DELETE', `${API_BASE_URL}/api/business-plans/1/`, {
        statusCode: 204
      }).as('deleteBusinessPlan')

      cy.intercept('GET', `${API_BASE_URL}/api/business-plans/`, {
        statusCode: 200,
        body: {
          results: [{ id: 1, title: 'Plan to Delete', status: 'draft' }],
          count: 1
        }
      }).as('getBusinessPlans')

      cy.visit('/business-plans')
      cy.wait('@getBusinessPlans')

      cy.getByTestId('business-plan-card').within(() => {
        cy.getByTestId('delete-button').click()
      })

      cy.getByTestId('confirm-delete-button').click()

      cy.wait('@deleteBusinessPlan').then((interception) => {
        expect(interception.request.headers).to.have.property('authorization')
      })

      cy.checkToast('Business plan deleted successfully', 'success')
    })

    it('should handle API errors gracefully', () => {
      cy.intercept('GET', `${API_BASE_URL}/api/business-plans/`, {
        statusCode: 500,
        body: { error: 'Internal server error' }
      }).as('serverError')

      cy.visit('/business-plans')

      cy.wait('@serverError')
      cy.getByTestId('error-message').should('be.visible')
      cy.getByTestId('error-message').should('contain.text', 'Failed to load business plans')
      cy.getByTestId('retry-button').should('be.visible')
    })

    it('should handle network errors', () => {
      cy.intercept('GET', `${API_BASE_URL}/api/business-plans/`, {
        forceNetworkError: true
      }).as('networkError')

      cy.visit('/business-plans')

      cy.wait('@networkError')
      cy.getByTestId('error-message').should('contain.text', 'Network error')
      cy.getByTestId('retry-button').should('be.visible')
    })
  })

  describe('User Management API (Admin)', () => {
    beforeEach(() => {
      cy.loginAs('admin')
    })

    it('should fetch users list', () => {
      const mockUsers = [
        { id: 1, email: '<EMAIL>', role: 'user', is_active: true },
        { id: 2, email: '<EMAIL>', role: 'mentor', is_active: true }
      ]

      cy.intercept('GET', `${API_BASE_URL}/api/admin/users/`, {
        statusCode: 200,
        body: {
          results: mockUsers,
          count: 2
        }
      }).as('getUsers')

      cy.visit('/admin/users')

      cy.wait('@getUsers').then((interception) => {
        expect(interception.request.headers).to.have.property('authorization')
      })

      cy.getByTestId('users-table').should('be.visible')
      cy.getByTestId('user-row').should('have.length', 2)
    })

    it('should create new user', () => {
      const newUser = {
        email: '<EMAIL>',
        first_name: 'New',
        last_name: 'User',
        role: 'user'
      }

      cy.intercept('POST', `${API_BASE_URL}/api/admin/users/`, {
        statusCode: 201,
        body: { id: 3, ...newUser }
      }).as('createUser')

      cy.visit('/admin/users')
      cy.getByTestId('create-user-button').click()

      cy.fillForm({
        email: newUser.email,
        'first-name': newUser.first_name,
        'last-name': newUser.last_name,
        role: newUser.role
      })

      cy.getByTestId('submit-button').click()

      cy.wait('@createUser').then((interception) => {
        expect(interception.request.body).to.deep.include(newUser)
      })

      cy.checkToast('User created successfully', 'success')
    })

    it('should update user role', () => {
      cy.intercept('PATCH', `${API_BASE_URL}/api/admin/users/1/`, {
        statusCode: 200,
        body: { id: 1, role: 'admin' }
      }).as('updateUserRole')

      cy.visit('/admin/users')
      
      cy.getByTestId('user-row').first().within(() => {
        cy.getByTestId('role-select').select('admin')
        cy.getByTestId('save-role-button').click()
      })

      cy.wait('@updateUserRole').then((interception) => {
        expect(interception.request.body).to.deep.include({ role: 'admin' })
      })

      cy.checkToast('User role updated', 'success')
    })
  })

  describe('File Upload API', () => {
    beforeEach(() => {
      cy.loginAs('user')
    })

    it('should upload file successfully', () => {
      cy.intercept('POST', `${API_BASE_URL}/api/files/upload/`, {
        statusCode: 201,
        body: {
          id: 1,
          filename: 'test-document.pdf',
          url: '/media/uploads/test-document.pdf',
          size: 1024
        }
      }).as('uploadFile')

      cy.visit('/business-plans/create')
      
      cy.uploadFile('[data-testid="file-upload"]', 'test-document.pdf')

      cy.wait('@uploadFile').then((interception) => {
        expect(interception.request.headers).to.have.property('content-type')
        expect(interception.request.headers['content-type']).to.include('multipart/form-data')
      })

      cy.getByTestId('uploaded-file').should('contain.text', 'test-document.pdf')
    })

    it('should handle file upload errors', () => {
      cy.intercept('POST', `${API_BASE_URL}/api/files/upload/`, {
        statusCode: 413,
        body: { error: 'File too large' }
      }).as('uploadError')

      cy.visit('/business-plans/create')
      
      cy.uploadFile('[data-testid="file-upload"]', 'large-file.pdf')

      cy.wait('@uploadError')
      cy.checkToast('File too large', 'error')
    })
  })

  describe('Real-time Features', () => {
    beforeEach(() => {
      cy.loginAs('user')
    })

    it('should handle WebSocket connections', () => {
      // Mock WebSocket connection
      cy.window().then((win) => {
        const mockWebSocket = {
          send: cy.stub(),
          close: cy.stub(),
          readyState: 1, // OPEN
          addEventListener: cy.stub()
        }
        
        win.WebSocket = cy.stub().returns(mockWebSocket)
      })

      cy.visit('/dashboard')

      // Should establish WebSocket connection
      cy.window().then((win) => {
        expect(win.WebSocket).to.have.been.calledWith(`ws://localhost:8000/ws/notifications/`)
      })
    })

    it('should handle real-time notifications', () => {
      cy.visit('/dashboard')

      // Simulate receiving a notification
      cy.window().then((win) => {
        const event = new CustomEvent('notification', {
          detail: {
            type: 'business_plan_approved',
            message: 'Your business plan has been approved',
            timestamp: new Date().toISOString()
          }
        })
        win.dispatchEvent(event)
      })

      cy.getByTestId('notification-toast').should('be.visible')
      cy.getByTestId('notification-toast').should('contain.text', 'Your business plan has been approved')
    })
  })

  describe('API Performance', () => {
    beforeEach(() => {
      cy.loginAs('user')
    })

    it('should load data within acceptable time limits', () => {
      cy.intercept('GET', `${API_BASE_URL}/api/business-plans/`, {
        statusCode: 200,
        body: { results: [], count: 0 },
        delay: 100 // Simulate 100ms response time
      }).as('getBusinessPlans')

      cy.startPerformanceTimer('api-response-time')
      cy.visit('/business-plans')
      cy.wait('@getBusinessPlans')
      cy.endPerformanceTimer('api-response-time', 2000) // Should complete within 2 seconds
    })

    it('should handle concurrent requests efficiently', () => {
      // Set up multiple API endpoints
      cy.intercept('GET', `${API_BASE_URL}/api/business-plans/`, { fixture: 'business-plan.json' }).as('getBusinessPlans')
      cy.intercept('GET', `${API_BASE_URL}/api/mentorship/`, { body: [] }).as('getMentorship')
      cy.intercept('GET', `${API_BASE_URL}/api/funding/`, { body: [] }).as('getFunding')
      cy.intercept('GET', `${API_BASE_URL}/api/notifications/`, { body: [] }).as('getNotifications')

      cy.visit('/dashboard')

      // All requests should complete
      cy.wait(['@getBusinessPlans', '@getMentorship', '@getFunding', '@getNotifications'])
      
      cy.getByTestId('dashboard-content').should('be.visible')
    })
  })

  describe('API Security', () => {
    it('should include CSRF tokens in requests', () => {
      cy.loginAs('user')

      cy.intercept('POST', `${API_BASE_URL}/api/business-plans/`, (req) => {
        expect(req.headers).to.have.property('x-csrftoken')
        req.reply({ statusCode: 201, body: { id: 1 } })
      }).as('createWithCSRF')

      cy.visit('/business-plans/create')
      cy.fillForm({ title: 'Test Plan', description: 'Test' })
      cy.getByTestId('submit-button').click()

      cy.wait('@createWithCSRF')
    })

    it('should handle unauthorized access', () => {
      cy.intercept('GET', `${API_BASE_URL}/api/admin/users/`, {
        statusCode: 403,
        body: { error: 'Permission denied' }
      }).as('unauthorizedAccess')

      cy.loginAs('user') // Regular user trying to access admin endpoint
      cy.visit('/admin/users', { failOnStatusCode: false })

      cy.wait('@unauthorizedAccess')
      cy.get('body').should('contain.text', 'Access Denied')
    })

    it('should validate request data', () => {
      cy.loginAs('user')

      cy.intercept('POST', `${API_BASE_URL}/api/business-plans/`, {
        statusCode: 400,
        body: {
          errors: {
            title: ['This field is required'],
            email: ['Enter a valid email address']
          }
        }
      }).as('validationError')

      cy.visit('/business-plans/create')
      cy.getByTestId('submit-button').click() // Submit without required fields

      cy.wait('@validationError')
      cy.getByTestId('title-input').should('have.attr', 'aria-invalid', 'true')
    })
  })
})
