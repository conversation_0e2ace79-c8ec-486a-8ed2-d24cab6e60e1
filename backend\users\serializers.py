from rest_framework import serializers
from django.contrib.auth.models import User
from django.db.models import Count
from .models import UserProfile, UserRole, UserRoleAssignment, RoleApplication
from forums.models import UserReputation, ForumThread, ForumPost


class UserRoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserRole
        fields = ['id', 'name', 'display_name', 'description', 'permission_level', 'is_active', 'requires_approval']


class UserRoleAssignmentSerializer(serializers.ModelSerializer):
    role = UserRoleSerializer(read_only=True)

    class Meta:
        model = UserRoleAssignment
        fields = ['id', 'role', 'assigned_at', 'expires_at', 'is_active', 'is_approved', 'notes']


class RoleApplicationSerializer(serializers.ModelSerializer):
    requested_role = UserRoleSerializer(read_only=True)
    user = serializers.StringRelatedField(read_only=True)

    class Meta:
        model = RoleApplication
        fields = ['id', 'user', 'requested_role', 'motivation', 'qualifications',
                 'experience', 'portfolio_url', 'status', 'created_at', 'reviewed_at', 'admin_notes']
        read_only_fields = ['user', 'status', 'reviewed_at', 'admin_notes']

class UserProfileSerializer(serializers.ModelSerializer):
    completion_percentage = serializers.SerializerMethodField()
    primary_role = UserRoleSerializer(read_only=True)
    active_roles = serializers.SerializerMethodField()
    highest_permission_level = serializers.SerializerMethodField()

    class Meta:
        model = UserProfile
        fields = ['id', 'bio', 'location', 'expertise', 'profile_image',
                  'website', 'github', 'linkedin', 'twitter', 'company',
                  'years_of_experience', 'primary_role', 'active_roles',
                  'highest_permission_level', 'created_at', 'updated_at',
                  'completion_percentage']

    def get_completion_percentage(self, obj):
        return obj.calculate_completion_percentage()

    def get_active_roles(self, obj):
        active_roles = obj.get_active_roles()
        return UserRoleSerializer(active_roles, many=True).data

    def get_highest_permission_level(self, obj):
        return obj.get_highest_permission_level()

class UserSerializer(serializers.ModelSerializer):
    profile = UserProfileSerializer(read_only=True)
    is_admin = serializers.SerializerMethodField()
    forum_stats = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'profile',
                 'is_admin', 'is_superuser', 'is_staff', 'forum_stats']
        read_only_fields = ['id', 'is_admin', 'is_superuser', 'is_staff', 'forum_stats']

    def get_is_admin(self, obj):
        # Consider a user admin if they are staff or superuser
        return obj.is_staff or obj.is_superuser

    def get_forum_stats(self, obj):
        """Get forum statistics for the user"""
        # Get thread and post counts
        thread_count = ForumThread.objects.filter(author=obj).count()
        post_count = ForumPost.objects.filter(author=obj).count()

        # Get like counts
        likes_received = ForumPost.objects.filter(author=obj).aggregate(
            total_likes=Count('likes')
        )['total_likes'] or 0

        # Get solution count
        solution_count = ForumPost.objects.filter(author=obj, is_solution=True).count()

        # Get reputation data
        try:
            reputation = UserReputation.objects.get(user=obj)
            reputation_data = {
                'points': reputation.points,
                'level': reputation.level,
                'threads_created': reputation.threads_created,
                'posts_created': reputation.posts_created,
                'solutions_provided': reputation.solutions_provided,
                'likes_received': reputation.likes_received,
                'likes_given': reputation.likes_given
            }
        except UserReputation.DoesNotExist:
            reputation_data = {
                'points': 0,
                'level': 'Newcomer',
                'threads_created': 0,
                'posts_created': 0,
                'solutions_provided': 0,
                'likes_received': 0,
                'likes_given': 0
            }

        # Get subscription counts
        topic_subscriptions = obj.topic_subscriptions.count() if hasattr(obj, 'topic_subscriptions') else 0
        thread_subscriptions = obj.thread_subscriptions.count() if hasattr(obj, 'thread_subscriptions') else 0

        return {
            'thread_count': thread_count,
            'post_count': post_count,
            'likes_received': likes_received,
            'solution_count': solution_count,
            'reputation': reputation_data,
            'subscriptions': {
                'topics': topic_subscriptions,
                'threads': thread_subscriptions
            }
        }

class UserRegistrationSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)
    password_confirm = serializers.CharField(write_only=True)

    class Meta:
        model = User
        fields = ['username', 'email', 'password', 'password_confirm', 'first_name', 'last_name']

    def validate(self, data):
        if data['password'] != data['password_confirm']:
            raise serializers.ValidationError("Passwords do not match")
        return data

    def create(self, validated_data):
        validated_data.pop('password_confirm')
        user = User.objects.create_user(
            username=validated_data['username'],
            email=validated_data['email'],
            password=validated_data['password'],
            first_name=validated_data.get('first_name', ''),
            last_name=validated_data.get('last_name', '')
        )
        return user


class AdminUserCreationSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)
    password_confirm = serializers.CharField(write_only=True)
    role = serializers.CharField(required=False, default='user')
    is_active = serializers.BooleanField(required=False, default=True)

    class Meta:
        model = User
        fields = ['username', 'email', 'password', 'password_confirm', 'first_name', 'last_name', 'role', 'is_active']

    def validate(self, data):
        if data['password'] != data['password_confirm']:
            raise serializers.ValidationError("Passwords do not match")
        return data

    def create(self, validated_data):
        # Remove non-User fields
        validated_data.pop('password_confirm')
        role = validated_data.pop('role', 'user')
        is_active = validated_data.pop('is_active', True)

        # Create the user
        user = User.objects.create_user(
            username=validated_data['username'],
            email=validated_data['email'],
            password=validated_data['password'],
            first_name=validated_data.get('first_name', ''),
            last_name=validated_data.get('last_name', ''),
            is_active=is_active
        )

        # Set admin status based on role
        if role in ['admin', 'super_admin']:
            user.is_staff = True
            if role == 'super_admin':
                user.is_superuser = True
            user.save()

        # Set role in user profile if it exists
        if hasattr(user, 'profile'):
            from .models import UserRole
            try:
                user_role = UserRole.objects.get(name=role)
                user.profile.primary_role = user_role
                user.profile.save()
            except UserRole.DoesNotExist:
                pass  # Role doesn't exist, skip setting it

        return user
