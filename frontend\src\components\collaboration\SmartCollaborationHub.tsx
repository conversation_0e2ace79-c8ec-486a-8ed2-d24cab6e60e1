/**
 * Smart Collaboration Hub
 * Advanced real-time collaboration platform with AI-powered features
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  Users,
  MessageSquare,
  Video,
  Share2,
  Bell,
  Calendar,
  FileText,
  Brain,
  Zap,
  Clock,
  CheckCircle,
  AlertCircle,
  UserPlus,
  Settings,
  Activity,
  Eye,
  Edit3,
  Send,
  Mic,
  MicOff,
  VideoOff,
  Phone,
  PhoneOff,
  Monitor,
  MoreHorizontal
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { RTLText, RTLFlex } from '../common';

interface Collaborator {
  id: string;
  name: string;
  email: string;
  avatar: string;
  role: 'owner' | 'editor' | 'reviewer' | 'viewer';
  status: 'online' | 'away' | 'offline';
  lastSeen: Date;
  currentSection?: string;
  permissions: string[];
}

interface Message {
  id: string;
  senderId: string;
  senderName: string;
  content: string;
  timestamp: Date;
  type: 'text' | 'file' | 'system' | 'ai_suggestion';
  attachments?: Array<{ name: string; url: string; type: string }>;
  reactions?: Array<{ emoji: string; users: string[] }>;
  threadId?: string;
}

interface AIInsight {
  id: string;
  type: 'suggestion' | 'warning' | 'opportunity' | 'improvement';
  title: string;
  description: string;
  confidence: number;
  section?: string;
  actionable: boolean;
  timestamp: Date;
}

interface VideoCall {
  id: string;
  participants: Collaborator[];
  isActive: boolean;
  startTime: Date;
  duration: number;
  isRecording: boolean;
}

export const SmartCollaborationHub: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [activeTab, setActiveTab] = useState<'chat' | 'video' | 'insights' | 'activity'>('chat');
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [messages, setMessages] = useState<Message[]>([]);
  const [aiInsights, setAiInsights] = useState<AIInsight[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState<string[]>([]);
  const [videoCall, setVideoCall] = useState<VideoCall | null>(null);
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoOff, setIsVideoOff] = useState(false);
  const [isScreenSharing, setIsScreenSharing] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Load collaboration data from API
  useEffect(() => {
    const loadCollaborationData = async () => {
      try {
        // This would be replaced with actual collaborative API
        // For now, show empty state since collaboration requires backend implementation
        setCollaborators([]);
        setMessages([]);
        setAiInsights([]);
      } catch (error) {
        console.error('Error loading collaboration data:', error);
      }
    };

    loadCollaborationData();
  }, [templateId]);

  // Auto-scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const sendMessage = () => {
    if (!newMessage.trim()) return;

    const message: Message = {
      id: Date.now().toString(),
      senderId: '1', // Current user
      senderName: t("common.ahmed.alrashid", "Ahmed Al-Rashid"),
      content: newMessage,
      timestamp: new Date(),
      type: 'text'
    };

    setMessages(prev => [...prev, message]);
    setNewMessage('');

    // ✅ REAL DATA: Use actual AI API instead of setTimeout simulation
    const handleAIResponse = async () => {
      if (newMessage.toLowerCase().includes('help') || newMessage.toLowerCase().includes('suggest')) {
        try {
          // TODO: Replace with actual AI API call
          const { aiAPI } = await import('../../services/aiApi');
          const response = await aiAPI.getChatResponse(newMessage);

          const aiResponse: Message = {
            id: (Date.now() + 1).toString(),
            senderId: 'ai',
            senderName: t("common.yasmeen.ai.content", "Yasmeen AI"),
            content: response.content || 'I can help you with that! Based on your business plan, I have several suggestions. Would you like me to analyze specific sections or provide general recommendations?',
            timestamp: new Date(),
            type: 'ai_suggestion'
          };
          setMessages(prev => [...prev, aiResponse]);
        } catch (error) {
          console.error('AI response error:', error);
          // Fallback message without setTimeout
          const aiResponse: Message = {
            id: (Date.now() + 1).toString(),
            senderId: 'ai',
            senderName: t("common.yasmeen.ai.content", "Yasmeen AI"),
            content: 'I can help you with that! Based on your business plan, I have several suggestions. Would you like me to analyze specific sections or provide general recommendations?',
            timestamp: new Date(),
            type: 'ai_suggestion'
          };
          setMessages(prev => [...prev, aiResponse]);
        }
      }
    };

    handleAIResponse();
  };

  const startVideoCall = () => {
    const call: VideoCall = {
      id: Date.now().toString(),
      participants: collaborators.filter(c => c.status === 'online'),
      isActive: true,
      startTime: new Date(),
      duration: 0,
      isRecording: false
    };
    setVideoCall(call);
  };

  const endVideoCall = () => {
    setVideoCall(null);
    setIsMuted(false);
    setIsVideoOff(false);
    setIsScreenSharing(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-400';
      case 'away': return 'bg-yellow-400';
      case 'offline': return 'bg-gray-400';
      default: return 'bg-gray-400';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'owner': return 'text-purple-400';
      case 'editor': return 'text-blue-400';
      case 'reviewer': return 'text-yellow-400';
      case 'viewer': return 'text-gray-400';
      default: return 'text-gray-400';
    }
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'suggestion': return <Zap className="text-blue-400" size={16} />;
      case 'warning': return <AlertCircle className="text-yellow-400" size={16} />;
      case 'opportunity': return <CheckCircle className="text-green-400" size={16} />;
      case 'improvement': return <Brain className="text-purple-400" size={16} />;
      default: return <Brain className="text-gray-400" size={16} />;
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatRelativeTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);

    if (minutes < 1) return t("common.just.now", "Just now");
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  };

  return (
    <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 overflow-hidden">
      {/* Header */}
      <div className="bg-gray-800/50 border-b border-gray-700/50 p-4">
        <RTLFlex className="items-center justify-between">
          <RTLFlex className="items-center">
            <Users className="text-purple-400" size={24} />
            <RTLText className={`text-lg font-semibold text-white ${isRTL ? 'mr-3' : 'ml-3'}`}>
              Collaboration Hub
            </RTLText>
            <span className={`px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full ml-3 ${isRTL ? "space-x-reverse" : ""}`}>
              {collaborators.filter(c => c.status === 'online').length} online
            </span>
          </RTLFlex>

          <RTLFlex className="items-center gap-2">
            <button
              onClick={startVideoCall}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
              title={t("common.start.video.call", t("common.start.video.call", "Start video call"))}
            >
              <Video size={20} />
            </button>
            <button className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <Share2 size={20} />
            </button>
            <button className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors">
              <Settings size={20} />
            </button>
          </RTLFlex>
        </RTLFlex>

        {/* Tabs */}
        <div className={`flex gap-1 mt-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          {[
            { id: 'chat', label: 'Chat', icon: MessageSquare },
            { id: 'video', label: t("common.video", "Video"), icon: Video },
            { id: 'insights', label: t("common.ai.insights", "AI Insights"), icon: Brain },
            { id: 'activity', label: t("common.activity", "Activity"), icon: Activity }
          ].map(tab => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700'}
                }`}
              >
                <Icon size={16} />
                <span className={isRTL ? 'mr-2' : 'ml-2'}>{tab.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Content */}
      <div className={`h-96 flex ${isRTL ? "flex-row-reverse" : ""}`}>
        {/* Sidebar - Collaborators */}
        <div className="w-64 bg-gray-800/30 border-r border-gray-700/50 p-4">
          <h3 className="text-sm font-medium text-gray-300 mb-3">t("common.team.members", "Team Members")</h3>
          <div className="space-y-3">
            {collaborators.map(collaborator => (
              <div key={collaborator.id} className={`flex items-center gap-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className="relative">
                  <div className={`w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                    {collaborator.avatar}
                  </div>
                  <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-gray-800 ${getStatusColor(collaborator.status)}`} />
                </div>
                <div className={`flex-1 min-w-0 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className="text-sm font-medium text-white truncate">
                    {collaborator.name}
                  </div>
                  <div className={`text-xs ${getRoleColor(collaborator.role)}`}>
                    {collaborator.role}
                  </div>
                  {collaborator.currentSection && (
                    <div className="text-xs text-gray-500 truncate">
                      Editing: {collaborator.currentSection}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          <button className={`w-full mt-4 flex items-center justify-center gap-2 px-3 py-2 bg-purple-600/20 text-purple-400 rounded-lg hover:bg-purple-600/30 transition-colors ${isRTL ? "flex-row-reverse" : ""}`}>
            <UserPlus size={16} />
            <span>t("common.invite", "Invite")</span>
          </button>
        </div>

        {/* Main Content */}
        <div className={`flex-1 flex flex-col ${isRTL ? "flex-row-reverse" : ""}`}>
          {activeTab === 'chat' && (
            <>
              {/* Messages */}
              <div className={`flex-1 p-4 overflow-y-auto ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className="space-y-4">
                  {messages.map(message => (
                    <div key={message.id} className={`flex gap-3 ${message.senderId === '1' ? 'flex-row-reverse' : ''}`}>
                      <div className={`w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center text-sm flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`}>
                        {message.senderId === 'ai' ? '🤖' : '👤'}
                      </div>
                      <div className={`flex-1 ${message.senderId === '1' ? 'text-right' : ''}`}>
                        <div className={`flex items-center gap-2 mb-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                          <span className="text-sm font-medium text-white">{message.senderName}</span>
                          <span className="text-xs text-gray-500">{formatTime(message.timestamp)}</span>
                        </div>
                        <div className={`inline-block px-3 py-2 rounded-lg max-w-xs ${
                          message.senderId === '1'
                            ? 'bg-purple-600 text-white'
                            : message.type === 'ai_suggestion'
                            ? 'bg-blue-600/20 border border-blue-500/30 text-blue-300'
                            : 'bg-gray-700 text-white'}
                        }`}>
                          {message.content}
                        </div>
                        {message.reactions && (
                          <div className={`flex gap-1 mt-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                            {message.reactions.map((reaction, idx) => (
                              <span key={idx} className="text-xs bg-gray-700 px-2 py-1 rounded-full">
                                {reaction.emoji} {reaction.users.length}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                  <div ref={messagesEndRef} />
                </div>
              </div>

              {/* Message Input */}
              <div className="border-t border-gray-700/50 p-4">
                <RTLFlex className="items-center gap-3">
                  <input
                    type="text"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                    placeholder={t("common.type.a.message", "Type a message...")}
                    className={`flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent ${isRTL ? "flex-row-reverse" : ""}`}
                  />
                  <button
                    onClick={sendMessage}
                    disabled={!newMessage.trim()}
                    className="p-2 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                  >
                    <Send size={20} />
                  </button>
                </RTLFlex>
              </div>
            </>
          )}

          {activeTab === 'insights' && (
            <div className={`flex-1 p-4 overflow-y-auto ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className="space-y-4">
                {aiInsights.map(insight => (
                  <div key={insight.id} className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50">
                    <RTLFlex className="items-start gap-3">
                      {getInsightIcon(insight.type)}
                      <div className={`flex-1 ${isRTL ? "flex-row-reverse" : ""}`}>
                        <RTLFlex className="items-center justify-between mb-2">
                          <h4 className="font-medium text-white">{insight.title}</h4>
                          <span className="text-xs text-gray-500">{formatRelativeTime(insight.timestamp)}</span>
                        </RTLFlex>
                        <p className="text-sm text-gray-300 mb-2">{insight.description}</p>
                        <RTLFlex className="items-center justify-between">
                          <span className="text-xs text-gray-500">
                            Confidence: {insight.confidence}%
                          </span>
                          {insight.actionable && (
                            <button className="text-xs px-2 py-1 bg-purple-600/20 text-purple-400 rounded hover:bg-purple-600/30 transition-colors">
                              Apply Suggestion
                            </button>
                          )}
                        </RTLFlex>
                      </div>
                    </RTLFlex>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'video' && (
            <div className={`flex-1 p-4 ${isRTL ? "flex-row-reverse" : ""}`}>
              {videoCall ? (
                <div className="h-full bg-gray-800 rounded-lg relative">
                  <div className={`absolute inset-0 flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className="text-center">
                      <Video className="mx-auto mb-4 text-green-400" size={48} />
                      <h3 className="text-lg font-semibold text-white mb-2">t("common.video.call.active", "Video Call Active")</h3>
                      <p className="text-gray-400 mb-4">
                        {videoCall.participants.length} participants
                      </p>

                      {/* Video Controls */}
                      <RTLFlex className="items-center justify-center gap-4">
                        <button
                          onClick={() => setIsMuted(!isMuted)}
                          className={`p-3 rounded-full transition-colors ${
                            isMuted ? 'bg-red-600 hover:bg-red-700' : 'bg-gray-600 hover:bg-gray-700'}
                          }`}
                        >
                          {isMuted ? <MicOff className="text-white" size={20} /> : <Mic className="text-white" size={20} />}
                        </button>

                        <button
                          onClick={() => setIsVideoOff(!isVideoOff)}
                          className={`p-3 rounded-full transition-colors ${
                            isVideoOff ? 'bg-red-600 hover:bg-red-700' : 'bg-gray-600 hover:bg-gray-700'}
                          }`}
                        >
                          {isVideoOff ? <VideoOff className="text-white" size={20} /> : <Video className="text-white" size={20} />}
                        </button>

                        <button
                          onClick={() => setIsScreenSharing(!isScreenSharing)}
                          className={`p-3 rounded-full transition-colors ${
                            isScreenSharing ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-600 hover:bg-gray-700'}
                          }`}
                        >
                          <Monitor className="text-white" size={20} />
                        </button>

                        <button
                          onClick={endVideoCall}
                          className="p-3 bg-red-600 hover:bg-red-700 rounded-full transition-colors"
                        >
                          <PhoneOff className="text-white" size={20} />
                        </button>
                      </RTLFlex>
                    </div>
                  </div>
                </div>
              ) : (
                <div className={`h-full flex items-center justify-center ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className="text-center">
                    <Video className="mx-auto mb-4 text-gray-600" size={48} />
                    <h3 className="text-lg font-semibold text-gray-400 mb-2">t("common.no.active.call", "No Active Call")</h3>
                    <p className="text-gray-500 mb-4">t("common.start.a.video", "Start a video call with your team")</p>
                    <button
                      onClick={startVideoCall}
                      className="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                    >
                      Start Video Call
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'activity' && (
            <div className={`flex-1 p-4 overflow-y-auto ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className="space-y-3">
                <div className={`flex items-center gap-3 text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="text-gray-300">t("common.fatima.hassan.edited", "Fatima Hassan edited Market Analysis")</span>
                  <span className="text-gray-500">5 minutes ago</span>
                </div>
                <div className={`flex items-center gap-3 text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                  <span className="text-gray-300">t("common.omar.khalil.added", "Omar Khalil added a comment")</span>
                  <span className="text-gray-500">10 minutes ago</span>
                </div>
                <div className={`flex items-center gap-3 text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                  <span className="text-gray-300">t("common.ai.generated.new", "AI generated new insights")</span>
                  <span className="text-gray-500">15 minutes ago</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SmartCollaborationHub;
