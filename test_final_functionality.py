#!/usr/bin/env python
"""
Final Functionality Test - Verify all systems are working
"""
import requests
import json
import time

def test_frontend():
    """Test if frontend is accessible"""
    print("🔍 Testing Frontend...")
    try:
        response = requests.get('http://localhost:3000', timeout=10)
        if response.status_code == 200:
            print("✅ Frontend is accessible")
            return True
        else:
            print(f"❌ Frontend returned {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Frontend error: {e}")
        return False

def test_backend():
    """Test if backend is accessible"""
    print("\n🔍 Testing Backend...")
    try:
        response = requests.get('http://localhost:8000/api/', timeout=10)
        if response.status_code in [200, 401]:  # 401 is expected for protected endpoints
            print("✅ Backend API is accessible")
            return True
        else:
            print(f"❌ Backend returned {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend error: {e}")
        return False

def test_ai_service():
    """Test AI service status"""
    print("\n🔍 Testing AI Service...")
    try:
        response = requests.get('http://localhost:8000/api/ai/status/', timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('status', {}).get('available'):
                print("✅ AI Service is available and working")
                print(f"   Service: {data.get('status', {}).get('service', 'Unknown')}")
                print(f"   Model: {data.get('status', {}).get('model', 'Unknown')}")
                return True
            else:
                print("❌ AI Service is not available")
                print(f"   Status: {data}")
                return False
        else:
            print(f"❌ AI Status check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ AI Service error: {e}")
        return False

def test_key_pages():
    """Test key frontend pages"""
    print("\n🔍 Testing Key Pages...")
    
    pages = [
        ('Homepage', 'http://localhost:3000/'),
        ('Login', 'http://localhost:3000/login'),
        ('Register', 'http://localhost:3000/register'),
        ('Dashboard', 'http://localhost:3000/dashboard'),
        ('AI Features', 'http://localhost:3000/ai'),
        ('Business Ideas', 'http://localhost:3000/business-ideas'),
        ('Templates', 'http://localhost:3000/templates'),
    ]
    
    success_count = 0
    for name, url in pages:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {name} page loads successfully")
                success_count += 1
            else:
                print(f"❌ {name} page failed: {response.status_code}")
        except Exception as e:
            print(f"❌ {name} page error: {e}")
    
    print(f"\n📊 Page Test Results: {success_count}/{len(pages)} pages working")
    return success_count == len(pages)

def test_authentication():
    """Test authentication endpoints"""
    print("\n🔍 Testing Authentication...")
    
    # Test registration endpoint
    try:
        reg_data = {
            'username': 'testuser_' + str(int(time.time())),
            'email': '<EMAIL>',
            'password': 'testpass123',
            'first_name': 'Test',
            'last_name': 'User'
        }
        
        response = requests.post('http://localhost:8000/api/auth/register/', 
                               json=reg_data, timeout=10)
        
        if response.status_code in [200, 201, 400]:  # 400 for validation errors is OK
            print("✅ Registration endpoint is working")
        else:
            print(f"❌ Registration failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Registration test error: {e}")
    
    # Test login endpoint
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = requests.post('http://localhost:8000/api/auth/login/', 
                               json=login_data, timeout=10)
        
        if response.status_code in [200, 201, 400, 401]:  # Various responses are OK
            print("✅ Login endpoint is working")
        else:
            print(f"❌ Login failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Login test error: {e}")

def main():
    print("🧪 FINAL FUNCTIONALITY TEST")
    print("=" * 50)
    
    # Test all components
    frontend_ok = test_frontend()
    backend_ok = test_backend()
    ai_ok = test_ai_service()
    pages_ok = test_key_pages()
    
    # Test authentication
    test_authentication()
    
    print("\n" + "=" * 50)
    print("🎯 FINAL RESULTS:")
    print(f"Frontend: {'✅ WORKING' if frontend_ok else '❌ FAILED'}")
    print(f"Backend: {'✅ WORKING' if backend_ok else '❌ FAILED'}")
    print(f"AI Service: {'✅ WORKING' if ai_ok else '❌ FAILED'}")
    print(f"Key Pages: {'✅ WORKING' if pages_ok else '❌ SOME FAILED'}")
    
    if frontend_ok and backend_ok and ai_ok:
        print("\n🎉 SUCCESS: All critical systems are working!")
        print("✅ The application is ready for full testing")
        print("\n🚀 Next Steps:")
        print("1. Open http://localhost:3000 in your browser")
        print("2. Register a new account or login")
        print("3. Test AI features, business ideas, and templates")
        print("4. Verify all functionality works as expected")
    else:
        print("\n⚠️ Some systems need attention:")
        if not frontend_ok:
            print("- Frontend server needs to be started")
        if not backend_ok:
            print("- Backend server needs to be started")
        if not ai_ok:
            print("- AI service configuration needs review")

if __name__ == "__main__":
    main()
