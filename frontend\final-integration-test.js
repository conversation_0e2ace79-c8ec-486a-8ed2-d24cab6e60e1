// Final Integration Test Suite
// Comprehensive end-to-end testing of all core user workflows
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:8000';
const FRONTEND_URL = 'http://localhost:3000';

const TEST_USERS = {
  user: { username: 'testuser', password: 'testpass123', expectedRole: 'user' },
  mentor: { username: 'testmentor', password: 'testpass123', expectedRole: 'mentor' },
  investor: { username: 'testinvestor', password: 'testpass123', expectedRole: 'investor' },
  moderator: { username: 'testmoderator', password: 'testpass123', expectedRole: 'moderator' },
  admin: { username: 'testadmin', password: 'testpass123', expectedRole: 'admin' },
  superadmin: { username: 'testsuperadmin', password: 'testpass123', expectedRole: 'super_admin' }
};

let overallResults = {
  totalTests: 0,
  passedTests: 0,
  failedTests: 0,
  testSuites: [],
  startTime: new Date(),
  endTime: null
};

async function runTestSuite(suiteName, testFunction) {
  console.log(`\n🧪 Running ${suiteName}...`);
  console.log('='.repeat(50));
  
  const suiteStartTime = Date.now();
  let suiteResults = {
    name: suiteName,
    passed: 0,
    failed: 0,
    duration: 0,
    details: []
  };

  try {
    const results = await testFunction();
    suiteResults.passed = results.passed || 0;
    suiteResults.failed = results.failed || 0;
    suiteResults.details = results.details || [];
    
    overallResults.passedTests += suiteResults.passed;
    overallResults.failedTests += suiteResults.failed;
    
  } catch (error) {
    console.error(`❌ Test suite ${suiteName} failed: ${error.message}`);
    suiteResults.failed = 1;
    suiteResults.details.push({ error: error.message });
    overallResults.failedTests += 1;
  }

  suiteResults.duration = Date.now() - suiteStartTime;
  overallResults.testSuites.push(suiteResults);
  overallResults.totalTests += suiteResults.passed + suiteResults.failed;

  const successRate = suiteResults.passed + suiteResults.failed > 0 
    ? ((suiteResults.passed / (suiteResults.passed + suiteResults.failed)) * 100).toFixed(1)
    : '0.0';

  console.log(`\n📊 ${suiteName} Results:`);
  console.log(`   ✅ Passed: ${suiteResults.passed}`);
  console.log(`   ❌ Failed: ${suiteResults.failed}`);
  console.log(`   📈 Success Rate: ${successRate}%`);
  console.log(`   ⏱️  Duration: ${(suiteResults.duration / 1000).toFixed(2)}s`);
}

// Authentication Test Suite
async function authenticationTests() {
  const results = { passed: 0, failed: 0, details: [] };

  for (const [userType, userData] of Object.entries(TEST_USERS)) {
    try {
      const response = await fetch(`${BASE_URL}/api/auth/token/`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          username: userData.username,
          password: userData.password
        })
      });

      if (response.ok) {
        const data = await response.json();
        const userRole = data.user.user_role;
        
        if (userRole === userData.expectedRole) {
          results.passed++;
          results.details.push({
            userType,
            status: 'PASSED',
            role: userRole,
            message: 'Authentication successful'
          });
        } else {
          results.failed++;
          results.details.push({
            userType,
            status: 'FAILED',
            error: `Role mismatch: expected ${userData.expectedRole}, got ${userRole}`
          });
        }
      } else {
        results.failed++;
        results.details.push({
          userType,
          status: 'FAILED',
          error: `Login failed with status ${response.status}`
        });
      }
    } catch (error) {
      results.failed++;
      results.details.push({
        userType,
        status: 'FAILED',
        error: error.message
      });
    }
  }

  return results;
}

// Dashboard Access Test Suite
async function dashboardTests() {
  const results = { passed: 0, failed: 0, details: [] };

  const dashboardRoutes = {
    user: '/dashboard',
    mentor: '/dashboard/mentorship',
    investor: '/dashboard/investments',
    moderator: '/dashboard/moderation',
    admin: '/admin',
    superadmin: '/super_admin'
  };

  for (const [userType, userData] of Object.entries(TEST_USERS)) {
    try {
      // Login first
      const loginResponse = await fetch(`${BASE_URL}/api/auth/token/`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          username: userData.username,
          password: userData.password
        })
      });

      if (!loginResponse.ok) {
        throw new Error('Login failed');
      }

      const loginData = await loginResponse.json();
      const token = loginData.access;

      // Test dashboard access
      const dashboardRoute = dashboardRoutes[userType];
      const dashboardResponse = await fetch(`${FRONTEND_URL}${dashboardRoute}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
        }
      });

      if (dashboardResponse.ok || dashboardResponse.status === 200) {
        results.passed++;
        results.details.push({
          userType,
          status: 'PASSED',
          route: dashboardRoute,
          message: 'Dashboard accessible'
        });
      } else {
        results.failed++;
        results.details.push({
          userType,
          status: 'FAILED',
          route: dashboardRoute,
          error: `Dashboard returned status ${dashboardResponse.status}`
        });
      }

    } catch (error) {
      results.failed++;
      results.details.push({
        userType,
        status: 'FAILED',
        error: error.message
      });
    }
  }

  return results;
}

// Navigation Test Suite
async function navigationTests() {
  const results = { passed: 0, failed: 0, details: [] };

  const commonRoutes = ['/dashboard', '/profile', '/settings'];

  for (const [userType, userData] of Object.entries(TEST_USERS)) {
    try {
      // Login first
      const loginResponse = await fetch(`${BASE_URL}/api/auth/token/`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          username: userData.username,
          password: userData.password
        })
      });

      if (!loginResponse.ok) {
        throw new Error('Login failed');
      }

      const loginData = await loginResponse.json();
      const token = loginData.access;

      // Test common routes
      let accessibleRoutes = 0;
      for (const route of commonRoutes) {
        const response = await fetch(`${FRONTEND_URL}${route}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
          }
        });

        if (response.ok || response.status === 200) {
          accessibleRoutes++;
        }
      }

      if (accessibleRoutes === commonRoutes.length) {
        results.passed++;
        results.details.push({
          userType,
          status: 'PASSED',
          accessibleRoutes,
          totalRoutes: commonRoutes.length,
          message: 'Navigation working correctly'
        });
      } else {
        results.failed++;
        results.details.push({
          userType,
          status: 'FAILED',
          accessibleRoutes,
          totalRoutes: commonRoutes.length,
          error: 'Some navigation routes inaccessible'
        });
      }

    } catch (error) {
      results.failed++;
      results.details.push({
        userType,
        status: 'FAILED',
        error: error.message
      });
    }
  }

  return results;
}

// System Health Test Suite
async function systemHealthTests() {
  const results = { passed: 0, failed: 0, details: [] };

  try {
    // Test frontend server
    const frontendResponse = await fetch(`${FRONTEND_URL}/`, {
      method: 'GET',
      headers: { 'Accept': 'text/html' }
    });

    if (frontendResponse.ok) {
      results.passed++;
      results.details.push({
        component: 'Frontend Server',
        status: 'PASSED',
        message: 'Frontend server responding correctly'
      });
    } else {
      results.failed++;
      results.details.push({
        component: 'Frontend Server',
        status: 'FAILED',
        error: `Frontend server returned status ${frontendResponse.status}`
      });
    }

    // Test backend server health
    const backendResponse = await fetch(`${BASE_URL}/api/auth/token/`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: 'invalid', password: 'invalid' })
    });

    // We expect this to fail, but the server should respond
    if (backendResponse.status === 400 || backendResponse.status === 401) {
      results.passed++;
      results.details.push({
        component: 'Backend Server',
        status: 'PASSED',
        message: 'Backend server responding correctly'
      });
    } else {
      results.failed++;
      results.details.push({
        component: 'Backend Server',
        status: 'FAILED',
        error: `Unexpected backend response: ${backendResponse.status}`
      });
    }

  } catch (error) {
    results.failed++;
    results.details.push({
      component: 'System Health',
      status: 'FAILED',
      error: error.message
    });
  }

  return results;
}

async function runFinalIntegrationTests() {
  console.log('🚀 FINAL INTEGRATION TEST SUITE');
  console.log('🎯 Comprehensive End-to-End Testing');
  console.log('=' * 60);
  console.log(`📅 Started: ${overallResults.startTime.toLocaleString()}`);
  console.log(`🌐 Frontend: ${FRONTEND_URL}`);
  console.log(`🔧 Backend: ${BASE_URL}`);

  // Run all test suites
  await runTestSuite('System Health Tests', systemHealthTests);
  await runTestSuite('Authentication Tests', authenticationTests);
  await runTestSuite('Dashboard Access Tests', dashboardTests);
  await runTestSuite('Navigation Tests', navigationTests);

  // Calculate final results
  overallResults.endTime = new Date();
  const totalDuration = (overallResults.endTime - overallResults.startTime) / 1000;
  const overallSuccessRate = overallResults.totalTests > 0 
    ? ((overallResults.passedTests / overallResults.totalTests) * 100).toFixed(1)
    : '0.0';

  // Print comprehensive results
  console.log('\n🏆 FINAL INTEGRATION TEST RESULTS');
  console.log('=' * 60);
  console.log(`📊 Total Tests: ${overallResults.totalTests}`);
  console.log(`✅ Passed: ${overallResults.passedTests}`);
  console.log(`❌ Failed: ${overallResults.failedTests}`);
  console.log(`📈 Overall Success Rate: ${overallSuccessRate}%`);
  console.log(`⏱️  Total Duration: ${totalDuration.toFixed(2)}s`);
  console.log(`📅 Completed: ${overallResults.endTime.toLocaleString()}`);

  console.log('\n📋 Test Suite Summary:');
  overallResults.testSuites.forEach(suite => {
    const suiteSuccessRate = suite.passed + suite.failed > 0 
      ? ((suite.passed / (suite.passed + suite.failed)) * 100).toFixed(1)
      : '0.0';
    const status = suite.failed === 0 ? '✅' : '❌';
    console.log(`   ${status} ${suite.name}: ${suiteSuccessRate}% (${suite.passed}/${suite.passed + suite.failed})`);
  });

  console.log('\n🎯 FINAL ASSESSMENT:');
  if (overallResults.failedTests === 0) {
    console.log('🎉 ALL TESTS PASSED! The application is working excellently.');
    console.log('✅ Ready for production deployment with confidence.');
    console.log('🚀 All core user workflows are functioning correctly.');
  } else if (parseFloat(overallSuccessRate) >= 90) {
    console.log('✅ MOSTLY SUCCESSFUL! Minor issues detected but core functionality works.');
    console.log('⚠️  Review failed tests and address before production deployment.');
  } else {
    console.log('⚠️  SIGNIFICANT ISSUES DETECTED! Review and fix before proceeding.');
    console.log('🔧 Core functionality may be impaired.');
  }

  console.log('\n📄 Detailed test report available in: comprehensive-test-report.md');
  
  return overallResults;
}

// Run the comprehensive test suite
runFinalIntegrationTests().catch(console.error);
