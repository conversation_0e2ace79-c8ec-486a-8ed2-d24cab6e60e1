import { useEffect, useCallback, useRef } from 'react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { getCurrentUser, clearAuth } from '../store/authSlice';
import { authAPI, isTokenValid, getTokenExpiry, getAuthToken } from '../services/api';

/**
 * Session Manager Hook
 * Handles automatic token refresh, session restoration, and session monitoring
 */
export const useSessionManager = () => {
  const dispatch = useAppDispatch();
  const { isAuthenticated, user } = useAppSelector(state => state.auth);
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const sessionCheckIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Check if session should be restored on app start
  const restoreSession = useCallback(async () => {
    const token = getAuthToken();
    
    if (token && isTokenValid()) {
      try {
        // Token exists and is valid, restore user session
        await dispatch(getCurrentUser()).unwrap();
        console.log('✅ Session restored successfully');
      } catch (error) {
        console.log('❌ Session restoration failed:', error);
        // Clear invalid session
        dispatch(clearAuth());
      }
    } else if (token && !isTokenValid()) {
      // Token exists but is expired, try to refresh
      console.log('🔄 Token expired, attempting refresh...');
      const refreshSuccess = await authAPI.refreshToken();
      
      if (refreshSuccess) {
        try {
          await dispatch(getCurrentUser()).unwrap();
          console.log('✅ Session refreshed and restored');
        } catch (error) {
          console.log('❌ Session restoration after refresh failed:', error);
          dispatch(clearAuth());
        }
      } else {
        console.log('❌ Token refresh failed, clearing session');
        dispatch(clearAuth());
      }
    }
  }, [dispatch]);

  // Setup automatic token refresh
  const setupTokenRefresh = useCallback(() => {
    if (refreshIntervalRef.current) {
      clearInterval(refreshIntervalRef.current);
    }

    const token = getAuthToken();
    if (!token || !isAuthenticated) return;

    const expiry = getTokenExpiry();
    if (!expiry) return;

    // Refresh token 5 minutes before expiry
    const refreshTime = expiry - Date.now() - (5 * 60 * 1000);
    
    if (refreshTime > 0) {
      refreshIntervalRef.current = setTimeout(async () => {
        console.log('🔄 Auto-refreshing token...');
        const success = await authAPI.refreshToken();
        
        if (success) {
          console.log('✅ Token auto-refreshed successfully');
          // Setup next refresh
          setupTokenRefresh();
        } else {
          console.log('❌ Auto token refresh failed, logging out');
          dispatch(clearAuth());
        }
      }, refreshTime);
    }
  }, [isAuthenticated, dispatch]);

  // Monitor session validity
  const setupSessionMonitoring = useCallback(() => {
    if (sessionCheckIntervalRef.current) {
      clearInterval(sessionCheckIntervalRef.current);
    }

    if (!isAuthenticated) return;

    // Check session validity every 30 seconds
    sessionCheckIntervalRef.current = setInterval(() => {
      const token = getAuthToken();
      
      if (!token || !isTokenValid()) {
        console.log('⚠️ Session invalid detected, attempting refresh...');
        authAPI.refreshToken().then(success => {
          if (!success) {
            console.log('❌ Session monitoring: refresh failed, logging out');
            dispatch(clearAuth());
          }
        });
      }
    }, 30000); // Check every 30 seconds
  }, [isAuthenticated, dispatch]);

  // Initialize session management
  useEffect(() => {
    // Only restore session if not already authenticated
    if (!isAuthenticated && !user) {
      restoreSession();
    }
  }, []); // Run only once on mount

  // Setup token refresh and monitoring when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      setupTokenRefresh();
      setupSessionMonitoring();
    } else {
      // Clear intervals when not authenticated
      if (refreshIntervalRef.current) {
        clearTimeout(refreshIntervalRef.current);
        refreshIntervalRef.current = null;
      }
      if (sessionCheckIntervalRef.current) {
        clearInterval(sessionCheckIntervalRef.current);
        sessionCheckIntervalRef.current = null;
      }
    }

    // Cleanup on unmount
    return () => {
      if (refreshIntervalRef.current) {
        clearTimeout(refreshIntervalRef.current);
      }
      if (sessionCheckIntervalRef.current) {
        clearInterval(sessionCheckIntervalRef.current);
      }
    };
  }, [isAuthenticated, setupTokenRefresh, setupSessionMonitoring]);

  // Manual session refresh function
  const refreshSession = useCallback(async () => {
    try {
      const success = await authAPI.refreshToken();
      if (success) {
        await dispatch(getCurrentUser()).unwrap();
        setupTokenRefresh(); // Reset refresh timer
        return true;
      }
      return false;
    } catch (error) {
      console.error('Manual session refresh failed:', error);
      return false;
    }
  }, [dispatch, setupTokenRefresh]);

  return {
    isAuthenticated,
    user,
    refreshSession,
    restoreSession,
  };
};
