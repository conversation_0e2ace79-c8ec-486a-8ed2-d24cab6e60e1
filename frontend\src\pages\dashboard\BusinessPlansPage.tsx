/**
 * Business Plans Page
 * Main page for managing business plans
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Plus, FileText, Edit, Trash2, Eye, Download, Share2 } from 'lucide-react';
import { useAppSelector } from '../../store/hooks';

interface BusinessPlan {
  id: number;
  title: string;
  description: string;
  status: 'draft' | 'in_progress' | 'completed' | 'published';
  created_at: string;
  updated_at: string;
  template_used?: string;
  progress: number;
}

const BusinessPlansPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAppSelector(state => state.auth);
  const [businessPlans, setBusinessPlans] = useState<BusinessPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchBusinessPlans();
  }, []);

  const fetchBusinessPlans = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      // const response = await fetch('/api/business-plans/', {
      //   headers: {
      //     'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      //   }
      // });
      // const data = await response.json();
      
      // Mock data for now
      const mockPlans: BusinessPlan[] = [
        {
          id: 1,
          title: 'Tech Startup Business Plan',
          description: 'Comprehensive business plan for a technology startup',
          status: 'in_progress',
          created_at: '2024-01-15',
          updated_at: '2024-01-20',
          template_used: 'Tech Startup Template',
          progress: 65
        },
        {
          id: 2,
          title: 'E-commerce Platform Plan',
          description: 'Business plan for online retail platform',
          status: 'draft',
          created_at: '2024-01-10',
          updated_at: '2024-01-18',
          template_used: 'E-commerce Template',
          progress: 30
        }
      ];
      
      setBusinessPlans(mockPlans);
    } catch (err) {
      setError('Failed to fetch business plans');
      console.error('Error fetching business plans:', err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'published': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleCreateNew = () => {
    // TODO: Navigate to business plan creation page
    console.log('Create new business plan');
  };

  const handleEdit = (planId: number) => {
    // TODO: Navigate to business plan edit page
    console.log('Edit business plan:', planId);
  };

  const handleView = (planId: number) => {
    // TODO: Navigate to business plan view page
    console.log('View business plan:', planId);
  };

  const handleDelete = (planId: number) => {
    // TODO: Implement delete functionality
    console.log('Delete business plan:', planId);
  };

  const handleDownload = (planId: number) => {
    // TODO: Implement download functionality
    console.log('Download business plan:', planId);
  };

  const handleShare = (planId: number) => {
    // TODO: Implement share functionality
    console.log('Share business plan:', planId);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3].map(i => (
                <div key={i} className="bg-white rounded-lg shadow p-6">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                  <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-red-900 mb-2">Error</h2>
            <p className="text-red-700">{error}</p>
            <button
              onClick={fetchBusinessPlans}
              className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {t('businessPlans.title', 'Business Plans')}
            </h1>
            <p className="text-gray-600 mt-2">
              {t('businessPlans.subtitle', 'Create and manage your business plans')}
            </p>
          </div>
          <button
            onClick={handleCreateNew}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="w-5 h-5 mr-2" />
            {t('businessPlans.createNew', 'Create New Plan')}
          </button>
        </div>

        {/* Business Plans Grid */}
        {businessPlans.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {t('businessPlans.noPlans', 'No Business Plans Yet')}
            </h3>
            <p className="text-gray-600 mb-6">
              {t('businessPlans.getStarted', 'Get started by creating your first business plan')}
            </p>
            <button
              onClick={handleCreateNew}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              {t('businessPlans.createFirst', 'Create Your First Plan')}
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {businessPlans.map((plan) => (
              <div key={plan.id} className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow">
                <div className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 truncate">
                      {plan.title}
                    </h3>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(plan.status)}`}>
                      {t(`businessPlans.status.${plan.status}`, plan.status)}
                    </span>
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {plan.description}
                  </p>
                  
                  {plan.template_used && (
                    <p className="text-xs text-blue-600 mb-3">
                      {t('businessPlans.basedOn', 'Based on')}: {plan.template_used}
                    </p>
                  )}
                  
                  {/* Progress Bar */}
                  <div className="mb-4">
                    <div className="flex justify-between text-xs text-gray-600 mb-1">
                      <span>{t('businessPlans.progress', 'Progress')}</span>
                      <span>{plan.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${plan.progress}%` }}
                      ></div>
                    </div>
                  </div>
                  
                  <div className="text-xs text-gray-500 mb-4">
                    {t('businessPlans.lastUpdated', 'Last updated')}: {new Date(plan.updated_at).toLocaleDateString()}
                  </div>
                  
                  {/* Actions */}
                  <div className="flex justify-between items-center">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleView(plan.id)}
                        className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                        title={t('businessPlans.view', 'View')}
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleEdit(plan.id)}
                        className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded transition-colors"
                        title={t('businessPlans.edit', 'Edit')}
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDownload(plan.id)}
                        className="p-2 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded transition-colors"
                        title={t('businessPlans.download', 'Download')}
                      >
                        <Download className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleShare(plan.id)}
                        className="p-2 text-gray-600 hover:text-indigo-600 hover:bg-indigo-50 rounded transition-colors"
                        title={t('businessPlans.share', 'Share')}
                      >
                        <Share2 className="w-4 h-4" />
                      </button>
                    </div>
                    <button
                      onClick={() => handleDelete(plan.id)}
                      className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                      title={t('businessPlans.delete', 'Delete')}
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default BusinessPlansPage;
