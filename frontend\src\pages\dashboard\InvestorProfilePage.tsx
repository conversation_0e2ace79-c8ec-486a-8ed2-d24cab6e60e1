import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import Button from '../../components/ui/Button';
import { Badge } from '../../components/ui/badge';
import { Input } from '../../components/ui/input';
import { Textarea } from '../../components/ui/textarea';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { User, Building, DollarSign, Target, Award, Edit, Save, X } from 'lucide-react';
import { useInvestorProfile } from '../../hooks/useInvestment';

interface InvestorProfile {
  personalInfo: {
    name: string;
    title: string;
    company: string;
    email: string;
    phone: string;
    location: string;
    bio: string;
    profileImage?: string;
  };
  investmentPreferences: {
    sectors: string[];
    stages: string[];
    geographies: string[];
    minInvestment: number;
    maxInvestment: number;
    investmentHorizon: string;
    riskTolerance: 'Conservative' | 'Moderate' | 'Aggressive';
  };
  experience: {
    yearsInvesting: number;
    totalInvestments: number;
    successfulExits: number;
    portfolioValue: number;
    notableInvestments: string[];
    expertise: string[];
  };
  credentials: {
    education: string[];
    certifications: string[];
    boardPositions: string[];
    previousRoles: string[];
  };
}

const InvestorProfilePage: React.FC = () => {
  // Use real investor profile hook
  const {
    profile,
    isLoading: loading,
    error,
    updateProfile,
    uploadImage
  } = useInvestorProfile();

  const [editing, setEditing] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // Investor profile data is now fetched by the hook

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const handleSave = () => {
    // Save profile changes
    setEditing(false);
    // API call would go here
  };

  const handleCancel = () => {
    setEditing(false);
    // Reset changes
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!profile) return null;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Investor Profile</h1>
          <p className="text-gray-600 mt-1">Manage your investor profile and preferences</p>
        </div>
        <div className="flex gap-2">
          {editing ? (
            <>
              <Button variant="outline" onClick={handleCancel}>
                <X className="w-4 h-4 mr-2" />
                Cancel
              </Button>
              <Button onClick={handleSave} className="bg-green-600 hover:bg-green-700">
                <Save className="w-4 h-4 mr-2" />
                Save Changes
              </Button>
            </>
          ) : (
            <Button onClick={() => setEditing(true)} className="bg-blue-600 hover:bg-blue-700">
              <Edit className="w-4 h-4 mr-2" />
              Edit Profile
            </Button>
          )}
        </div>
      </div>

      {/* Profile Header Card */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-start gap-6">
            <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center">
              <User className="w-12 h-12 text-gray-400" />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h2 className="text-2xl font-bold">{profile.personalInfo.name}</h2>
                <Badge className="bg-blue-100 text-blue-800">Verified Investor</Badge>
              </div>
              <p className="text-lg text-gray-600 mb-1">{profile.personalInfo.title}</p>
              <p className="text-gray-600 mb-3">{profile.personalInfo.company}</p>
              <p className="text-gray-700">{profile.personalInfo.bio}</p>
            </div>
            <div className="text-right">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-600">Total Investments</p>
                  <p className="text-xl font-bold">{profile.experience.totalInvestments}</p>
                </div>
                <div>
                  <p className="text-gray-600">Portfolio Value</p>
                  <p className="text-xl font-bold">{formatCurrency(profile.experience.portfolioValue)}</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Profile Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="preferences">Investment Preferences</TabsTrigger>
          <TabsTrigger value="experience">Experience</TabsTrigger>
          <TabsTrigger value="credentials">Credentials</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Personal Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5" />
                  Personal Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {editing ? (
                  <>
                    <div>
                      <label className="block text-sm font-medium mb-1">Name</label>
                      <Input value={profile.personalInfo.name} />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Title</label>
                      <Input value={profile.personalInfo.title} />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Company</label>
                      <Input value={profile.personalInfo.company} />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Email</label>
                      <Input value={profile.personalInfo.email} />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Phone</label>
                      <Input value={profile.personalInfo.phone} />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Location</label>
                      <Input value={profile.personalInfo.location} />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Bio</label>
                      <Textarea value={profile.personalInfo.bio} rows={3} />
                    </div>
                  </>
                ) : (
                  <>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Email:</span>
                      <span>{profile.personalInfo.email}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Phone:</span>
                      <span>{profile.personalInfo.phone}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Location:</span>
                      <span>{profile.personalInfo.location}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Company:</span>
                      <span>{profile.personalInfo.company}</span>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Investment Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="w-5 h-5" />
                  Investment Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Years Investing:</span>
                  <span className="font-semibold">{profile.experience.yearsInvesting} years</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Investments:</span>
                  <span className="font-semibold">{profile.experience.totalInvestments}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Successful Exits:</span>
                  <span className="font-semibold">{profile.experience.successfulExits}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Portfolio Value:</span>
                  <span className="font-semibold">{formatCurrency(profile.experience.portfolioValue)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Success Rate:</span>
                  <span className="font-semibold">
                    {Math.round((profile.experience.successfulExits / profile.experience.totalInvestments) * 100)}%
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Areas of Expertise */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5" />
                Areas of Expertise
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {profile.experience.expertise.map((area, index) => (
                  <Badge key={index} variant="secondary">
                    {area}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preferences" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Investment Criteria</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Investment Range</label>
                  <div className="flex items-center gap-2">
                    <span>{formatCurrency(profile.investmentPreferences.minInvestment)}</span>
                    <span>-</span>
                    <span>{formatCurrency(profile.investmentPreferences.maxInvestment)}</span>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Investment Horizon</label>
                  <span>{profile.investmentPreferences.investmentHorizon}</span>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Risk Tolerance</label>
                  <Badge className={
                    profile.investmentPreferences.riskTolerance === 'Conservative' ? 'bg-green-100 text-green-800' :
                    profile.investmentPreferences.riskTolerance === 'Moderate' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }>
                    {profile.investmentPreferences.riskTolerance}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Preferred Sectors</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {profile.investmentPreferences.sectors.map((sector, index) => (
                    <Badge key={index} variant="outline">
                      {sector}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Investment Stages</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {profile.investmentPreferences.stages.map((stage, index) => (
                    <Badge key={index} variant="outline">
                      {stage}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Geographic Focus</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {profile.investmentPreferences.geographies.map((geo, index) => (
                    <Badge key={index} variant="outline">
                      {geo}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="experience" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Notable Investments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {profile.experience.notableInvestments.map((investment, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded">
                    <span className="font-medium">{investment}</span>
                    <Badge className="bg-green-100 text-green-800">Active</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Investment Track Record</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-600">{profile.experience.totalInvestments}</p>
                  <p className="text-sm text-gray-600">Total Investments</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">{profile.experience.successfulExits}</p>
                  <p className="text-sm text-gray-600">Successful Exits</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-purple-600">{profile.experience.yearsInvesting}</p>
                  <p className="text-sm text-gray-600">Years Experience</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-orange-600">
                    {Math.round((profile.experience.successfulExits / profile.experience.totalInvestments) * 100)}%
                  </p>
                  <p className="text-sm text-gray-600">Success Rate</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="credentials" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="w-5 h-5" />
                  Education
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {profile.credentials.education.map((edu, index) => (
                    <div key={index} className="p-2 border rounded">
                      {edu}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Certifications</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {profile.credentials.certifications.map((cert, index) => (
                    <div key={index} className="p-2 border rounded">
                      {cert}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="w-5 h-5" />
                  Board Positions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {profile.credentials.boardPositions.map((position, index) => (
                    <div key={index} className="p-2 border rounded">
                      {position}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Previous Roles</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {profile.credentials.previousRoles.map((role, index) => (
                    <div key={index} className="p-2 border rounded">
                      {role}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default InvestorProfilePage;
