import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { UserPlus } from 'lucide-react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { useTranslation } from 'react-i18next';
import { RTLIcon, RTLText, RTLFlex } from '../components/common';
import { register, clearError } from '../store/authSlice';

import { useLanguage } from '../hooks/useLanguage';
const RegisterPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { isAuthenticated, isLoading, error } = useAppSelector(state => state.auth);
  const { language, isRTL } = useLanguage();

  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    password_confirm: '',
    first_name: '',
    last_name: '',
  });
  const [formError, setFormError] = useState<string | null>(null);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      // Regular users are directed to the user dashboard
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    dispatch(clearError());
    setFormError(null);

    // Validate form
    if (!formData.username.trim()) {
      setFormError(t('auth.usernameRequired'));
      return;
    }
    if (!formData.email.trim()) {
      setFormError(t('auth.emailRequired'));
      return;
    }
    if (!formData.password) {
      setFormError(t('auth.passwordRequired'));
      return;
    }
    if (formData.password !== formData.password_confirm) {
      setFormError(t('auth.passwordsDoNotMatch'));
      return;
    }

    try {
      // Create a copy of the form data to ensure we're sending the right format
      const userData = {
        username: formData.username.trim(),
        email: formData.email.trim(),
        password: formData.password,
        password_confirm: formData.password_confirm,
        first_name: formData.first_name?.trim() || '',
        last_name: formData.last_name?.trim() || ''
      };

      if (process.env.NODE_ENV === 'development') {
        console.log("Submitting registration data:", { ...userData, password: '***', password_confirm: '***' });
      }

      const resultAction = await dispatch(register(userData));
      if (register.fulfilled.match(resultAction)) {
        if (process.env.NODE_ENV === 'development') {
          console.log("Registration successful, redirecting to dashboard");
        }
        // New users are always regular users, so direct them to the user dashboard
        navigate('/dashboard');
      } else if (register.rejected.match(resultAction)) {
        // If we get here, there was an error but it was handled by the thunk
        if (process.env.NODE_ENV === 'development') {
          console.error("Registration rejected:", resultAction.payload);
        }
        setFormError(resultAction.payload as string || t("common.registration.failed", "Registration failed"));
      }
    } catch (err) {
      console.error("Registration failed:", err);
      setFormError(err instanceof Error ? err.message : t("common.registration.failed", "Registration failed"));
    }
  };

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center p-4 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className="max-w-md w-full">
        <div className="text-center mb-8">
          <Link to="/" className="inline-block">
            <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-400">
              {t('app.name')}
            </h1>
          </Link>
          <RTLText as="div" align="center" className="text-gray-300 mt-2">
            {t('auth.createNewAccount')}
          </RTLText>
        </div>

        <div className="bg-black/30 backdrop-blur-sm rounded-lg p-8 shadow-lg border border-white/20">
          <div className="text-center mb-6">
            <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-purple-600/20 mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
              <RTLIcon icon={UserPlus} size={28} className="text-purple-400" />
            </div>
            <h2 className="text-2xl font-bold text-white">{t('auth.createAccount')}</h2>
            <RTLText as="div" align="center" className="text-gray-300 mt-1">
              {t('auth.joinCommunity')}
            </RTLText>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4" data-testid="register-form">
            {(error || formError) && (
              <div className="bg-red-500/20 text-red-300 p-3 rounded-lg text-sm">
                {formError || error}
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div>
                <RTLText as="label" htmlFor="first_name" className="block text-sm font-medium text-gray-300 mb-1">
                  {t('auth.firstName')}
                </RTLText>
                <input
                  id="first_name"
                  name="first_name"
                  type="text"
                  value={formData.first_name}
                  onChange={handleChange}
                  className="w-full px-4 py-2 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                  placeholder={t('auth.enterFirstName')}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                  data-testid="first-name-input"
                />
              </div>
              <div>
                <RTLText as="label" htmlFor="last_name" className="block text-sm font-medium text-gray-300 mb-1">
                  {t('auth.lastName')}
                </RTLText>
                <input
                  id="last_name"
                  name="last_name"
                  type="text"
                  value={formData.last_name}
                  onChange={handleChange}
                  className="w-full px-4 py-2 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                  placeholder={t('auth.enterLastName')}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                  data-testid="last-name-input"
                />
              </div>
            </div>

            <div>
              <RTLText as="label" htmlFor="username" className="block text-sm font-medium text-gray-300 mb-1">
                {t('auth.username')}*
              </RTLText>
              <input
                id="username"
                name="username"
                type="text"
                value={formData.username}
                onChange={handleChange}
                className="w-full px-4 py-2 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                placeholder={t('auth.chooseUsername')}
                required
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
            </div>

            <div>
              <RTLText as="label" htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1">
                {t('auth.email')}*
              </RTLText>
              <input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                className="w-full px-4 py-2 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                placeholder={t('auth.enterEmail')}
                required
                dir={language === 'ar' ? 'rtl' : 'ltr'}
                data-testid="email-input"
              />
            </div>

            <div>
              <RTLText as="label" htmlFor="password" className="block text-sm font-medium text-gray-300 mb-1">
                {t('auth.password')}*
              </RTLText>
              <input
                id="password"
                name="password"
                type="password"
                value={formData.password}
                onChange={handleChange}
                className="w-full px-4 py-2 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                placeholder={t('auth.createPassword')}
                required
                dir={language === 'ar' ? 'rtl' : 'ltr'}
                data-testid="password-input"
              />
            </div>

            <div>
              <RTLText as="label" htmlFor="password_confirm" className="block text-sm font-medium text-gray-300 mb-1">
                {t('auth.confirmPassword')}*
              </RTLText>
              <input
                id="password_confirm"
                name="password_confirm"
                type="password"
                value={formData.password_confirm}
                onChange={handleChange}
                className="w-full px-4 py-2 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                placeholder={t('auth.confirmYourPassword')}
                required
                dir={language === 'ar' ? 'rtl' : 'ltr'}
                data-testid="confirm-password-input"
              />
            </div>

            <div className="pt-2">
              <button
                type="submit"
                disabled={isLoading}
                className="w-full px-4 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center gap-2"
                data-testid="register-button"
              >
                {isLoading ? (
                  <span className={`inline-block w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin ${language === 'ar' ? 'ml-2' : 'mr-2'}`}></span>
                ) : (
                  <RTLIcon icon={UserPlus} size={18} className={language === 'ar' ? 'ml-2' : 'mr-2'} />
                )}
                {isLoading ? t('auth.creatingAccount') : t('auth.createAccount')}
              </button>
            </div>
          </form>

          <div className="mt-6 text-center">
            <RTLText as="p" align="center" className="text-gray-300">
              {t('auth.haveAccount')}{' '}
              <Link to="/login" className="text-purple-400 hover:text-purple-300 transition-colors">
                {t('auth.signIn')}
              </Link>
            </RTLText>
            <Link to="/" className="text-sm text-purple-400 hover:text-purple-300 block mt-4">
              {t('common.returnToHomepage')}
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage;
