import { configureStore } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { combineReducers } from '@reduxjs/toolkit';

// Import only essential slices first
import authReducer from './authSlice';
import languageReducer, { initializeLanguage } from './languageSlice';
import toastReducer from './toastSlice';

// Import other essential slices
import eventsReducer from './eventsSlice';
import adminReducer from './adminSlice';
import incubatorReducer from './incubatorSlice';
import forumReducer from './forumSlice';
import aiReducer from './aiSlice';
import businessPlansReducer from './businessPlansSlice';
import aiContextReducer from './aiContextSlice';
import dashboardReducer from './dashboardSlice';
import uiReducer from './uiSlice';
// import { aiContextMiddleware } from '../middleware/aiContextMiddleware';

// Configure persistence for auth state
const authPersistConfig = {
  key: 'auth',
  storage,
  whitelist: ['user', 'isAuthenticated'], // Only persist user and auth status
};

// Configure persistence for language state
const languagePersistConfig = {
  key: 'language',
  storage,
};

// Combine all reducers
const rootReducer = combineReducers({
  auth: persistReducer(authPersistConfig, authReducer),
  events: eventsReducer,
  admin: adminReducer,
  language: persistReducer(languagePersistConfig, languageReducer),
  incubator: incubatorReducer,
  forum: forumReducer,
  ai: aiReducer,
  businessPlans: businessPlansReducer,
  aiContext: aiContextReducer,
  dashboard: dashboardReducer,
  toast: toastReducer,
  ui: uiReducer,
});

export const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }), // .concat(aiContextMiddleware), // TODO: Fix imports and re-enable
});

export const persistor = persistStore(store);

// Simple store - just Redux

// Initialize language after i18n is loaded
// We'll do this with a slight delay to ensure i18n is fully initialized
setTimeout(() => {
  initializeLanguage();
}, 100);

// Note: fetchLanguage() will be called after user authentication is established
// This prevents "Authentication credentials were not provided" errors

// Define RootState and AppDispatch types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
