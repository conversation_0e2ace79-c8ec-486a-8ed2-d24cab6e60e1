/**
 * Reports Hook
 * Custom hook for reports management
 */

import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { reportApi, Report, ReportFilters, ReportStats } from '../services/reportsApi';

/**
 * Hook for reports management
 */
export function useReports(filters?: ReportFilters) {
  const queryClient = useQueryClient();

  // Fetch reports
  const {
    data: reports = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['reports', filters],
    queryFn: () => reportApi.reports.getReports(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Create report mutation
  const createReportMutation = useMutation({
    mutationFn: (reportData: {
      type: Report['type'];
      reportedItemId: string;
      reportedUserId: string;
      reason: string;
      category: Report['category'];
      description: string;
      evidence?: Array<{
        type: 'screenshot' | 'link' | 'text';
        content: string;
      }>;
    }) => reportApi.reports.createReport(reportData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reports'] });
    },
  });

  // Update report status mutation
  const updateStatusMutation = useMutation({
    mutationFn: ({ id, status, resolution }: { 
      id: string; 
      status: Report['status']; 
      resolution?: string 
    }) => reportApi.reports.updateReportStatus(id, status, resolution),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reports'] });
    },
  });

  // Update report priority mutation
  const updatePriorityMutation = useMutation({
    mutationFn: ({ id, priority }: { id: string; priority: Report['priority'] }) =>
      reportApi.reports.updateReportPriority(id, priority),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reports'] });
    },
  });

  // Assign report mutation
  const assignReportMutation = useMutation({
    mutationFn: ({ id, moderatorId }: { id: string; moderatorId: string }) =>
      reportApi.reports.assignReport(id, moderatorId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reports'] });
    },
  });

  // Add evidence mutation
  const addEvidenceMutation = useMutation({
    mutationFn: ({ id, evidence }: { 
      id: string; 
      evidence: {
        type: 'screenshot' | 'link' | 'text';
        content: string;
      }
    }) => reportApi.reports.addEvidence(id, evidence),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reports'] });
    },
  });

  // Delete report mutation
  const deleteReportMutation = useMutation({
    mutationFn: (id: string) => reportApi.reports.deleteReport(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reports'] });
    },
  });

  return {
    reports,
    isLoading,
    error,
    refetch,
    createReport: createReportMutation.mutate,
    updateStatus: updateStatusMutation.mutate,
    updatePriority: updatePriorityMutation.mutate,
    assignReport: assignReportMutation.mutate,
    addEvidence: addEvidenceMutation.mutate,
    deleteReport: deleteReportMutation.mutate,
    isCreating: createReportMutation.isPending,
    isUpdating: updateStatusMutation.isPending || updatePriorityMutation.isPending,
    isAssigning: assignReportMutation.isPending,
    isAddingEvidence: addEvidenceMutation.isPending,
    isDeleting: deleteReportMutation.isPending,
  };
}

/**
 * Hook for single report
 */
export function useReport(id: string) {
  const {
    data: report,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['report', id],
    queryFn: () => reportApi.reports.getReport(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  return {
    report,
    isLoading,
    error,
    refetch,
  };
}

/**
 * Hook for report statistics
 */
export function useReportStats(timeRange?: 'week' | 'month' | 'quarter' | 'year') {
  const {
    data: stats,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['report-stats', timeRange],
    queryFn: () => reportApi.stats.getStats(timeRange),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  return {
    stats,
    isLoading,
    error,
    refetch,
  };
}

/**
 * Hook for trending reports
 */
export function useTrendingReports() {
  const {
    data: trendingReports = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['trending-reports'],
    queryFn: reportApi.stats.getTrendingReports,
    staleTime: 15 * 60 * 1000, // 15 minutes
  });

  return {
    trendingReports,
    isLoading,
    error,
    refetch,
  };
}

/**
 * Hook for report categories
 */
export function useReportCategories() {
  const {
    data: categories = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['report-categories'],
    queryFn: reportApi.categories.getCategories,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });

  return {
    categories,
    isLoading,
    error,
    refetch,
  };
}

/**
 * Hook for report filters
 */
export function useReportFilters() {
  const [filters, setFilters] = useState<ReportFilters>({
    status: '',
    category: '',
    priority: '',
    type: '',
    dateFrom: '',
    dateTo: '',
    reportedBy: '',
    reportedUser: '',
  });

  const updateFilter = (key: keyof ReportFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      status: '',
      category: '',
      priority: '',
      type: '',
      dateFrom: '',
      dateTo: '',
      reportedBy: '',
      reportedUser: '',
    });
  };

  const hasActiveFilters = Object.values(filters).some(value => value !== '');

  return {
    filters,
    updateFilter,
    clearFilters,
    hasActiveFilters,
  };
}

/**
 * Hook for report analytics
 */
export function useReportAnalytics() {
  const {
    data: analytics,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['report-analytics'],
    queryFn: reportApi.stats.getAnalytics,
    staleTime: 15 * 60 * 1000, // 15 minutes
  });

  return {
    analytics,
    isLoading,
    error,
    refetch,
  };
}

export default useReports;
