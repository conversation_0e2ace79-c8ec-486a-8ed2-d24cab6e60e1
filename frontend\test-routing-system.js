// Router and URL Testing Script
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:8000';
const FRONTEND_URL = 'http://localhost:3000';

const TEST_USERS = {
  user: { username: 'testuser', password: 'testpass123', expectedRole: 'user' },
  mentor: { username: 'testmentor', password: 'testpass123', expectedRole: 'mentor' },
  investor: { username: 'testinvestor', password: 'testpass123', expectedRole: 'investor' },
  moderator: { username: 'testmoderator', password: 'testpass123', expectedRole: 'moderator' },
  admin: { username: 'testadmin', password: 'testpass123', expectedRole: 'admin' },
  superadmin: { username: 'testsuperadmin', password: 'testpass123', expectedRole: 'super_admin' }
};

// Define routes to test for each role
const ROLE_ROUTES = {
  user: [
    '/dashboard',
    '/dashboard/business-ideas',
    '/dashboard/business-plans',
    '/dashboard/incubator',
    '/dashboard/forums',
    '/dashboard/resources',
    '/dashboard/ai',
    '/profile',
    '/settings'
  ],
  mentor: [
    '/dashboard',
    '/dashboard/mentorship',
    '/dashboard/mentorship/mentees',
    '/dashboard/business-ideas',
    '/dashboard/business-plans',
    '/dashboard/incubator',
    '/dashboard/forums',
    '/dashboard/resources',
    '/dashboard/ai',
    '/profile',
    '/settings'
  ],
  investor: [
    '/dashboard',
    '/dashboard/investments',
    '/dashboard/investments/opportunities',
    '/dashboard/business-ideas',
    '/dashboard/business-plans',
    '/dashboard/incubator',
    '/dashboard/forums',
    '/dashboard/resources',
    '/dashboard/ai',
    '/profile',
    '/settings'
  ],
  moderator: [
    '/dashboard',
    '/dashboard/moderation',
    '/dashboard/moderation/reports',
    '/dashboard/forums',
    '/dashboard/resources',
    '/dashboard/ai',
    '/profile',
    '/settings'
  ],
  admin: [
    '/dashboard',
    '/admin',
    '/admin/dashboard',
    '/admin/users',
    '/admin/content',
    '/dashboard/analytics',
    '/dashboard/forums',
    '/dashboard/resources',
    '/dashboard/ai',
    '/profile',
    '/settings'
  ],
  superadmin: [
    '/dashboard',
    '/super_admin',
    '/admin',
    '/admin/dashboard',
    '/admin/users',
    '/admin/content',
    '/super_admin/system',
    '/super_admin/security',
    '/dashboard/analytics',
    '/dashboard/forums',
    '/dashboard/resources',
    '/dashboard/ai',
    '/profile',
    '/settings'
  ]
};

let testResults = {
  passed: 0,
  failed: 0,
  details: []
};

async function loginUser(userType, userData) {
  try {
    const response = await fetch(`${BASE_URL}/api/auth/token/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: userData.username,
        password: userData.password
      })
    });

    if (!response.ok) {
      throw new Error(`Login failed with status ${response.status}`);
    }

    const data = await response.json();
    return {
      token: data.access,
      user: data.user
    };
  } catch (error) {
    console.error(`❌ Login failed for ${userType}: ${error.message}`);
    return null;
  }
}

async function testRouteAccess(route, token, userType) {
  try {
    const response = await fetch(`${FRONTEND_URL}${route}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
      },
      redirect: 'manual' // Don't follow redirects automatically
    });

    return {
      route,
      status: response.status,
      ok: response.ok,
      redirected: response.status >= 300 && response.status < 400,
      location: response.headers.get('location'),
      userType
    };
  } catch (error) {
    return {
      route,
      status: 0,
      ok: false,
      error: error.message,
      userType
    };
  }
}

async function testUnauthorizedRedirects() {
  console.log('\n🧪 Testing unauthorized access redirects...');
  
  const protectedRoutes = [
    '/dashboard',
    '/admin',
    '/super_admin',
    '/dashboard/business-ideas',
    '/profile',
    '/settings'
  ];

  const redirectResults = [];

  for (const route of protectedRoutes) {
    try {
      const response = await fetch(`${FRONTEND_URL}${route}`, {
        method: 'GET',
        headers: {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
        },
        redirect: 'manual'
      });

      const result = {
        route,
        status: response.status,
        redirected: response.status >= 300 && response.status < 400,
        location: response.headers.get('location')
      };

      redirectResults.push(result);

      if (result.redirected && result.location && result.location.includes('/login')) {
        console.log(`   ✅ ${route} properly redirects to login`);
      } else if (response.status === 200) {
        console.log(`   ℹ️  ${route} returns 200 (SPA handles auth client-side)`);
      } else {
        console.log(`   ⚠️  ${route} returned status ${response.status}`);
      }

    } catch (error) {
      console.log(`   ❌ Error testing ${route}: ${error.message}`);
    }
  }

  return redirectResults;
}

async function testRoleBasedRouting(userType, userData) {
  console.log(`\n🧪 Testing routing system for ${userType}...`);
  
  try {
    // Login first
    const loginResult = await loginUser(userType, userData);
    if (!loginResult) {
      throw new Error('Login failed');
    }

    const { token, user } = loginResult;
    console.log(`✅ ${userType} logged in successfully`);

    // Test routes specific to this role
    const routesToTest = ROLE_ROUTES[userType] || [];
    const accessibleRoutes = [];
    const inaccessibleRoutes = [];
    const redirectedRoutes = [];

    console.log(`   🔍 Testing ${routesToTest.length} routes for ${userType}...`);

    for (const route of routesToTest) {
      const result = await testRouteAccess(route, token, userType);
      
      if (result.ok || result.status === 200) {
        accessibleRoutes.push(route);
        console.log(`   ✅ ${userType} can access ${route}`);
      } else if (result.redirected) {
        redirectedRoutes.push({ route, location: result.location });
        console.log(`   🔄 ${userType} redirected from ${route} to ${result.location}`);
      } else {
        inaccessibleRoutes.push(route);
        console.log(`   ⚠️  ${userType} cannot access ${route} (Status: ${result.status})`);
      }
    }

    // Test routes that should be restricted for this role
    const restrictedRoutes = getRestrictedRoutesForRole(userType);
    const properlyRestricted = [];
    const improperlAccessible = [];

    if (restrictedRoutes.length > 0) {
      console.log(`   🔒 Testing ${restrictedRoutes.length} restricted routes for ${userType}...`);
      
      for (const route of restrictedRoutes) {
        const result = await testRouteAccess(route, token, userType);
        
        if (result.status === 401 || result.status === 403 || result.redirected) {
          properlyRestricted.push(route);
          console.log(`   ✅ ${userType} properly restricted from ${route}`);
        } else if (result.ok || result.status === 200) {
          improperlAccessible.push(route);
          console.log(`   ℹ️  ${userType} can access ${route} (client-side auth expected)`);
        } else {
          console.log(`   ⚠️  ${userType} got status ${result.status} for ${route}`);
        }
      }
    }

    // Calculate routing score
    const totalRoutes = routesToTest.length;
    const accessibleCount = accessibleRoutes.length;
    const routingScore = totalRoutes > 0 ? (accessibleCount / totalRoutes) * 100 : 100;

    console.log(`   📊 Routing accessibility: ${routingScore.toFixed(1)}%`);
    console.log(`   📋 Accessible routes: ${accessibleCount}/${totalRoutes}`);

    testResults.passed++;
    testResults.details.push({
      userType,
      status: 'PASSED',
      role: user.user_role,
      routingScore,
      accessibleRoutes: accessibleCount,
      totalRoutes,
      redirectedRoutes: redirectedRoutes.length,
      restrictedRoutes: properlyRestricted.length,
      message: 'Routing system tests completed'
    });

  } catch (error) {
    console.error(`❌ Routing test failed for ${userType}: ${error.message}`);
    testResults.failed++;
    testResults.details.push({
      userType,
      status: 'FAILED',
      error: error.message
    });
  }
}

function getRestrictedRoutesForRole(userType) {
  const allRestrictedRoutes = {
    user: ['/admin', '/super_admin', '/dashboard/moderation'],
    mentor: ['/admin', '/super_admin', '/dashboard/moderation', '/dashboard/investments'],
    investor: ['/admin', '/super_admin', '/dashboard/moderation', '/dashboard/mentorship'],
    moderator: ['/admin', '/super_admin', '/dashboard/mentorship', '/dashboard/investments'],
    admin: ['/super_admin'],
    superadmin: [] // Super admin should have access to everything
  };

  return allRestrictedRoutes[userType] || [];
}

async function testCommonRoutes() {
  console.log('\n🧪 Testing common routes accessibility...');
  
  const commonRoutes = [
    '/',
    '/login',
    '/register'
  ];

  for (const route of commonRoutes) {
    try {
      const response = await fetch(`${FRONTEND_URL}${route}`, {
        method: 'GET',
        headers: {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
        }
      });

      if (response.ok) {
        console.log(`   ✅ Public route ${route} is accessible`);
      } else {
        console.log(`   ⚠️  Public route ${route} returned status ${response.status}`);
      }

    } catch (error) {
      console.log(`   ❌ Error testing public route ${route}: ${error.message}`);
    }
  }
}

async function runRoutingSystemTests() {
  console.log('🚀 Starting Router and URL Testing');
  console.log('=' * 50);
  
  // Test common public routes
  await testCommonRoutes();
  
  // Test unauthorized access redirects
  await testUnauthorizedRedirects();
  
  // Test each user role's routing
  for (const [userType, userData] of Object.entries(TEST_USERS)) {
    await testRoleBasedRouting(userType, userData);
  }
  
  // Print results
  console.log('\n📊 Routing System Test Results');
  console.log('=' * 40);
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
  
  console.log('\n📋 Routing Test Summary:');
  testResults.details.forEach(detail => {
    const status = detail.status === 'PASSED' ? '✅' : '❌';
    console.log(`\n   ${status} ${detail.userType.toUpperCase()}`);
    if (detail.status === 'PASSED') {
      console.log(`      Role: ${detail.role}`);
      console.log(`      Routing Score: ${detail.routingScore?.toFixed(1)}%`);
      console.log(`      Accessible Routes: ${detail.accessibleRoutes}/${detail.totalRoutes}`);
      console.log(`      Redirected Routes: ${detail.redirectedRoutes}`);
      console.log(`      Restricted Routes: ${detail.restrictedRoutes}`);
      console.log(`      Status: ${detail.message}`);
    } else {
      console.log(`      Error: ${detail.error}`);
    }
  });
  
  console.log('\n🎯 Routing System Assessment:');
  if (testResults.failed === 0) {
    console.log('✅ All routing tests passed! The routing system is working correctly.');
  } else {
    console.log('⚠️  Some routing tests failed. Review the errors above.');
  }
  
  return testResults;
}

// Run the tests
runRoutingSystemTests().catch(console.error);
