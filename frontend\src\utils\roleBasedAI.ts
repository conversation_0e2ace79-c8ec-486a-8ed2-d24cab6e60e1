/**
 * Role-Based AI Capabilities System
 * Defines AI features and access levels based on user roles
 */

import { User } from '../services/api';
import { getUserRoles, getUserPermissions, hasRole, hasAnyRole } from './unifiedRoleManager';

export interface AICapability {
  id: string;
  name: string;
  description: string;
  icon: string;
  requiredRoles: string[];
  requiredPermissions: string[];
  features: string[];
  limitations?: string[];
  rateLimit?: {
    requests: number;
    period: string; // 'hour', 'day', 'month'
  };
}

export interface RoleBasedAIAccess {
  role: string;
  displayName: string;
  aiCapabilities: AICapability[];
  maxRequests: {
    chat: number;
    analysis: number;
    generation: number;
  };
  specialFeatures: string[];
}

/**
 * AI Capabilities Definition by Role
 */
export const AI_CAPABILITIES: Record<string, AICapability[]> = {
  // Regular User - Basic AI Features
  user: [
    {
      id: 'basic_chat',
      name: 'AI Assistant',
      description: 'Basic AI chat for business guidance and support',
      icon: 'MessageSquare',
      requiredRoles: ['user'],
      requiredPermissions: ['read'],
      features: [
        'General business advice',
        'Basic idea validation',
        'Simple Q&A support',
        'Language translation'
      ],
      limitations: [
        'Limited to 20 messages per hour',
        'Basic analysis only',
        'No advanced features'
      ],
      rateLimit: { requests: 20, period: 'hour' }
    },
    {
      id: 'basic_analysis',
      name: 'Basic Business Analysis',
      description: 'Simple business idea analysis and feedback',
      icon: 'BarChart3',
      requiredRoles: ['user'],
      requiredPermissions: ['read'],
      features: [
        'Basic SWOT analysis',
        'Simple market feedback',
        'Basic feasibility check'
      ],
      limitations: [
        'Limited to 5 analyses per day',
        'Basic insights only'
      ],
      rateLimit: { requests: 5, period: 'day' }
    }
  ],

  // Mentor - Enhanced AI Features
  mentor: [
    {
      id: 'advanced_chat',
      name: 'Advanced AI Mentor',
      description: 'Enhanced AI capabilities for mentoring and guidance',
      icon: 'Brain',
      requiredRoles: ['mentor'],
      requiredPermissions: ['write'],
      features: [
        'Advanced business strategy advice',
        'Detailed market analysis',
        'Mentorship guidance tools',
        'Industry-specific insights',
        'Startup roadmap generation'
      ],
      limitations: [
        'Limited to 100 messages per hour',
        'Advanced analysis included'
      ],
      rateLimit: { requests: 100, period: 'hour' }
    },
    {
      id: 'mentor_analysis',
      name: 'Mentor-Level Analysis',
      description: 'Comprehensive business analysis with mentoring insights',
      icon: 'TrendingUp',
      requiredRoles: ['mentor'],
      requiredPermissions: ['write'],
      features: [
        'Comprehensive SWOT analysis',
        'Market opportunity assessment',
        'Competitive landscape analysis',
        'Growth strategy recommendations',
        'Risk assessment'
      ],
      limitations: [
        'Limited to 20 analyses per day'
      ],
      rateLimit: { requests: 20, period: 'day' }
    },
    {
      id: 'mentee_support',
      name: 'Mentee Support Tools',
      description: 'AI tools to help mentors support their mentees',
      icon: 'Users',
      requiredRoles: ['mentor'],
      requiredPermissions: ['write'],
      features: [
        'Mentee progress tracking',
        'Personalized advice generation',
        'Session planning assistance',
        'Goal setting recommendations'
      ],
      rateLimit: { requests: 50, period: 'day' }
    }
  ],

  // Investor - Investment-Focused AI
  investor: [
    {
      id: 'investment_analysis',
      name: 'Investment Analysis AI',
      description: 'AI-powered investment analysis and due diligence',
      icon: 'DollarSign',
      requiredRoles: ['investor'],
      requiredPermissions: ['write'],
      features: [
        'Investment opportunity analysis',
        'Due diligence assistance',
        'Market sizing and validation',
        'Financial projections review',
        'Risk assessment for investments',
        'Portfolio optimization advice'
      ],
      limitations: [
        'Limited to 50 analyses per day'
      ],
      rateLimit: { requests: 50, period: 'day' }
    },
    {
      id: 'market_intelligence',
      name: 'Market Intelligence',
      description: 'Advanced market research and intelligence gathering',
      icon: 'Search',
      requiredRoles: ['investor'],
      requiredPermissions: ['write'],
      features: [
        'Market trend analysis',
        'Competitor intelligence',
        'Industry reports generation',
        'Investment landscape mapping'
      ],
      rateLimit: { requests: 30, period: 'day' }
    }
  ],

  // Moderator - Content and Community AI
  moderator: [
    {
      id: 'content_moderation',
      name: 'AI Content Moderation',
      description: 'AI-assisted content moderation and community management',
      icon: 'Shield',
      requiredRoles: ['moderator'],
      requiredPermissions: ['moderate'],
      features: [
        'Automated content screening',
        'Sentiment analysis',
        'Community health insights',
        'Moderation recommendations',
        'Spam detection'
      ],
      rateLimit: { requests: 200, period: 'hour' }
    },
    {
      id: 'community_insights',
      name: 'Community Analytics',
      description: 'AI-powered community insights and engagement analysis',
      icon: 'Users',
      requiredRoles: ['moderator'],
      requiredPermissions: ['moderate'],
      features: [
        'User engagement analysis',
        'Community growth insights',
        'Content performance metrics',
        'Trend identification'
      ],
      rateLimit: { requests: 100, period: 'day' }
    }
  ],

  // Admin - Full AI Access
  admin: [
    {
      id: 'admin_analytics',
      name: 'Admin AI Analytics',
      description: 'Comprehensive AI analytics and system insights',
      icon: 'BarChart',
      requiredRoles: ['admin'],
      requiredPermissions: ['admin'],
      features: [
        'Platform-wide analytics',
        'AI usage statistics',
        'Performance optimization',
        'System health monitoring',
        'User behavior analysis',
        'Revenue optimization'
      ],
      rateLimit: { requests: 1000, period: 'hour' }
    },
    {
      id: 'ai_management',
      name: 'AI System Management',
      description: 'AI system configuration and management tools',
      icon: 'Settings',
      requiredRoles: ['admin'],
      requiredPermissions: ['admin'],
      features: [
        'AI model configuration',
        'Rate limit management',
        'Feature flag control',
        'A/B testing setup',
        'Performance tuning'
      ],
      rateLimit: { requests: 500, period: 'day' }
    }
  ],

  // ✅ ADDED: Super Admin - Complete AI Control
  super_admin: [
    {
      id: 'super_admin_ai_control',
      name: 'Super Admin AI Control',
      description: 'Complete AI system control and management',
      icon: 'Shield',
      requiredRoles: ['super_admin'],
      requiredPermissions: ['super_admin'],
      features: [
        'Full AI system access',
        'Advanced analytics and monitoring',
        'System configuration and tuning',
        'All AI features and capabilities',
        'Security and audit controls',
        'API management and configuration'
      ],
      rateLimit: { requests: 10000, period: 'hour' }
    },
    {
      id: 'ai_system_administration',
      name: 'AI System Administration',
      description: 'Complete AI system administration and control',
      icon: 'Server',
      requiredRoles: ['super_admin'],
      requiredPermissions: ['super_admin'],
      features: [
        'Model management and deployment',
        'API configuration and security',
        'Performance monitoring and tuning',
        'Security controls and auditing',
        'Rate limit and quota management',
        'System health and diagnostics'
      ],
      rateLimit: { requests: 5000, period: 'day' }
    },
    {
      id: 'advanced_ai_analytics',
      name: 'Advanced AI Analytics',
      description: 'Comprehensive AI analytics and monitoring',
      icon: 'BarChart3',
      requiredRoles: ['super_admin'],
      requiredPermissions: ['super_admin'],
      features: [
        'System-wide AI metrics',
        'Usage analytics and trends',
        'Performance monitoring',
        'Security auditing and alerts',
        'Cost analysis and optimization',
        'Predictive analytics'
      ],
      rateLimit: { requests: 2000, period: 'hour' }
    }
  ]

};

/**
 * Get AI capabilities for a specific user based on their roles
 */
export function getUserAICapabilities(user: User | null): AICapability[] {
  if (!user) return [];

  const capabilities: AICapability[] = [];
  const userRoles = getUserRoles(user);

  // Add capabilities based on user roles
  userRoles.forEach(role => {
    const roleCapabilities = AI_CAPABILITIES[role] || [];
    capabilities.push(...roleCapabilities);
  });

  // Remove duplicates and sort by importance
  const uniqueCapabilities = capabilities.filter((capability, index, self) =>
    index === self.findIndex(c => c.id === capability.id)
  );

  return uniqueCapabilities;
}



/**
 * Check if user can access specific AI capability
 */
export function canAccessAICapability(user: User | null, capabilityId: string): boolean {
  const capabilities = getUserAICapabilities(user);
  return capabilities.some(cap => cap.id === capabilityId);
}

/**
 * Get AI rate limits for user
 */
export function getUserAIRateLimits(user: User | null): { chat: number; analysis: number; generation: number } {
  const userRoles = getUserRoles(user);

  // Define rate limits by role (highest role wins)
  const rateLimits = {
    super_admin: { chat: 10000, analysis: 5000, generation: 2000 },  // ✅ ADDED: Highest limits for Super Admin
    admin: { chat: 1000, analysis: 500, generation: 200 },
    moderator: { chat: 200, analysis: 100, generation: 50 },
    investor: { chat: 100, analysis: 50, generation: 25 },
    mentor: { chat: 100, analysis: 50, generation: 25 },
    user: { chat: 20, analysis: 5, generation: 3 }
  };

  // Get highest rate limit based on roles
  let maxLimits = rateLimits.user;
  
  userRoles.forEach(role => {
    const roleLimits = rateLimits[role as keyof typeof rateLimits];
    if (roleLimits) {
      if (roleLimits.chat > maxLimits.chat) maxLimits = roleLimits;
    }
  });

  return maxLimits;
}

/**
 * Get role-specific AI interface configuration
 */
export function getRoleBasedAIConfig(user: User | null) {
  const userRoles = getUserRoles(user);
  const capabilities = getUserAICapabilities(user);
  const rateLimits = getUserAIRateLimits(user);

  return {
    roles: userRoles,
    capabilities,
    rateLimits,
    primaryRole: userRoles[0] || 'user',
    hasAdvancedFeatures: userRoles.some(role => ['super_admin', 'admin', 'moderator', 'investor', 'mentor'].includes(role))
  };
}
