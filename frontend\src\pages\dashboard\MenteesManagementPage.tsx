import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Users,
  User,
  Calendar,
  MessageSquare,
  TrendingUp,
  Star,
  Clock,
  ArrowRight,
  Plus,
  Search,
  Filter,
  MoreVertical,
  Video,
  Mail,
  Phone,
  Award,
  Target,
  CheckCircle,
  AlertCircle,
  Edit,
  Trash2,
  X,
  Save,
  Loader2
} from 'lucide-react';
import { useAppDispatch, useAppSelector } from '../../store/hooks';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { getUserRole } from '../../utils/unifiedRoleManager';

// API Services
import { menteesApi, type Mentee as ApiMentee } from '../../services/mentorshipApi';

// UI Components
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { Badge } from '../../components/ui/Badge';
import { Input } from '../../components/ui/Input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/Tabs';

// Use the API Mentee type and extend it with local UI properties
interface Mentee extends Omit<ApiMentee, 'id'> {
  id: string; // Convert number to string for UI consistency
  businessIdea: string; // Map from business_idea
  sessionsCompleted: number; // Map from sessions_completed
  totalSessions?: number; // Optional for UI calculations
  lastSession?: string; // Map from last session data
  joinDate: string; // Map from joined_date
  stage?: string; // Optional stage information
  industry?: string; // Optional industry information
}

const MenteesManagementPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();

  const { user, isAuthenticated } = useAppSelector(state => state.auth);
  const userRole = getUserRole(user);

  // Redirect if not a mentor
  useEffect(() => {
    if (isAuthenticated && userRole !== 'mentor') {
      console.warn('Access denied: User is not a mentor');
      navigate('/dashboard');
    }
  }, [isAuthenticated, userRole, navigate]);

  // State Management
  const [mentees, setMentees] = useState<Mentee[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [stageFilter, setStageFilter] = useState('all');
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // CRUD State
  const [showAddForm, setShowAddForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [editingMentee, setEditingMentee] = useState<Mentee | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deletingMentee, setDeletingMentee] = useState<Mentee | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form state for add/edit
  const [formData, setFormData] = useState<{
    name: string;
    email: string;
    phone: string;
    businessIdea: string;
    status: 'active' | 'inactive' | 'completed';
    goals: string;
    notes: string;
  }>({
    name: '',
    email: '',
    phone: '',
    businessIdea: '',
    status: 'active',
    goals: '',
    notes: ''
  });

  // Load mentees data from API with fallback
  const loadMentees = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const apiMentees = await menteesApi.getAll();

      // Transform API data to match UI interface
      const transformedMentees: Mentee[] = apiMentees.map((apiMentee) => ({
        ...apiMentee,
        id: apiMentee.id.toString(),
        name: apiMentee.name || `${apiMentee.user.first_name} ${apiMentee.user.last_name}`,
        email: apiMentee.email || apiMentee.user.email,
        businessIdea: apiMentee.business_idea || 'General Mentorship',
        sessionsCompleted: apiMentee.sessions_completed,
        totalSessions: 20, // Default value, could be calculated from sessions
        lastSession: undefined, // Would need to be calculated from sessions
        joinDate: apiMentee.joined_date,
        stage: 'Planning', // Default value, could be derived from business idea
        industry: 'General', // Default value, could be derived from business idea
      }));

      setMentees(transformedMentees);
    } catch (err) {
      console.error('Error loading mentees:', err);

      // Fallback to demo data for development/testing
      const fallbackMentees: Mentee[] = [
        {
          id: '1',
          name: 'Alex Chen',
          email: '<EMAIL>',
          phone: '+****************',
          businessIdea: 'AI-Powered Learning Platform',
          progress: 75,
          sessionsCompleted: 12,
          totalSessions: 15,
          next_session: '2024-01-22T14:00:00Z',
          lastSession: '2024-01-15T14:00:00Z',
          status: 'active',
          joinDate: '2023-09-15',
          joined_date: '2023-09-15',
          sessions_completed: 12,
          goals: ['Launch MVP', 'Acquire first 100 users', 'Raise seed funding'],
          rating: 5,
          notes: 'Very motivated and making excellent progress.',
          stage: 'MVP',
          industry: 'EdTech',
          user: {
            id: 1,
            first_name: 'Alex',
            last_name: 'Chen',
            email: '<EMAIL>'
          }
        },
        {
          id: '2',
          name: 'Sarah Johnson',
          email: '<EMAIL>',
          phone: '+****************',
          businessIdea: 'Sustainable Fashion Marketplace',
          progress: 85,
          sessionsCompleted: 18,
          totalSessions: 20,
          next_session: '2024-01-25T10:00:00Z',
          lastSession: '2024-01-18T10:00:00Z',
          status: 'active',
          joinDate: '2023-06-20',
          joined_date: '2023-06-20',
          sessions_completed: 18,
          goals: ['Scale to 1000 sellers', 'International expansion', 'Series A funding'],
          rating: 4,
          notes: 'Great execution skills. Needs support with scaling operations.',
          stage: 'Growth',
          industry: 'Fashion',
          user: {
            id: 2,
            first_name: 'Sarah',
            last_name: 'Johnson',
            email: '<EMAIL>'
          }
        }
      ];

      setMentees(fallbackMentees);
      setError('Using demo data - API connection failed. Please check your authentication.');
    } finally {
      setIsLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadMentees();
  }, []);

  // CRUD Functions with API calls
  const handleAddMentee = async () => {
    if (!formData.name || !formData.email || !formData.businessIdea) {
      alert('Please fill in all required fields');
      return;
    }

    try {
      setIsSubmitting(true);

      // Create mentee via API
      await menteesApi.create({
        email: formData.email,
        business_idea: formData.businessIdea,
        goals: formData.goals ? formData.goals.split(',').map(g => g.trim()) : [],
        notes: formData.notes
      });

      // Reload mentees list
      await loadMentees();

      // Reset form
      setFormData({ name: '', email: '', phone: '', businessIdea: '', status: 'active', goals: '', notes: '' });
      setShowAddForm(false);

    } catch (err) {
      console.error('Error creating mentee:', err);
      alert('Failed to create mentee. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditMentee = (mentee: Mentee) => {
    setEditingMentee(mentee);
    setFormData({
      name: mentee.name || '',
      email: mentee.email || '',
      phone: mentee.phone || '',
      businessIdea: mentee.businessIdea,
      status: mentee.status,
      goals: mentee.goals.join(', '),
      notes: mentee.notes || ''
    });
    setShowEditForm(true);
  };

  const handleUpdateMentee = async () => {
    if (!editingMentee || !formData.businessIdea) {
      alert('Please fill in all required fields');
      return;
    }

    try {
      setIsSubmitting(true);

      // Update mentee via API
      await menteesApi.update(parseInt(editingMentee.id), {
        goals: formData.goals ? formData.goals.split(',').map(g => g.trim()) : [],
        notes: formData.notes,
        status: formData.status
      });

      // Reload mentees list
      await loadMentees();

      // Reset form
      setEditingMentee(null);
      setFormData({ name: '', email: '', phone: '', businessIdea: '', status: 'active', goals: '', notes: '' });
      setShowEditForm(false);

    } catch (err) {
      console.error('Error updating mentee:', err);
      alert('Failed to update mentee. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteMentee = (mentee: Mentee) => {
    setDeletingMentee(mentee);
    setShowDeleteConfirm(true);
  };

  const confirmDelete = async () => {
    if (!deletingMentee) return;

    try {
      setIsSubmitting(true);

      // Delete mentee via API
      await menteesApi.delete(parseInt(deletingMentee.id));

      // Reload mentees list
      await loadMentees();

      // Reset state
      setDeletingMentee(null);
      setShowDeleteConfirm(false);

    } catch (err) {
      console.error('Error deleting mentee:', err);
      alert('Failed to delete mentee. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Filter mentees based on search and status
  const filteredMentees = mentees.filter(mentee => {
    const matchesSearch = (mentee.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         mentee.businessIdea.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (mentee.email || '').toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || mentee.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // Helper functions
  const resetForm = () => {
    setFormData({ name: '', email: '', phone: '', businessIdea: '', status: 'active', goals: '', notes: '' });
    setEditingMentee(null);
    setShowAddForm(false);
    setShowEditForm(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-600/20 text-green-400';
      case 'inactive': return 'bg-yellow-600/20 text-yellow-400';
      case 'completed': return 'bg-blue-600/20 text-blue-400';
      default: return 'bg-gray-600/20 text-gray-400';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Helper function to get stage color
  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'Idea':
        return 'bg-blue-100 text-blue-800';
      case 'MVP':
        return 'bg-yellow-100 text-yellow-800';
      case 'Growth':
        return 'bg-green-100 text-green-800';
      case 'Scale':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Helper function to render star rating
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`w-4 h-4 ${
          index < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  // Debug: Log authentication and role status
  console.log('🔍 Auth Debug:', { isAuthenticated, userRole, user });

  // Temporarily allow access for debugging (remove in production)
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 flex items-center justify-center">
        <div className="text-center text-white">
          <AlertCircle size={48} className="mx-auto mb-4 text-red-400" />
          <h2 className="text-xl font-semibold mb-2">Not Authenticated</h2>
          <p className="text-gray-300">Please log in to access this page.</p>
          <p className="text-gray-400 mt-2">Debug: isAuthenticated={String(isAuthenticated)}, userRole={userRole}</p>
        </div>
      </div>
    );
  }

  // Show warning if not mentor but still allow access for debugging
  if (userRole !== 'mentor') {
    console.warn('⚠️ User is not a mentor but allowing access for debugging');
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 flex items-center justify-center">
        <div className="text-center text-white">
          <Loader2 size={48} className="mx-auto mb-4 text-blue-400 animate-spin" />
          <h2 className="text-xl font-semibold mb-2">Loading Mentees</h2>
          <p className="text-gray-300">Please wait while we fetch your mentees data...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 flex items-center justify-center">
        <div className="text-center text-white">
          <AlertCircle size={48} className="mx-auto mb-4 text-red-400" />
          <h2 className="text-xl font-semibold mb-2">Error Loading Mentees</h2>
          <p className="text-gray-300 mb-4">{error}</p>
          <Button
            onClick={loadMentees}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className={`flex justify-between items-center mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <h1 className="text-3xl font-bold text-white">Mentees Management</h1>
            <p className="text-gray-300 mt-1">Manage and track your mentees' progress</p>
          </div>
          <div className={`flex space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
            <button
              onClick={() => setShowAddForm(true)}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-md text-white flex items-center"
            >
              <Plus size={18} className={`mr-2 ${isRTL ? "ml-2 mr-0" : ""}`} />
              Add Mentee
            </button>
          </div>
        </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Mentees</p>
                <p className="text-2xl font-bold">{mentees.length}</p>
              </div>
              <User className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Mentees</p>
                <p className="text-2xl font-bold text-green-600">
                  {mentees.filter(m => m.status === 'active').length}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Progress</p>
                <p className="text-2xl font-bold text-purple-600">
                  {Math.round(mentees.reduce((sum, m) => sum + m.progress, 0) / mentees.length)}%
                </p>
              </div>
              <Calendar className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Rating</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {mentees.filter(m => m.rating).length > 0 
                    ? (mentees.filter(m => m.rating).reduce((sum, m) => sum + (m.rating || 0), 0) / 
                       mentees.filter(m => m.rating).length).toFixed(1)
                    : 'N/A'
                  }
                </p>
              </div>
              <MessageSquare className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search mentees..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select 
              value={statusFilter} 
              onChange={(e) => setStatusFilter(e.target.value)}
              className="border rounded px-3 py-2"
            >
              <option value="all">All Status</option>
              <option value="Active">Active</option>
              <option value="Inactive">Inactive</option>
              <option value="Completed">Completed</option>
            </select>
            <select 
              value={stageFilter} 
              onChange={(e) => setStageFilter(e.target.value)}
              className="border rounded px-3 py-2"
            >
              <option value="all">All Stages</option>
              <option value="Idea">Idea</option>
              <option value="MVP">MVP</option>
              <option value="Growth">Growth</option>
              <option value="Scale">Scale</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Mentees Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} tabGroupId="mentees-tabs">
        <TabsList>
          <TabsTrigger value="overview" tabGroupId="mentees-tabs">Overview</TabsTrigger>
          <TabsTrigger value="active" tabGroupId="mentees-tabs">Active</TabsTrigger>
          <TabsTrigger value="completed" tabGroupId="mentees-tabs">Completed</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4" tabGroupId="mentees-tabs">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredMentees.map((mentee) => (
              <Card key={mentee.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <User className="w-5 h-5 text-gray-400" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{mentee.name}</CardTitle>
                        <p className="text-sm text-gray-600">{mentee.industry}</p>
                      </div>
                    </div>
                    <Badge className={getStatusColor(mentee.status)}>
                      {mentee.status}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <h4 className="font-medium text-sm mb-1">Business Idea</h4>
                    <p className="text-sm text-gray-700">{mentee.businessIdea}</p>
                  </div>

                  <div className="flex items-center justify-between">
                    <Badge className={getStageColor(mentee.stage || 'Planning')}>
                      {mentee.stage || 'Planning'}
                    </Badge>
                    <span className="text-sm text-gray-600">
                      {mentee.totalSessions} sessions
                    </span>
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Progress</span>
                      <span>{mentee.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${mentee.progress}%` }}
                      ></div>
                    </div>
                  </div>

                  {mentee.rating && (
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600">Rating:</span>
                      <div className="flex">{renderStars(mentee.rating)}</div>
                    </div>
                  )}

                  <div className="text-sm text-gray-600">
                    <p>Joined: {formatDate(mentee.joinDate)}</p>
                    {mentee.lastSession && (
                      <p>Last session: {formatDate(mentee.lastSession)}</p>
                    )}
                    {mentee.next_session && (
                      <p>Next session: {formatDate(mentee.next_session)}</p>
                    )}
                  </div>

                  {mentee.notes && (
                    <div className="bg-gray-50 p-2 rounded text-sm">
                      <strong>Notes:</strong> {mentee.notes}
                    </div>
                  )}

                  <div className="flex gap-2 pt-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <MessageSquare className="w-4 h-4 mr-1" />
                      Message
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <Calendar className="w-4 h-4 mr-1" />
                      Schedule
                    </Button>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      View Profile
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      Edit Notes
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredMentees.length === 0 && (
            <Card>
              <CardContent className="p-8 text-center">
                <User className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-4">No mentees found matching your criteria.</p>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Your First Mentee
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
      </div>
    </div>
  );
};

export default MenteesManagementPage;
