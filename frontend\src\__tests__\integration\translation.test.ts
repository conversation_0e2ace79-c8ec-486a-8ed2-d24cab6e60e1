/**
 * Integration tests for translation system
 * Tests the complete i18n workflow including missing keys detection
 */

import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../i18n';
import { store } from '../../store';
import ForumPage from '../../pages/ForumPage';
import { auditTranslations } from '../../utils/translationAuditor';

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Provider store={store}>
    <BrowserRouter>
      <I18nextProvider i18n={i18n}>
        {children}
      </I18nextProvider>
    </BrowserRouter>
  </Provider>
);

describe('Translation System Integration', () => {
  beforeEach(() => {
    // Reset language to English for consistent testing
    i18n.changeLanguage('en');
  });

  describe('Translation Completeness', () => {
    test('should have no missing translation keys', () => {
      const { missingKeys } = auditTranslations();
      
      // Check that critical sections have complete translations
      const criticalSections = ['forum', 'incubator', 'nav', 'auth'];
      
      criticalSections.forEach(section => {
        const englishMissing = missingKeys.en?.filter(key => key.startsWith(section)) || [];
        const arabicMissing = missingKeys.ar?.filter(key => key.startsWith(section)) || [];
        
        expect(englishMissing).toHaveLength(0);
        expect(arabicMissing).toHaveLength(0);
      });
    });

    test('should have matching key counts between languages', () => {
      const { translations } = findMissingKeys();
      
      const extractKeyCount = (obj: any): number => {
        let count = 0;
        for (const value of Object.values(obj)) {
          if (typeof value === 'object' && value !== null) {
            count += extractKeyCount(value);
          } else {
            count++;
          }
        }
        return count;
      };

      const enKeyCount = extractKeyCount(translations.en);
      const arKeyCount = extractKeyCount(translations.ar);
      
      // Allow small variance (5%) for language-specific keys
      const variance = Math.abs(enKeyCount - arKeyCount) / Math.max(enKeyCount, arKeyCount);
      expect(variance).toBeLessThan(0.05);
    });
  });

  describe('Forum Page Translations', () => {
    test('should render forum page without translation key placeholders', () => {
      render(
        <TestWrapper>
          <ForumPage />
        </TestWrapper>
      );

      // Check that actual translations are rendered, not keys
      expect(screen.queryByText('forum.title')).not.toBeInTheDocument();
      expect(screen.queryByText('forum.description')).not.toBeInTheDocument();
      expect(screen.queryByText('forum.guidelines.title')).not.toBeInTheDocument();
      
      // Check that actual content is present
      expect(screen.getByText(/Community Forum|منتدى المجتمع/)).toBeInTheDocument();
    });

    test('should switch languages correctly', async () => {
      render(
        <TestWrapper>
          <ForumPage />
        </TestWrapper>
      );

      // Test English
      expect(screen.getByText(/Community Forum/)).toBeInTheDocument();

      // Switch to Arabic
      await i18n.changeLanguage('ar');
      
      // Test Arabic
      expect(screen.getByText(/منتدى المجتمع/)).toBeInTheDocument();
    });
  });

  describe('RTL Support', () => {
    test('should apply RTL styles for Arabic', async () => {
      await i18n.changeLanguage('ar');
      
      render(
        <TestWrapper>
          <ForumPage />
        </TestWrapper>
      );

      // Check that document direction is set
      expect(document.documentElement.dir).toBe('rtl');
      expect(document.documentElement.lang).toBe('ar');
    });

    test('should apply LTR styles for English', async () => {
      await i18n.changeLanguage('en');
      
      render(
        <TestWrapper>
          <ForumPage />
        </TestWrapper>
      );

      // Check that document direction is set
      expect(document.documentElement.dir).toBe('ltr');
      expect(document.documentElement.lang).toBe('en');
    });
  });

  describe('Translation Performance', () => {
    test('should load translations quickly', async () => {
      const startTime = performance.now();
      
      await i18n.changeLanguage('ar');
      await i18n.changeLanguage('en');
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Language switching should be fast (under 100ms)
      expect(duration).toBeLessThan(100);
    });

    test('should cache translations', () => {
      const initialResources = i18n.store.data;
      
      // Change language and back
      i18n.changeLanguage('ar');
      i18n.changeLanguage('en');
      
      // Resources should still be available (cached)
      expect(i18n.store.data.en).toBeDefined();
      expect(i18n.store.data.ar).toBeDefined();
      expect(Object.keys(i18n.store.data)).toEqual(['en', 'ar']);
    });
  });

  describe('Error Handling', () => {
    test('should handle missing translation gracefully', () => {
      const missingKey = 'this.key.does.not.exist';
      const result = i18n.t(missingKey);
      
      // Should return the key itself as fallback
      expect(result).toBe(missingKey);
    });

    test('should handle malformed translation keys', () => {
      const malformedKeys = ['', null, undefined, 123];
      
      malformedKeys.forEach(key => {
        expect(() => i18n.t(key as any)).not.toThrow();
      });
    });
  });
});
