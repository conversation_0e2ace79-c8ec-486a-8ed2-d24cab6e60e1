/**
 * Analytics Page
 * Main analytics dashboard for users
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { BarChart3, TrendingUp, Users, FileText, Clock, Target, Activity, Calendar } from 'lucide-react';
import { useAppSelector } from '../../store/hooks';
import { useAnalytics } from '../../hooks/useAnalytics';

interface AnalyticsData {
  overview: {
    totalBusinessIdeas: number;
    totalBusinessPlans: number;
    completedPlans: number;
    timeSpent: number; // in hours
  };
  progress: {
    weeklyProgress: number[];
    monthlyGoals: {
      target: number;
      achieved: number;
    };
  };
  activity: {
    recentActions: Array<{
      id: number;
      action: string;
      timestamp: string;
      type: 'idea' | 'plan' | 'template' | 'ai';
    }>;
  };
  performance: {
    planCompletionRate: number;
    averageTimePerPlan: number;
    mostUsedTemplates: Array<{
      name: string;
      usage: number;
    }>;
  };
}

const AnalyticsPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAppSelector(state => state.auth);

  // Use real analytics hook
  const { analyticsData, isLoading: loading, error, refetch } = useAnalytics();

  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'quarter'>('month');

  useEffect(() => {
    refetch();
  }, [timeRange, refetch]);

  // Analytics data is now fetched by the hook

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ReactNode;
    change?: string;
    changeType?: 'positive' | 'negative' | 'neutral';
  }> = ({ title, value, icon, change, changeType = 'neutral' }) => (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {change && (
            <p className={`text-sm ${
              changeType === 'positive' ? 'text-green-600' : 
              changeType === 'negative' ? 'text-red-600' : 'text-gray-600'
            }`}>
              {change}
            </p>
          )}
        </div>
        <div className="p-3 bg-blue-50 rounded-lg">
          {icon}
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {[1, 2, 3, 4].map(i => (
                <div key={i} className="bg-white rounded-lg shadow p-6">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-red-900 mb-2">Error</h2>
            <p className="text-red-700">{error}</p>
            <button
              onClick={fetchAnalyticsData}
              className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!analyticsData) return null;

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {t('analytics.title', 'Analytics Dashboard')}
            </h1>
            <p className="text-gray-600 mt-2">
              {t('analytics.subtitle', 'Track your progress and performance')}
            </p>
          </div>
          
          {/* Time Range Selector */}
          <div className="flex bg-white rounded-lg border">
            {(['week', 'month', 'quarter'] as const).map((range) => (
              <button
                key={range}
                onClick={() => setTimeRange(range)}
                className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                  timeRange === range
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {t(`analytics.timeRange.${range}`, range.charAt(0).toUpperCase() + range.slice(1))}
              </button>
            ))}
          </div>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatCard
            title={t('analytics.totalIdeas', 'Total Business Ideas')}
            value={analyticsData.overview.totalBusinessIdeas}
            icon={<Target className="w-6 h-6 text-blue-600" />}
            change="+2 this month"
            changeType="positive"
          />
          <StatCard
            title={t('analytics.totalPlans', 'Total Business Plans')}
            value={analyticsData.overview.totalBusinessPlans}
            icon={<FileText className="w-6 h-6 text-blue-600" />}
            change="+1 this month"
            changeType="positive"
          />
          <StatCard
            title={t('analytics.completedPlans', 'Completed Plans')}
            value={analyticsData.overview.completedPlans}
            icon={<TrendingUp className="w-6 h-6 text-blue-600" />}
            change={`${analyticsData.performance.planCompletionRate}% completion rate`}
            changeType="neutral"
          />
          <StatCard
            title={t('analytics.timeSpent', 'Time Spent (Hours)')}
            value={analyticsData.overview.timeSpent}
            icon={<Clock className="w-6 h-6 text-blue-600" />}
            change="15.2h avg per plan"
            changeType="neutral"
          />
        </div>

        {/* Progress and Goals */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Weekly Progress */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {t('analytics.weeklyProgress', 'Weekly Progress')}
            </h3>
            <div className="space-y-3">
              {analyticsData.progress.weeklyProgress.map((progress, index) => (
                <div key={index} className="flex items-center">
                  <span className="text-sm text-gray-600 w-16">
                    Day {index + 1}
                  </span>
                  <div className="flex-1 bg-gray-200 rounded-full h-2 mx-3">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900 w-12">
                    {progress}%
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Monthly Goals */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {t('analytics.monthlyGoals', 'Monthly Goals')}
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">{t('analytics.target', 'Target')}</span>
                <span className="font-semibold">{analyticsData.progress.monthlyGoals.target} plans</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">{t('analytics.achieved', 'Achieved')}</span>
                <span className="font-semibold text-green-600">{analyticsData.progress.monthlyGoals.achieved} plans</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className="bg-green-600 h-3 rounded-full transition-all duration-300"
                  style={{ 
                    width: `${(analyticsData.progress.monthlyGoals.achieved / analyticsData.progress.monthlyGoals.target) * 100}%` 
                  }}
                ></div>
              </div>
              <p className="text-sm text-gray-600">
                {Math.round((analyticsData.progress.monthlyGoals.achieved / analyticsData.progress.monthlyGoals.target) * 100)}% of monthly goal achieved
              </p>
            </div>
          </div>
        </div>

        {/* Recent Activity and Performance */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Activity */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {t('analytics.recentActivity', 'Recent Activity')}
            </h3>
            <div className="space-y-4">
              {analyticsData.activity.recentActions.map((action) => (
                <div key={action.id} className="flex items-start space-x-3">
                  <div className={`p-2 rounded-lg ${
                    action.type === 'idea' ? 'bg-blue-100' :
                    action.type === 'plan' ? 'bg-green-100' :
                    action.type === 'template' ? 'bg-purple-100' :
                    'bg-orange-100'
                  }`}>
                    <Activity className={`w-4 h-4 ${
                      action.type === 'idea' ? 'text-blue-600' :
                      action.type === 'plan' ? 'text-green-600' :
                      action.type === 'template' ? 'text-purple-600' :
                      'text-orange-600'
                    }`} />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">{action.action}</p>
                    <p className="text-xs text-gray-500">
                      {new Date(action.timestamp).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Most Used Templates */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              {t('analytics.mostUsedTemplates', 'Most Used Templates')}
            </h3>
            <div className="space-y-4">
              {analyticsData.performance.mostUsedTemplates.map((template, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm text-gray-900">{template.name}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${(template.usage / 10) * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium text-gray-600 w-8">
                      {template.usage}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsPage;
