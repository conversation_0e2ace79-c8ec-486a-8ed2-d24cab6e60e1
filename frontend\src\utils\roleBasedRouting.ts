/**
 * ⚠️ DEPRECATED - DO NOT USE ⚠️
 * This file contains old role checking logic that conflicts with the unified role manager.
 * Use '../utils/unifiedRoleManager' instead.
 *
 * @deprecated Use unifiedRoleManager.ts instead
 */

console.warn('⚠️ DEPRECATED: roleBasedRouting.ts is deprecated. Use unifiedRoleManager.ts instead.');

// Re-export functions from unified role manager to maintain compatibility
// while migrating existing code
export {
  getUserRole as getUserType,
  getUserRoles,
  hasRole,
  hasAnyRole,
  canAccessRoute,
  isSuperAdmin,
  isAdmin,
  getDashboardRoute,
  UserRole,
  PermissionLevel
} from './unifiedRoleManager';

export type DashboardType = 'super_admin' | 'admin' | 'mentor' | 'investor' | 'moderator' | 'user';

/**
 * Get the primary dashboard route for a user based on their roles
 */
export function getPrimaryDashboardRoute(user: User | null): string {
  // Always log for debugging multi-role authentication issues
  console.log('🔍 getPrimaryDashboardRoute called with user:', user);

  if (!user) {
    console.log('❌ No user provided, returning /login');
    return '/login';
  }

  const isDevelopment = import.meta.env?.DEV || window.location.hostname === 'localhost';

  if (isDevelopment) {
    console.log('🔍 getPrimaryDashboardRoute debug:', {
      user: user.username,
      is_superuser: user.is_superuser,
      is_admin: user.is_admin,
      is_staff: user.is_staff,
      profile: user.profile,
      active_roles: user.profile?.active_roles?.map(r => r.name),
      primary_role: user.profile?.primary_role?.name
    });
  }

  // Check for specific roles in order of priority
  const activeRoles = user.profile?.active_roles || [];
  const primaryRole = user.profile?.primary_role;

  // Priority order: super_admin > admin > moderator > mentor > investor > user
  // Each role now has their own dedicated dashboard
  const rolePriority: Record<string, { route: string; priority: number }> = {
    'super_admin': { route: '/super_admin', priority: 6 },
    'admin': { route: '/admin', priority: 5 },
    'moderator': { route: '/dashboard/moderator', priority: 4 }, // Dedicated moderator dashboard
    'mentor': { route: '/dashboard/mentor', priority: 3 },       // Dedicated mentor dashboard
    'investor': { route: '/dashboard/investor', priority: 2 },   // Dedicated investor dashboard
    'user': { route: '/dashboard', priority: 1 }                // Regular user dashboard
  };

  // Find the highest priority role
  let highestPriorityRoute = '/dashboard'; // default
  let highestPriority = 0;

  // Check primary role first
  if (primaryRole && rolePriority[primaryRole.name]) {
    const roleInfo = rolePriority[primaryRole.name];
    if (roleInfo.priority > highestPriority) {
      highestPriority = roleInfo.priority;
      highestPriorityRoute = roleInfo.route;
      if (isDevelopment) {
        console.log(`✅ Primary role ${primaryRole.name} found, routing to ${roleInfo.route}`);
      }
    }
  }

  // Check all active roles
  activeRoles.forEach(role => {
    if (rolePriority[role.name]) {
      const roleInfo = rolePriority[role.name];
      if (roleInfo.priority > highestPriority) {
        highestPriority = roleInfo.priority;
        highestPriorityRoute = roleInfo.route;
        if (isDevelopment) {
          console.log(`✅ Active role ${role.name} found, routing to ${roleInfo.route}`);
        }
      }
    }
  });

  if (isDevelopment) {
    console.log(`🎯 Final routing decision: ${highestPriorityRoute} (priority: ${highestPriority})`);
  }

  return highestPriorityRoute;
}

/**
 * Get the dashboard type for a user
 */
export function getUserDashboardType(user: User | null): DashboardType {
  if (!user) {
    return 'user';
  }

  const isDevelopment = import.meta.env?.DEV || window.location.hostname === 'localhost';

  // Super Admin gets super_admin dashboard type
  if (isSuperAdmin(user)) {
    if (isDevelopment) {
      console.log('🚀 getUserDashboardType: Super Admin detected');
    }
    return 'super_admin';
  }

  if (user.is_admin) {
    if (isDevelopment) {
      console.log('🔍 getUserDashboardType: Admin user detected');
    }
    return 'admin';
  }

  const activeRoles = user.profile?.active_roles || [];
  const primaryRole = user.profile?.primary_role;

  if (isDevelopment) {
    console.log('🔍 getUserDashboardType debug:', {
      user: user.username,
      primaryRole: primaryRole?.name,
      activeRoles: activeRoles.map(r => r.name)
    });
  }

  // Check primary role first
  if (primaryRole) {
    switch (primaryRole.name) {
      case 'moderator':
        if (isDevelopment) console.log('✅ Primary role: moderator');
        return 'moderator';
      case 'mentor':
        if (isDevelopment) console.log('✅ Primary role: mentor');
        return 'mentor';
      case 'investor':
        if (isDevelopment) console.log('✅ Primary role: investor');
        return 'investor';
      default:
        break;
    }
  }

  // Check active roles for highest priority
  const roleTypes: DashboardType[] = ['moderator', 'mentor', 'investor'];
  for (const roleType of roleTypes) {
    if (activeRoles.some(role => role.name === roleType)) {
      if (isDevelopment) console.log(`✅ Active role found: ${roleType}`);
      return roleType;
    }
  }

  if (isDevelopment) console.log('🔍 No special roles found, defaulting to user');
  return 'user';
}

/**
 * Get the appropriate dashboard URL for a user
 */
export function getUserDashboardUrl(user: User | null): string {
  const dashboardType = getUserDashboardType(user);

  switch (dashboardType) {
    case 'super_admin':
      return '/admin/super-admin';
    case 'admin':
      return '/admin';
    case 'user':
    default:
      return '/dashboard';
  }
}

/**
 * Check if user has a specific role
 */
export function hasRole(user: User | null, roleName: string): boolean {
  if (!user) return false;

  switch (roleName.toLowerCase()) {
    case 'admin':
    case 'super_admin':
      return user.is_admin === true;
    case 'moderator':
    case 'staff':
      return user.is_staff === true;
    default:
      return true; // All authenticated users have 'user' role
  }
}

/**
 * Check if user is a Super Admin
 */
export function isSuperAdmin(user: User | null): boolean {
  return user?.is_admin === true;
}

/**
 * Check if user has any of the specified roles
 */
export function hasAnyRole(user: User | null, roleNames: string[]): boolean {
  if (!user) return false;

  return roleNames.some(role => hasRole(user, role));
}

/**
 * Get user's highest permission level
 */
export function getHighestPermissionLevel(user: User | null): 'read' | 'write' | 'moderate' | 'admin' | 'super_admin' {
  if (!user) {
    return 'read';
  }

  // Super Admin has the highest permission level
  if (isSuperAdmin(user)) {
    return 'super_admin';
  }

  if (user.is_admin) {
    return 'admin';
  }

  return user.profile?.highest_permission_level || 'read';
}

/**
 * Check if user can access a specific route
 */
export function canAccessRoute(user: User | null, requiredRoles: string[], requiredPermissions?: string[]): boolean {
  const isDevelopment = import.meta.env?.DEV || window.location.hostname === 'localhost';

  if (!user) {
    return false;
  }

  // 🚀 SUPER ADMIN BYPASS: Super Admin can access ALL pages
  if (isSuperAdmin(user)) {
    return true;
  }

  // Check role requirements
  if (requiredRoles.length > 0) {
    const hasRequiredRole = hasAnyRole(user, requiredRoles);
    if (!hasRequiredRole) {
      return false;
    }
  }

  // Check permission requirements
  if (requiredPermissions && requiredPermissions.length > 0) {
    const userPermission = getHighestPermissionLevel(user);
    const permissionHierarchy = { 'read': 1, 'write': 2, 'moderate': 3, 'admin': 4, 'super_admin': 5 };
    const userLevel = permissionHierarchy[userPermission];

    const hasRequiredPermission = requiredPermissions.some(permission => {
      const requiredLevel = permissionHierarchy[permission as keyof typeof permissionHierarchy];
      return userLevel >= requiredLevel;
    });

    if (!hasRequiredPermission) {
      return false;
    }
  }

  if (isDevelopment) {
    console.log(`🔍 canAccessRoute: Access granted`, {
      username: user.username,
      requiredRoles,
      requiredPermissions
    });
  }

  return true;
}

/**
 * Get appropriate navigation items based on user roles
 */
export function getNavigationItems(user: User | null) {
  const dashboardType = getUserDashboardType(user);

  const baseItems = [
    { path: '/dashboard', label: 'Dashboard', icon: Home }
  ];

  // Super Admin gets access to ALL navigation items
  if (isSuperAdmin(user)) {
    return [
      ...baseItems,
      { path: '/super_admin', label: 'Super Admin Dashboard', icon: Shield },
      { path: '/super_admin/users', label: 'User Management', icon: Users },
      { path: '/super_admin/system-management', label: 'System Management', icon: Server },
      { path: '/super_admin/user-impersonation', label: 'User Impersonation', icon: Eye },
      { path: '/super_admin/ai-system-management', label: 'AI System Management', icon: Brain },
      { path: '/super_admin/system-logs', label: 'System Logs', icon: FileText },
      { path: '/super_admin/settings', label: 'Super Admin Settings', icon: Settings },
      { path: '/admin', label: 'Admin Panel Access', icon: Settings },
      { path: '/dashboard/mentor', label: 'Mentor Dashboard', icon: Users },
      { path: '/dashboard/investor', label: 'Investor Dashboard', icon: TrendingUp },
      { path: '/dashboard/moderator', label: 'Moderator Panel', icon: Shield }
    ];
  }

  switch (dashboardType) {
    case 'admin':
      return [
        ...baseItems,
        { path: '/admin', label: 'Admin Panel', icon: Settings },
        { path: '/admin/users', label: 'User Management', icon: Users },
        { path: '/admin/roles', label: 'Role Management', icon: Shield },
        { path: '/admin/analytics', label: 'System Analytics', icon: BarChart3 }
      ];

    case 'mentor':
      return [
        ...baseItems,
        { path: '/dashboard/mentor', label: 'Mentor Dashboard', icon: Users },
        { path: '/dashboard/mentor/mentees', label: 'My Mentees', icon: UserIcon },
        { path: '/dashboard/mentor/sessions', label: 'Sessions', icon: Calendar },
        { path: '/dashboard/mentor/resources', label: 'Resources', icon: BookOpen }
      ];

    case 'investor':
      return [
        ...baseItems,
        { path: '/dashboard/investor', label: 'Investor Dashboard', icon: TrendingUp },
        {
          label: 'Investment Management',
          icon: Briefcase,
          children: [
            { path: '/dashboard/investor/portfolio', label: 'Portfolio', icon: Briefcase },
            { path: '/dashboard/investor/opportunities', label: 'Opportunities', icon: Target },
            { path: '/dashboard/investor/deals', label: 'Active Deals', icon: FileText },
            { path: '/dashboard/investor/funding', label: 'Funding', icon: DollarSign }
          ]
        },
        {
          label: 'Analysis & Research',
          icon: BarChart3,
          children: [
            { path: '/dashboard/investor/analytics', label: 'Analytics', icon: BarChart3 },
            { path: '/dashboard/investor/due-diligence', label: 'Due Diligence', icon: Eye },
            { path: '/dashboard/investor/market-analysis', label: 'Market Analysis', icon: TrendingUp },
            { path: '/dashboard/investor/reports', label: 'Reports', icon: FileText }
          ]
        },
        {
          label: 'Tools & Settings',
          icon: Settings,
          children: [
            { path: '/dashboard/investor/profile', label: 'Profile', icon: UserIcon },
            { path: '/dashboard/investor/preferences', label: 'Preferences', icon: Settings },
            { path: '/dashboard/investor/tools', label: 'Tools', icon: Wrench },
            { path: '/dashboard/investor/network', label: 'Network', icon: Users }
          ]
        }
      ];

    case 'moderator':
      return [
        ...baseItems,
        { path: '/dashboard/moderator', label: 'Moderator Panel', icon: Shield },
        { path: '/dashboard/moderator/content', label: 'Content Review', icon: Eye },
        { path: '/dashboard/moderator/reports', label: 'Reports', icon: Flag }
      ];

    default:
      return [
        ...baseItems,
        { path: '/dashboard/business-ideas', label: 'Business Ideas', icon: Lightbulb },
        { path: '/dashboard/mentorship', label: 'Mentorship', icon: Users },
        { path: '/dashboard/resources', label: 'Resources', icon: BookOpen },
        { path: '/dashboard/funding', label: 'Funding', icon: DollarSign }
      ];
  }
}

/**
 * Get role display information
 */
export function getRoleDisplayInfo(user: User | null) {
  if (!user) {
    return { title: 'Guest', description: 'Not logged in' };
  }

  // Super Admin gets special display
  if (isSuperAdmin(user)) {
    return { title: 'Super Administrator', description: 'Ultimate system control and access to all pages' };
  }

  if (user.is_admin) {
    return { title: 'Administrator', description: 'Full system access' };
  }

  const primaryRole = user.profile?.primary_role;
  if (primaryRole) {
    return { 
      title: primaryRole.display_name, 
      description: primaryRole.description 
    };
  }

  const activeRoles = user.profile?.active_roles || [];
  if (activeRoles.length > 0) {
    const role = activeRoles[0];
    return { 
      title: role.display_name, 
      description: role.description 
    };
  }

  return { title: 'User', description: 'Standard user access' };
}
