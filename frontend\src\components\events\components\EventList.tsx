import React from 'react';
import { Event } from '../../../services/api';
import { LazyComponent } from '../../ui';
import EventCard from './EventCard';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { RTLText } from '../../common';
import { formatEventData } from '../utils';
import { RefreshCw } from 'lucide-react';
interface EventListProps {
  events: Event[];
  isLoading: boolean;
  error: string | null | unknown;
  onAttend: (id: number) => void;
  onUnattend: (id: number) => void;
  onShowSampleData: () => void;
  onRefresh?: () => void;
}

const EventList: React.FC<EventListProps> = ({ events,
  isLoading,
  error,
  onAttend,
  onUnattend,
  onShowSampleData,
  onRefresh
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  // Ensure events is always an array
  const eventsArray = Array.isArray(events) ? events : [];

  if (isLoading) {
    return (
      <div className={`flex flex-col justify-center items-center py-20 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mb-6"></div>
        <RTLText as="div" align="center" className="text-gray-400 mb-4">{t('home.events.loadingEvents')}</RTLText>
        <button
          type="button"
          onClick={onShowSampleData}
          className="px-4 py-2 bg-indigo-700 hover:bg-indigo-600 rounded-lg text-white"
        >
          {t('home.events.showSampleEventsInstead')}
        </button>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-500/20 text-red-300 p-4 rounded-lg text-center mb-8">
        <div className="mb-4">{error instanceof Error ? error.message : String(error)}</div>
        <div className={`flex justify-center gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          {onRefresh && (
            <button
              type="button"
              onClick={onRefresh}
              className={`px-4 py-2 bg-indigo-700 hover:bg-indigo-600 rounded-lg text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <RefreshCw size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
              {t('common.retry')}
            </button>
          )}
          <button
            type="button"
            onClick={onShowSampleData}
            className="px-4 py-2 bg-indigo-700 hover:bg-indigo-600 rounded-lg text-white"
          >
            {t('home.events.showSampleEventsInstead')}
          </button>
        </div>
      </div>
    );
  }

  if (eventsArray.length === 0) {
    return (
      <div className="text-center py-10">
        <RTLText as="div" align="center" className="text-gray-400 text-lg mb-6">{t('home.events.noUpcomingEvents')}</RTLText>
        <button
          type="button"
          onClick={onShowSampleData}
          className="px-4 py-2 bg-indigo-700 hover:bg-indigo-600 rounded-lg text-white"
        >
          {t('home.events.showSampleEvents')}
        </button>
      </div>
    );
  }

  return (
    <div>
      {/* Header with refresh button */}
      {onRefresh && (
        <div className={`flex justify-end mb-6 ${isRTL ? "flex-row-reverse" : ""}`}>
          <button
            onClick={onRefresh}
            disabled={isLoading}
            className={`px-3 py-1.5 bg-indigo-800/50 hover:bg-indigo-700/50 rounded-lg text-white flex items-center text-sm ${isRTL ? "flex-row-reverse" : ""}`}
          >
            <RefreshCw size={14} className={`mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            {t('common.refresh')}
          </button>
        </div>
      )}

      {/* Events grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {eventsArray.map((event) => {
          const formattedEvent = formatEventData(event);
          return (
            <LazyComponent
              key={event.id}
              placeholderHeight="400px"
              threshold={0.1}
              rootMargin="100px"
            >
              <EventCard
                {...formattedEvent}
                onAttend={onAttend}
                onUnattend={onUnattend}
              />
            </LazyComponent>
          );
        })}
      </div>
    </div>
  );
};

export default EventList;
