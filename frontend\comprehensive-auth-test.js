// Comprehensive Authentication Test for All Roles
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:8000';

const TEST_USERS = {
  user: { username: 'testuser', password: 'testpass123', expectedRole: 'user' },
  mentor: { username: 'testmentor', password: 'testpass123', expectedRole: 'mentor' },
  investor: { username: 'testinvestor', password: 'testpass123', expectedRole: 'investor' },
  moderator: { username: 'testmoderator', password: 'testpass123', expectedRole: 'moderator' },
  admin: { username: 'testadmin', password: 'testpass123', expectedRole: 'admin' },
  superadmin: { username: 'testsuperadmin', password: 'testpass123', expectedRole: 'super_admin' }
};

let testResults = {
  passed: 0,
  failed: 0,
  details: []
};

async function testUserLogin(userType, userData) {
  console.log(`\n🧪 Testing ${userType} authentication...`);
  
  try {
    const response = await fetch(`${BASE_URL}/api/auth/token/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: userData.username,
        password: userData.password
      })
    });

    if (!response.ok) {
      throw new Error(`Login failed with status ${response.status}`);
    }

    const data = await response.json();
    
    // Verify we got the expected data
    if (!data.access || !data.refresh || !data.user) {
      throw new Error('Missing authentication data in response');
    }

    // Check user role
    const userRole = data.user.user_role || data.user.profile?.primary_role?.name;
    
    if (userRole !== userData.expectedRole) {
      throw new Error(`Role mismatch: expected ${userData.expectedRole}, got ${userRole}`);
    }

    console.log(`✅ ${userType} login successful`);
    console.log(`   - Role: ${userRole}`);
    console.log(`   - Permissions: ${data.user.role_permissions?.join(', ') || 'none'}`);
    console.log(`   - Is Staff: ${data.user.is_staff}`);
    console.log(`   - Is Superuser: ${data.user.is_superuser}`);

    testResults.passed++;
    testResults.details.push({
      userType,
      status: 'PASSED',
      role: userRole,
      permissions: data.user.role_permissions || [],
      isStaff: data.user.is_staff,
      isSuperuser: data.user.is_superuser
    });

    return data.access; // Return token for further testing

  } catch (error) {
    console.error(`❌ ${userType} authentication failed: ${error.message}`);
    testResults.failed++;
    testResults.details.push({
      userType,
      status: 'FAILED',
      error: error.message
    });
    return null;
  }
}

async function testDashboardAccess(userType, token) {
  if (!token) return;

  console.log(`   🔍 Testing dashboard access for ${userType}...`);
  
  try {
    // Test general dashboard endpoint
    const dashboardResponse = await fetch(`${BASE_URL}/api/dashboard/stats/`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      }
    });

    if (dashboardResponse.ok) {
      console.log(`   ✅ ${userType} can access dashboard`);
    } else {
      console.log(`   ⚠️  ${userType} dashboard access returned ${dashboardResponse.status}`);
    }

    // Test admin-specific endpoints for admin users
    if (userType === 'admin' || userType === 'superadmin') {
      const adminResponse = await fetch(`${BASE_URL}/api/admin/dashboard/`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      });

      if (adminResponse.ok) {
        console.log(`   ✅ ${userType} can access admin dashboard`);
      } else {
        console.log(`   ⚠️  ${userType} admin dashboard access returned ${adminResponse.status}`);
      }
    }

  } catch (error) {
    console.log(`   ❌ Dashboard access test failed for ${userType}: ${error.message}`);
  }
}

async function testInvalidCredentials() {
  console.log('\n🧪 Testing invalid credentials...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/auth/token/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'invaliduser',
        password: 'wrongpassword'
      })
    });

    if (response.ok) {
      throw new Error('Login should have failed with invalid credentials');
    }

    console.log('✅ Invalid credentials properly rejected');
    testResults.passed++;
    
  } catch (error) {
    console.error(`❌ Invalid credentials test failed: ${error.message}`);
    testResults.failed++;
  }
}

async function runComprehensiveAuthTests() {
  console.log('🚀 Starting Comprehensive Authentication Tests');
  console.log('=' * 60);
  
  const tokens = {};
  
  // Test each user type
  for (const [userType, userData] of Object.entries(TEST_USERS)) {
    const token = await testUserLogin(userType, userData);
    if (token) {
      tokens[userType] = token;
      await testDashboardAccess(userType, token);
    }
  }
  
  // Test invalid credentials
  await testInvalidCredentials();
  
  // Print comprehensive results
  console.log('\n📊 Authentication Test Results');
  console.log('=' * 40);
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
  
  console.log('\n📋 Detailed Results by Role:');
  testResults.details.forEach(detail => {
    const status = detail.status === 'PASSED' ? '✅' : '❌';
    console.log(`\n   ${status} ${detail.userType.toUpperCase()}`);
    if (detail.status === 'PASSED') {
      console.log(`      Role: ${detail.role}`);
      console.log(`      Permissions: ${detail.permissions.join(', ') || 'none'}`);
      console.log(`      Staff: ${detail.isStaff}`);
      console.log(`      Superuser: ${detail.isSuperuser}`);
    } else {
      console.log(`      Error: ${detail.error}`);
    }
  });
  
  console.log('\n🎯 Authentication System Assessment:');
  if (testResults.failed === 0) {
    console.log('✅ All authentication tests passed! The system is working correctly.');
  } else {
    console.log('⚠️  Some authentication tests failed. Review the errors above.');
  }
  
  return testResults;
}

// Run the tests
runComprehensiveAuthTests().catch(console.error);
