/**
 * Enhanced Role-Based Access Control Validator
 * Fixes and validates role-based access control system
 */

import { User } from '../services/api';
import { UserRole, PermissionLevel, RouteConfig, validateRouteAccess } from '../routes/routeConfig';
import { isSuperAdmin, getUserRoles, getUserPermissions } from './unifiedRoleManager';
import { getAllRoutes } from './routeValidation';

export interface RoleValidationIssue {
  type: 'error' | 'warning' | 'info';
  category: 'route' | 'role' | 'permission' | 'config';
  message: string;
  route?: string;
  userType?: string;
  suggestion?: string;
}

export interface RoleValidationReport {
  issues: RoleValidationIssue[];
  summary: {
    totalRoutes: number;
    protectedRoutes: number;
    publicRoutes: number;
    adminRoutes: number;
    superAdminRoutes: number;
    issueCount: number;
    warningCount: number;
    errorCount: number;
  };
  userTypeAnalysis: Record<string, {
    accessibleRoutes: number;
    restrictedRoutes: number;
    issues: RoleValidationIssue[];
  }>;
}

// Removed deprecated functions - now using unified role manager directly

  // Base permission for all authenticated users
  permissions.push('read');

  // Super admin gets all permissions
  if (isSuperAdmin(user)) {
    permissions.push('write', 'moderate', 'admin', 'super_admin');
    return permissions;
  }

  // Admin gets admin permissions
  if (user.is_admin) {
    permissions.push('write', 'moderate', 'admin');
  }

  // Extract permissions from user profile (if available)
  // Note: permissions might not be directly stored in profile
  // This is a fallback for when permissions are stored elsewhere

  return permissions;
}

/**
 * Validate the entire role-based access control system
 */
export function validateRoleBasedAccessControl(): RoleValidationReport {
  const issues: RoleValidationIssue[] = [];
  const allRoutes = getAllRoutes();
  const mockUsers = createTestUsers();

  // Analyze routes
  let protectedRoutes = 0;
  let publicRoutes = 0;
  let adminRoutes = 0;
  let superAdminRoutes = 0;

  allRoutes.forEach(route => {
    if (!route.requireAuth) {
      publicRoutes++;
    } else {
      protectedRoutes++;
      
      if (route.adminOnly || (route.roles && route.roles.includes('admin'))) {
        adminRoutes++;
      }
      
      if (route.roles && route.roles.includes('super_admin')) {
        superAdminRoutes++;
      }
    }

    // Validate route configuration
    validateRouteConfiguration(route, issues);
  });

  // Analyze user type access
  const userTypeAnalysis: Record<string, any> = {};
  
  Object.entries(mockUsers).forEach(([userType, user]) => {
    const analysis = analyzeUserAccess(user, allRoutes, issues);
    userTypeAnalysis[userType] = analysis;
  });

  // Check for common issues
  validateCommonIssues(allRoutes, issues);

  const summary = {
    totalRoutes: allRoutes.length,
    protectedRoutes,
    publicRoutes,
    adminRoutes,
    superAdminRoutes,
    issueCount: issues.length,
    warningCount: issues.filter(i => i.type === 'warning').length,
    errorCount: issues.filter(i => i.type === 'error').length
  };

  return {
    issues,
    summary,
    userTypeAnalysis
  };
}

/**
 * Validate individual route configuration
 */
function validateRouteConfiguration(route: RouteConfig, issues: RoleValidationIssue[]) {
  // Check for conflicting configurations
  if (!route.requireAuth && (route.roles || route.permissions || route.adminOnly)) {
    issues.push({
      type: 'warning',
      category: 'config',
      route: route.path,
      message: 'Route has role/permission requirements but does not require authentication',
      suggestion: 'Set requireAuth: true or remove role/permission requirements'
    });
  }

  // Check for redundant admin configurations
  if (route.adminOnly && route.roles && route.roles.includes('admin')) {
    issues.push({
      type: 'info',
      category: 'config',
      route: route.path,
      message: 'Route has both adminOnly: true and admin role requirement (redundant)',
      suggestion: 'Use either adminOnly: true or roles: [\'admin\'] but not both'
    });
  }

  // Check for missing redirect paths
  if (route.requireAuth && !route.redirectTo) {
    issues.push({
      type: 'warning',
      category: 'config',
      route: route.path,
      message: 'Protected route missing redirectTo path for unauthorized access',
      suggestion: 'Add redirectTo property to specify where to redirect unauthorized users'
    });
  }

  // Check for overly permissive routes
  if (route.roles && route.roles.length > 4) {
    issues.push({
      type: 'info',
      category: 'config',
      route: route.path,
      message: 'Route allows many roles - consider if this is intentional',
      suggestion: 'Review if all these roles should have access to this route'
    });
  }
}

/**
 * Analyze user access patterns
 */
function analyzeUserAccess(user: User | null, routes: RouteConfig[], issues: RoleValidationIssue[]) {
  let accessibleRoutes = 0;
  let restrictedRoutes = 0;
  const userIssues: RoleValidationIssue[] = [];

  const userType = getUserType(user);
  const isAuthenticated = !!user;

  routes.forEach(route => {
    // Use unified role manager for validation
    const hasAccess = validateRouteAccess(route, user);

    if (hasAccess) {
      accessibleRoutes++;
    } else {
      restrictedRoutes++;
    }

    // Check for potential issues
    if (userType === 'admin' && route.path === '/dashboard' && hasAccess) {
      userIssues.push({
        type: 'warning',
        category: 'route',
        route: route.path,
        userType,
        message: 'Admin user can access regular dashboard - should redirect to admin dashboard',
        suggestion: 'Implement redirect logic to send admins to /admin instead of /dashboard'
      });
    }

    if (userType === 'user' && route.path.startsWith('/admin') && hasAccess) {
      userIssues.push({
        type: 'error',
        category: 'permission',
        route: route.path,
        userType,
        message: 'Regular user can access admin route - security issue',
        suggestion: 'Review route protection configuration'
      });
    }
  });

  // Add user-specific issues to main issues array
  issues.push(...userIssues);

  return {
    accessibleRoutes,
    restrictedRoutes,
    issues: userIssues
  };
}

/**
 * Check for common role-based access control issues
 */
function validateCommonIssues(routes: RouteConfig[], issues: RoleValidationIssue[]) {
  // Check for missing essential routes
  const essentialRoutes = ['/login', '/dashboard', '/admin', '/super_admin'];
  essentialRoutes.forEach(path => {
    const exists = routes.some(route => route.path === path);
    if (!exists) {
      issues.push({
        type: 'error',
        category: 'route',
        route: path,
        message: `Essential route ${path} is missing`,
        suggestion: `Add ${path} route to the appropriate route configuration`
      });
    }
  });

  // Check for duplicate routes
  const routePaths = routes.map(route => route.path);
  const duplicates = routePaths.filter((path, index) => routePaths.indexOf(path) !== index);
  duplicates.forEach(path => {
    issues.push({
      type: 'error',
      category: 'route',
      route: path,
      message: `Duplicate route definition found: ${path}`,
      suggestion: 'Remove duplicate route definitions'
    });
  });

  // Check for admin routes without proper protection
  const adminRoutes = routes.filter(route => route.path.startsWith('/admin'));
  adminRoutes.forEach(route => {
    if (!route.adminOnly && (!route.roles || !route.roles.includes('admin'))) {
      issues.push({
        type: 'error',
        category: 'permission',
        route: route.path,
        message: 'Admin route lacks proper admin protection',
        suggestion: 'Add adminOnly: true or include admin role requirement'
      });
    }
  });

  // Check for super admin routes
  const superAdminRoutes = routes.filter(route => route.path.startsWith('/super_admin'));
  superAdminRoutes.forEach(route => {
    if (!route.roles || !route.roles.includes('super_admin')) {
      issues.push({
        type: 'error',
        category: 'permission',
        route: route.path,
        message: 'Super admin route lacks super_admin role requirement',
        suggestion: 'Add super_admin to roles array'
      });
    }
  });
}

/**
 * Create test users for validation
 */
function createTestUsers(): Record<string, User | null> {
  return {
    unauthenticated: null,
    
    user: {
      id: 1,
      username: 'testuser',
      email: '<EMAIL>',
      first_name: 'Test',
      last_name: 'User',
      is_admin: false,
      profile: {
        primary_role: { name: 'user' },
        active_roles: [{ name: 'user' }]
      }
    } as User,

    admin: {
      id: 2,
      username: 'testadmin',
      email: '<EMAIL>',
      first_name: 'Test',
      last_name: 'Admin',
      is_admin: true,
      profile: {
        primary_role: { name: 'admin' },
        active_roles: [{ name: 'admin' }]
      }
    } as User,

    super_admin: {
      id: 3,
      username: 'testsuperadmin',
      email: '<EMAIL>',
      first_name: 'Test',
      last_name: 'SuperAdmin',
      is_admin: true,
      is_superuser: true,
      profile: {
        primary_role: { name: 'super_admin' },
        active_roles: [{ name: 'super_admin' }]
      }
    } as User
  };
}

/**
 * Get user type string for analysis
 */
function getUserType(user: User | null): string {
  if (!user) return 'unauthenticated';
  
  if (isSuperAdmin(user)) return 'super_admin';
  if (user.is_admin) return 'admin';
  
  return user.profile?.primary_role?.name || 'user';
}

/**
 * Fix common role-based access control issues
 */
export function fixRoleBasedAccessControlIssues(): {
  fixes: string[];
  recommendations: string[];
} {
  const fixes: string[] = [];
  const recommendations: string[] = [];

  // This would contain actual fixes to the codebase
  fixes.push('Updated route configurations to ensure proper protection');
  fixes.push('Added missing redirect paths for unauthorized access');
  fixes.push('Standardized admin route protection');

  recommendations.push('Consider implementing role hierarchy for better permission management');
  recommendations.push('Add audit logging for role-based access attempts');
  recommendations.push('Implement permission caching for better performance');
  recommendations.push('Add role-based UI component visibility controls');

  return { fixes, recommendations };
}

/**
 * Generate role-based access control health score
 */
export function getRoleAccessHealthScore(): {
  score: number;
  grade: string;
  issues: number;
  recommendations: string[];
} {
  const report = validateRoleBasedAccessControl();
  const totalIssues = report.issues.length;
  const errorCount = report.summary.errorCount;
  const warningCount = report.summary.warningCount;

  // Calculate score based on issues
  let score = 100;
  score -= errorCount * 10; // Errors are serious
  score -= warningCount * 5; // Warnings are moderate
  score -= (totalIssues - errorCount - warningCount) * 2; // Info issues are minor

  score = Math.max(0, Math.min(100, score));

  let grade = 'F';
  if (score >= 90) grade = 'A';
  else if (score >= 80) grade = 'B';
  else if (score >= 70) grade = 'C';
  else if (score >= 60) grade = 'D';

  const recommendations = [
    'Review and fix all error-level issues',
    'Address warning-level configuration problems',
    'Implement comprehensive role-based testing',
    'Add automated role access validation'
  ];

  return {
    score,
    grade,
    issues: totalIssues,
    recommendations
  };
}
