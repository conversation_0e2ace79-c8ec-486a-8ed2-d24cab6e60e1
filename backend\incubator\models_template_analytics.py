"""
Fixed Template Analytics Models
Properly structured template analytics with correct relationships
"""

from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from .models_business_plan import BusinessPlanTemplate
import json


# Template Analytics Models
# These models track template usage, performance, and user interactions

class TemplateUsageAnalytics(models.Model):
    """Track template usage and performance metrics"""

    template = models.ForeignKey(
        BusinessPlanTemplate,
        on_delete=models.CASCADE,
        related_name='usage_analytics'
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True
    )

    # Usage tracking
    viewed_at = models.DateTimeField(auto_now_add=True)
    selected_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    completion_time = models.DurationField(null=True, blank=True)

    # User feedback
    rating = models.PositiveSmallIntegerField(
        null=True,
        blank=True,
        choices=[(i, i) for i in range(1, 6)],
        help_text="Rating from 1-5 stars"
    )
    feedback_text = models.TextField(blank=True)

    # Success metrics
    business_plan_published = models.BooleanField(default=False)
    funding_received = models.BooleanField(default=False)
    business_launched = models.BooleanField(default=False)

    # Metadata
    user_agent = models.TextField(blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    referrer = models.URLField(blank=True)

    class Meta:
        db_table = 'template_usage_analytics'
        indexes = [
            models.Index(fields=['template', 'viewed_at']),
            models.Index(fields=['user', 'viewed_at']),
            models.Index(fields=['completed_at']),
        ]

    def __str__(self):
        return f"Usage: {self.template.name} by {self.user.username if self.user else 'Anonymous'}"


class TemplatePerformanceMetrics(models.Model):
    """Aggregated performance metrics for templates"""

    template = models.OneToOneField(
        BusinessPlanTemplate,
        on_delete=models.CASCADE,
        related_name='performance_metrics'
    )

    # Usage metrics
    total_views = models.IntegerField(default=0)
    total_selections = models.IntegerField(default=0)
    total_completions = models.IntegerField(default=0)
    unique_users = models.IntegerField(default=0)

    # Performance ratios
    selection_rate = models.FloatField(default=0.0)  # selections / views
    completion_rate = models.FloatField(default=0.0)  # completions / selections
    average_completion_time = models.DurationField(null=True, blank=True)

    # Quality metrics
    average_rating = models.FloatField(default=0.0)
    total_ratings = models.IntegerField(default=0)
    net_promoter_score = models.FloatField(default=0.0)

    # Success metrics
    business_plans_published = models.IntegerField(default=0)
    funding_success_rate = models.FloatField(default=0.0)
    business_launch_rate = models.FloatField(default=0.0)

    # Last updated
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'template_performance_metrics'

    def __str__(self):
        return f"Metrics: {self.template.name}"


class TemplateSectionAnalytics(models.Model):
    """Track analytics for individual template sections"""

    template = models.ForeignKey(
        BusinessPlanTemplate,
        on_delete=models.CASCADE,
        related_name='section_analytics'
    )
    section_key = models.CharField(max_length=100)
    section_title = models.CharField(max_length=200)

    # Section performance
    total_views = models.IntegerField(default=0)
    total_completions = models.IntegerField(default=0)
    average_time_spent = models.DurationField(null=True, blank=True)
    completion_rate = models.FloatField(default=0.0)

    # User interaction
    ai_assistance_used = models.IntegerField(default=0)
    content_regenerated = models.IntegerField(default=0)
    section_customized = models.IntegerField(default=0)

    # Quality indicators
    average_content_length = models.IntegerField(default=0)
    user_satisfaction_score = models.FloatField(default=0.0)

    # Last updated
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'template_section_analytics'
        unique_together = ['template', 'section_key']
        indexes = [
            models.Index(fields=['template', 'completion_rate']),
            models.Index(fields=['section_key', 'completion_rate']),
        ]

    def __str__(self):
        return f"Section Analytics: {self.section_title} ({self.template.name})"


class UserTemplateInteraction(models.Model):
    """Track detailed user interactions with templates"""

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    template = models.ForeignKey(BusinessPlanTemplate, on_delete=models.CASCADE)
    session_id = models.CharField(max_length=100)

    # Interaction details
    action_type = models.CharField(max_length=50, choices=[
        ('view', 'View Template'),
        ('select', 'Select Template'),
        ('start', 'Start Working'),
        ('save_section', 'Save Section'),
        ('complete_section', 'Complete Section'),
        ('use_ai', 'Use AI Assistance'),
        ('customize', 'Customize Section'),
        ('export', 'Export Business Plan'),
        ('share', 'Share Business Plan'),
        ('rate', 'Rate Template'),
        ('feedback', 'Provide Feedback'),
    ])

    section_key = models.CharField(max_length=100, blank=True)
    action_data = models.JSONField(default=dict, blank=True)

    # Timing
    timestamp = models.DateTimeField(auto_now_add=True)
    duration = models.DurationField(null=True, blank=True)

    # Context
    device_type = models.CharField(max_length=20, blank=True)
    browser = models.CharField(max_length=50, blank=True)

    class Meta:
        db_table = 'user_template_interaction'
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['template', 'action_type']),
            models.Index(fields=['session_id']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.action_type} - {self.template.name}"


class TemplateRecommendation(models.Model):
    """AI-powered template recommendations for users"""

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    recommended_template = models.ForeignKey(
        BusinessPlanTemplate,
        on_delete=models.CASCADE,
        related_name='recommendations'
    )

    # Recommendation details
    recommendation_score = models.FloatField(default=0.0)
    recommendation_reason = models.TextField(blank=True)

    # User interaction
    viewed = models.BooleanField(default=False)
    selected = models.BooleanField(default=False)
    dismissed = models.BooleanField(default=False)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    viewed_at = models.DateTimeField(null=True, blank=True)
    selected_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'template_recommendation'
        unique_together = ['user', 'recommended_template']
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['recommendation_score']),
        ]

    def __str__(self):
        return f"Recommendation: {self.recommended_template.name} for {self.user.username}"


class TemplateABTest(models.Model):
    """A/B testing for template variations"""

    test_name = models.CharField(max_length=100)
    description = models.TextField(blank=True)

    # Test configuration
    original_template = models.ForeignKey(
        BusinessPlanTemplate,
        on_delete=models.CASCADE,
        related_name='ab_tests_original'
    )
    variant_template = models.ForeignKey(
        BusinessPlanTemplate,
        on_delete=models.CASCADE,
        related_name='ab_tests_variant'
    )

    # Test parameters
    traffic_split = models.FloatField(default=50.0, help_text="Percentage for variant (0-100)")
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    is_active = models.BooleanField(default=True)

    # Results
    original_conversions = models.IntegerField(default=0)
    variant_conversions = models.IntegerField(default=0)
    original_views = models.IntegerField(default=0)
    variant_views = models.IntegerField(default=0)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'template_ab_test'
        indexes = [
            models.Index(fields=['is_active', 'start_date']),
            models.Index(fields=['test_name']),
        ]

    def __str__(self):
        return f"A/B Test: {self.test_name}"


