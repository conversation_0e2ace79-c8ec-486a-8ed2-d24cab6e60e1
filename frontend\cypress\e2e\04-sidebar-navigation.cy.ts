describe('Sidebar Navigation', () => {
  beforeEach(() => {
    cy.intercept('POST', '**/api/auth/login/', { fixture: 'login-response.json' }).as('login')
    cy.intercept('GET', '**/api/auth/user/', { fixture: 'user.json' }).as('getUser')
  })

  describe('User Role Sidebar', () => {
    beforeEach(() => {
      cy.loginAs('user')
      cy.visit('/dashboard')
    })

    it('should display correct navigation items for user role', () => {
      const expectedNavItems = [
        'Dashboard',
        'Business Plans',
        'Mentorship',
        'Funding',
        'Profile',
        'Settings'
      ]

      cy.getByTestId('sidebar').should('be.visible')
      
      expectedNavItems.forEach(item => {
        cy.getByTestId('sidebar').should('contain.text', item)
      })

      // Should NOT show admin items
      cy.getByTestId('sidebar').should('not.contain.text', 'User Management')
      cy.getByTestId('sidebar').should('not.contain.text', 'Analytics')
      cy.getByTestId('sidebar').should('not.contain.text', 'System Settings')
    })

    it('should navigate correctly between pages', () => {
      // Test Dashboard navigation
      cy.navigateViaSidebar('Dashboard')
      cy.url().should('include', '/dashboard')
      cy.getByTestId('nav-dashboard').should('have.class', 'active')

      // Test Business Plans navigation
      cy.navigateViaSidebar('Business Plans')
      cy.url().should('include', '/business-plans')
      cy.getByTestId('nav-business-plans').should('have.class', 'active')
      cy.getByTestId('nav-dashboard').should('not.have.class', 'active')

      // Test Mentorship navigation
      cy.navigateViaSidebar('Mentorship')
      cy.url().should('include', '/mentorship')
      cy.getByTestId('nav-mentorship').should('have.class', 'active')

      // Test Funding navigation
      cy.navigateViaSidebar('Funding')
      cy.url().should('include', '/funding')
      cy.getByTestId('nav-funding').should('have.class', 'active')

      // Test Profile navigation
      cy.navigateViaSidebar('Profile')
      cy.url().should('include', '/profile')
      cy.getByTestId('nav-profile').should('have.class', 'active')
    })

    it('should show user information in sidebar', () => {
      cy.getByTestId('sidebar-user-info').should('be.visible')
      cy.getByTestId('sidebar-user-info').should('contain.text', 'Test User')
      cy.getByTestId('sidebar-user-info').should('contain.text', '<EMAIL>')
      cy.getByTestId('user-role-badge').should('contain.text', 'User')
    })

    it('should collapse and expand correctly', () => {
      // Test collapse
      cy.getByTestId('sidebar-toggle').click()
      cy.getByTestId('sidebar').should('have.class', 'collapsed')
      
      // Navigation items should show only icons
      cy.getByTestId('nav-dashboard').within(() => {
        cy.get('[data-testid="nav-icon"]').should('be.visible')
        cy.get('[data-testid="nav-text"]').should('not.be.visible')
      })

      // Test expand
      cy.getByTestId('sidebar-toggle').click()
      cy.getByTestId('sidebar').should('not.have.class', 'collapsed')
      
      // Navigation items should show icons and text
      cy.getByTestId('nav-dashboard').within(() => {
        cy.get('[data-testid="nav-icon"]').should('be.visible')
        cy.get('[data-testid="nav-text"]').should('be.visible')
      })
    })

    it('should show tooltips when collapsed', () => {
      cy.getByTestId('sidebar-toggle').click()
      cy.getByTestId('sidebar').should('have.class', 'collapsed')

      // Hover over navigation item to show tooltip
      cy.getByTestId('nav-dashboard').trigger('mouseover')
      cy.getByTestId('nav-tooltip').should('be.visible')
      cy.getByTestId('nav-tooltip').should('contain.text', 'Dashboard')
    })
  })

  describe('Admin Role Sidebar', () => {
    beforeEach(() => {
      const adminUser = require('../fixtures/admin.json')
      const adminLoginResponse = {
        ...require('../fixtures/login-response.json'),
        user: adminUser
      }
      
      cy.intercept('POST', '**/api/auth/login/', { body: adminLoginResponse }).as('adminLogin')
      cy.intercept('GET', '**/api/auth/user/', { body: adminUser }).as('getAdmin')
      
      cy.loginAs('admin')
      cy.visit('/admin/dashboard')
    })

    it('should display admin navigation items', () => {
      const expectedAdminNavItems = [
        'Dashboard',
        'User Management',
        'Business Plans',
        'Mentorship',
        'Funding',
        'Analytics',
        'Reports',
        'Settings'
      ]

      expectedAdminNavItems.forEach(item => {
        cy.getByTestId('sidebar').should('contain.text', item)
      })

      // Should NOT show super admin items
      cy.getByTestId('sidebar').should('not.contain.text', 'System Configuration')
      cy.getByTestId('sidebar').should('not.contain.text', 'Admin Management')
    })

    it('should show admin badge', () => {
      cy.getByTestId('user-role-badge').should('contain.text', 'Admin')
      cy.getByTestId('user-role-badge').should('have.class', 'admin-badge')
    })

    it('should navigate to admin-specific pages', () => {
      cy.navigateViaSidebar('User Management')
      cy.url().should('include', '/admin/users')

      cy.navigateViaSidebar('Analytics')
      cy.url().should('include', '/admin/analytics')

      cy.navigateViaSidebar('Reports')
      cy.url().should('include', '/admin/reports')
    })
  })

  describe('Super Admin Role Sidebar', () => {
    beforeEach(() => {
      const superAdminUser = {
        ...require('../fixtures/admin.json'),
        role: 'superadmin',
        is_superuser: true
      }
      
      const superAdminLoginResponse = {
        ...require('../fixtures/login-response.json'),
        user: superAdminUser
      }
      
      cy.intercept('POST', '**/api/auth/login/', { body: superAdminLoginResponse }).as('superAdminLogin')
      cy.intercept('GET', '**/api/auth/user/', { body: superAdminUser }).as('getSuperAdmin')
      
      cy.loginAs('superadmin')
      cy.visit('/superadmin/dashboard')
    })

    it('should display super admin navigation items', () => {
      const expectedSuperAdminNavItems = [
        'Dashboard',
        'Admin Management',
        'System Configuration',
        'Global Analytics',
        'System Logs',
        'Maintenance'
      ]

      expectedSuperAdminNavItems.forEach(item => {
        cy.getByTestId('sidebar').should('contain.text', item)
      })
    })

    it('should show super admin badge', () => {
      cy.getByTestId('user-role-badge').should('contain.text', 'Super Admin')
      cy.getByTestId('user-role-badge').should('have.class', 'superadmin-badge')
    })
  })

  describe('Mentor Role Sidebar', () => {
    beforeEach(() => {
      const mentorUser = {
        ...require('../fixtures/user.json'),
        role: 'mentor'
      }
      
      const mentorLoginResponse = {
        ...require('../fixtures/login-response.json'),
        user: mentorUser
      }
      
      cy.intercept('POST', '**/api/auth/login/', { body: mentorLoginResponse }).as('mentorLogin')
      cy.intercept('GET', '**/api/auth/user/', { body: mentorUser }).as('getMentor')
      
      cy.loginAs('mentor')
      cy.visit('/mentor/dashboard')
    })

    it('should display mentor navigation items', () => {
      const expectedMentorNavItems = [
        'Dashboard',
        'Mentees',
        'Sessions',
        'Resources',
        'Calendar',
        'Profile'
      ]

      expectedMentorNavItems.forEach(item => {
        cy.getByTestId('sidebar').should('contain.text', item)
      })
    })

    it('should show mentor badge', () => {
      cy.getByTestId('user-role-badge').should('contain.text', 'Mentor')
      cy.getByTestId('user-role-badge').should('have.class', 'mentor-badge')
    })
  })

  describe('Responsive Behavior', () => {
    beforeEach(() => {
      cy.loginAs('user')
      cy.visit('/dashboard')
    })

    it('should adapt to mobile viewport', () => {
      cy.viewport(375, 667) // iPhone SE

      // Sidebar should be hidden by default on mobile
      cy.getByTestId('sidebar').should('not.be.visible')
      cy.getByTestId('mobile-menu-button').should('be.visible')

      // Open mobile menu
      cy.getByTestId('mobile-menu-button').click()
      cy.getByTestId('mobile-sidebar').should('be.visible')

      // Should show overlay
      cy.getByTestId('sidebar-overlay').should('be.visible')

      // Close by clicking overlay
      cy.getByTestId('sidebar-overlay').click()
      cy.getByTestId('mobile-sidebar').should('not.be.visible')
    })

    it('should adapt to tablet viewport', () => {
      cy.viewport(768, 1024) // iPad

      // Sidebar should be visible but collapsible
      cy.getByTestId('sidebar').should('be.visible')
      cy.getByTestId('mobile-menu-button').should('not.be.visible')

      // Should be able to collapse
      cy.getByTestId('sidebar-toggle').click()
      cy.getByTestId('sidebar').should('have.class', 'collapsed')
    })

    it('should adapt to desktop viewport', () => {
      cy.viewport(1920, 1080) // Desktop

      // Sidebar should be fully visible
      cy.getByTestId('sidebar').should('be.visible')
      cy.getByTestId('sidebar').should('not.have.class', 'collapsed')
      cy.getByTestId('mobile-menu-button').should('not.be.visible')
    })
  })

  describe('Internationalization', () => {
    beforeEach(() => {
      cy.loginAs('user')
      cy.visit('/dashboard')
    })

    it('should support Arabic language', () => {
      cy.switchLanguage('ar')

      // Check RTL layout
      cy.get('html').should('have.attr', 'dir', 'rtl')
      cy.getByTestId('sidebar').should('have.class', 'rtl')

      // Navigation items should be in Arabic
      cy.getByTestId('sidebar').should('contain.text', 'لوحة التحكم') // Dashboard in Arabic
      cy.getByTestId('sidebar').should('contain.text', 'خطط الأعمال') // Business Plans in Arabic
    })

    it('should maintain navigation state when switching languages', () => {
      // Navigate to business plans
      cy.navigateViaSidebar('Business Plans')
      cy.url().should('include', '/business-plans')

      // Switch to Arabic
      cy.switchLanguage('ar')

      // Should still be on business plans page
      cy.url().should('include', '/business-plans')
      cy.getByTestId('nav-business-plans').should('have.class', 'active')
    })
  })

  describe('Accessibility', () => {
    beforeEach(() => {
      cy.loginAs('user')
      cy.visit('/dashboard')
    })

    it('should be fully accessible', () => {
      cy.checkA11y()

      // Check ARIA attributes
      cy.getByTestId('sidebar').should('have.attr', 'role', 'navigation')
      cy.getByTestId('sidebar').should('have.attr', 'aria-label', 'Main navigation')

      // Check navigation items
      cy.getByTestId('nav-dashboard').should('have.attr', 'role', 'menuitem')
      cy.getByTestId('nav-dashboard').should('have.attr', 'aria-current', 'page')
    })

    it('should support keyboard navigation', () => {
      // Focus should start on first navigation item
      cy.getByTestId('sidebar').focus()
      cy.getByTestId('nav-dashboard').should('be.focused')

      // Arrow keys should navigate between items
      cy.realPress('ArrowDown')
      cy.getByTestId('nav-business-plans').should('be.focused')

      cy.realPress('ArrowDown')
      cy.getByTestId('nav-mentorship').should('be.focused')

      // Enter should activate navigation
      cy.realPress('Enter')
      cy.url().should('include', '/mentorship')
    })

    it('should support screen readers', () => {
      // Check for screen reader announcements
      cy.getByTestId('nav-dashboard').should('have.attr', 'aria-label', 'Dashboard - Current page')
      
      // Check for live region updates
      cy.navigateViaSidebar('Business Plans')
      cy.getByTestId('nav-announcement').should('contain.text', 'Navigated to Business Plans')
    })

    it('should have proper focus management', () => {
      // When sidebar is collapsed, focus should be managed properly
      cy.getByTestId('sidebar-toggle').click()
      cy.getByTestId('sidebar').should('have.class', 'collapsed')

      // Focus should still be visible and manageable
      cy.getByTestId('nav-dashboard').focus()
      cy.getByTestId('nav-dashboard').should('be.focused')
      cy.getByTestId('nav-dashboard').should('have.attr', 'aria-expanded', 'false')
    })
  })

  describe('Performance', () => {
    it('should load sidebar quickly', () => {
      cy.startPerformanceTimer('sidebar-load')
      cy.loginAs('user')
      cy.visit('/dashboard')
      cy.getByTestId('sidebar').should('be.visible')
      cy.endPerformanceTimer('sidebar-load', 2000)
    })

    it('should handle navigation smoothly', () => {
      cy.loginAs('user')
      cy.visit('/dashboard')

      cy.startPerformanceTimer('navigation-performance')
      
      // Navigate between multiple pages quickly
      cy.navigateViaSidebar('Business Plans')
      cy.navigateViaSidebar('Mentorship')
      cy.navigateViaSidebar('Funding')
      cy.navigateViaSidebar('Dashboard')
      
      cy.endPerformanceTimer('navigation-performance', 3000)
    })
  })

  describe('State Management', () => {
    beforeEach(() => {
      cy.loginAs('user')
      cy.visit('/dashboard')
    })

    it('should persist sidebar state across page reloads', () => {
      // Collapse sidebar
      cy.getByTestId('sidebar-toggle').click()
      cy.getByTestId('sidebar').should('have.class', 'collapsed')

      // Reload page
      cy.reload()
      cy.waitForPageLoad()

      // Sidebar should remain collapsed
      cy.getByTestId('sidebar').should('have.class', 'collapsed')
    })

    it('should sync with Redux state', () => {
      // Check initial Redux state
      cy.getReduxState('ui').then((uiState) => {
        expect(uiState.sidebarCollapsed).to.be.false
      })

      // Collapse sidebar
      cy.getByTestId('sidebar-toggle').click()

      // Check updated Redux state
      cy.getReduxState('ui').then((uiState) => {
        expect(uiState.sidebarCollapsed).to.be.true
      })
    })

    it('should handle role changes dynamically', () => {
      // Start as user
      cy.getByTestId('sidebar').should('not.contain.text', 'User Management')

      // Simulate role upgrade to admin
      const adminUser = require('../fixtures/admin.json')
      cy.intercept('GET', '**/api/auth/user/', { body: adminUser }).as('getUpgradedUser')
      
      // Trigger user data refresh
      cy.getByTestId('user-profile').click()
      cy.getByTestId('refresh-profile').click()
      cy.wait('@getUpgradedUser')

      // Sidebar should update to show admin items
      cy.getByTestId('sidebar').should('contain.text', 'User Management')
      cy.getByTestId('user-role-badge').should('contain.text', 'Admin')
    })
  })
})
