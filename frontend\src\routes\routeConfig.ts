import { ComponentType, ReactElement } from 'react';
import { UserRole, PermissionLevel } from '../utils/unifiedRoleManager';
import { getRouteConfig, ROUTE_ROLE_MAPPINGS } from '../config/centralizedRoleRouteMapping';

// Route configuration interface
export interface RouteConfig {
  path: string;
  component: ComponentType<any>;
  exact?: boolean;
  roles?: UserRole[]; // Allowed roles for this route
  permissions?: PermissionLevel[]; // Required permission levels
  requireAuth?: boolean; // Whether authentication is required
  adminOnly?: boolean; // Shortcut for admin-only routes
  loadingMessage?: string; // Custom loading message
  redirectTo?: string; // Redirect path for unauthorized access
  children?: RouteConfig[]; // Nested routes
  layout?: 'public' | 'authenticated' | 'ai-focused'; // Layout type for this route
}

// Route group configuration
export interface RouteGroup {
  name: string;
  basePath?: string;
  routes: RouteConfig[];
  defaultRole?: UserRole;
  defaultPermission?: PermissionLevel;
}

// Helper function to create route config
export const createRoute = (config: RouteConfig): RouteConfig => ({
  exact: true,
  requireAuth: true,
  loadingMessage: 'Loading...',
  ...config,
});

// ✅ UNIFIED: Helper function to create admin route (no hardcoded arrays)
export const createAdminRoute = (
  path: string,
  component: ComponentType<any>,
  loadingMessage?: string
): RouteConfig => createRoute({
  path,
  component,
  roles: ['admin', 'super_admin'], // ✅ UNIFIED: Uses centralized role mapping
  permissions: ['admin'], // ✅ UNIFIED: Simplified permission requirement
  layout: 'authenticated',
  loadingMessage: loadingMessage || 'Loading admin panel...',
  redirectTo: '/dashboard'
});

// Helper function to create Super Admin only route
export const createSuperAdminRoute = (
  path: string,
  component: ComponentType<any>,
  loadingMessage?: string
): RouteConfig => createRoute({
  path,
  component,
  roles: ['super_admin'], // Only super_admin
  permissions: ['super_admin'], // Only super_admin permissions
  layout: 'authenticated',
  loadingMessage: loadingMessage || 'Loading Super Admin panel...',
  redirectTo: '/super_admin'
});

// Helper function to create user route
export const createUserRoute = (
  path: string,
  component: ComponentType<any>,
  loadingMessage?: string,
  allowedRoles?: UserRole[]
): RouteConfig => createRoute({
  path,
  component,
  roles: allowedRoles || ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'],
  permissions: ['read'],
  layout: 'authenticated',
  loadingMessage: loadingMessage || 'Loading...',
  redirectTo: '/login'
});

// Helper function to create AI-focused route
export const createAIRoute = (
  path: string,
  component: ComponentType<any>,
  loadingMessage?: string,
  allowedRoles?: UserRole[]
): RouteConfig => createRoute({
  path,
  component,
  roles: allowedRoles || ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'],
  permissions: ['read'],
  layout: 'ai-focused',
  loadingMessage: loadingMessage || 'Loading AI Assistant...',
  redirectTo: '/login'
});

// Helper function to create role-based AI route
export const createRoleAIRoute = (
  path: string,
  component: ComponentType<any>,
  roles: UserRole[],
  permissions: PermissionLevel[],
  loadingMessage?: string
): RouteConfig => createRoute({
  path,
  component,
  roles,
  permissions,
  layout: 'ai-focused',
  loadingMessage: loadingMessage || 'Loading AI Assistant...',
  redirectTo: '/login'
});

// Helper function to create public route
export const createPublicRoute = (
  path: string,
  component: ComponentType<any>,
  loadingMessage?: string
): RouteConfig => ({
  path,
  component,
  exact: true,
  requireAuth: false,
  layout: 'public',
  loadingMessage: loadingMessage || 'Loading...'
});

// Helper function to create auth route (login, register) - no layout wrapper
export const createAuthRoute = (
  path: string,
  component: ComponentType<any>,
  loadingMessage?: string
): RouteConfig => ({
  path,
  component,
  exact: true,
  requireAuth: false,
  layout: undefined, // No layout wrapper
  loadingMessage: loadingMessage || 'Loading...'
});

// Helper function to create role-specific route
export const createRoleRoute = (
  path: string,
  component: ComponentType<any>,
  roles: UserRole[],
  permissions?: PermissionLevel[],
  loadingMessage?: string
): RouteConfig => createRoute({
  path,
  component,
  roles,
  permissions: permissions || ['read'],
  layout: 'authenticated',
  loadingMessage: loadingMessage || 'Loading...',
  redirectTo: '/dashboard'
});

// Import unified role manager functions
import { canAccessRoute as unifiedCanAccessRoute } from '../utils/unifiedRoleManager';
import { User } from '../services/api';

// Route validation helper - now uses unified role manager
export const validateRouteAccess = (
  route: RouteConfig,
  user: User | null
): boolean => {
  return unifiedCanAccessRoute(
    user,
    route.roles,
    route.permissions,
    route.requireAuth !== false
  );
};



// Get redirect path for unauthorized access
export const getRedirectPath = (route: RouteConfig, isAuthenticated: boolean): string => {
  if (!isAuthenticated) {
    return '/login'; // ✅ UNIFIED: All users use the same login page
  }

  return route.redirectTo || '/dashboard';
};

/**
 * ✅ UNIFIED ROUTE CREATION
 * Creates routes using centralized role-route mapping
 * Eliminates hardcoded role arrays and ensures consistency
 */
export const createUnifiedRoute = (
  path: string,
  component: ComponentType<any>,
  loadingMessage?: string
): RouteConfig => {
  // Get role configuration from centralized mapping
  const roleConfig = getRouteConfig(path);

  if (!roleConfig) {
    console.warn(`⚠️ Route ${path} not found in centralized role mapping - using defaults`);
    return createRoute({
      path,
      component,
      roles: ['user'], // Default fallback
      permissions: ['read'],
      layout: 'authenticated',
      loadingMessage: loadingMessage || 'Loading...',
      redirectTo: '/dashboard'
    });
  }

  // Create route using centralized configuration
  return createRoute({
    path,
    component,
    roles: roleConfig.allowedRoles,
    permissions: roleConfig.requiredPermissions,
    requireAuth: roleConfig.requireAuth,
    layout: roleConfig.category === 'public' ? 'public' :
           roleConfig.category === 'auth' ? undefined :
           roleConfig.category === 'ai' ? 'ai-focused' : 'authenticated',
    loadingMessage: loadingMessage || 'Loading...',
    redirectTo: roleConfig.requireAuth ? '/dashboard' : undefined
  });
};

/**
 * ✅ BATCH ROUTE CREATION
 * Creates multiple routes from centralized mapping
 */
export const createRoutesFromMapping = (
  routeComponents: Record<string, ComponentType<any>>,
  loadingMessages?: Record<string, string>
): RouteConfig[] => {
  return ROUTE_ROLE_MAPPINGS
    .filter(mapping => routeComponents[mapping.path])
    .map(mapping => createUnifiedRoute(
      mapping.path,
      routeComponents[mapping.path],
      loadingMessages?.[mapping.path]
    ));
};
