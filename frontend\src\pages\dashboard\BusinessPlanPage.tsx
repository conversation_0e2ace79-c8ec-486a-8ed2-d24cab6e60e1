import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useParams, useNavigate } from 'react-router-dom';
import { Plus, Search, Filter, Activity, ArrowRight } from 'lucide-react';
import {
  BusinessPlanCreateForm,
  BusinessPlanList,
  BusinessPlanEditor,
  BusinessPlanDeleteModal
} from '../../components/business-plans';
import { BusinessPlan } from '../../services/businessPlanApi';
import { useBusinessPlans } from '../../hooks/useBusinessPlans';

const BusinessPlanPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // Use real business plans hook
  const {
    businessPlans,
    isLoading: loading,
    error,
    createBusinessPlan,
    updateBusinessPlan,
    deleteBusinessPlan,
    refetch
  } = useBusinessPlans();

  // State management
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('updated_at');
  const [planToDelete, setPlanToDelete] = useState<BusinessPlan | null>(null);

  // Event handlers
  const handleCreateSuccess = (businessPlan: BusinessPlan) => {
    setShowCreateForm(false);
    refetch(); // Refresh the list with real data
  };

  const handleEdit = (plan: BusinessPlan) => {
    navigate(`/dashboard/business-plans/${plan.id}`);
  };

  const handleView = (plan: BusinessPlan) => {
    navigate(`/dashboard/business-plans/${plan.id}`);
  };

  const handleDelete = (plan: BusinessPlan) => {
    setPlanToDelete(plan);
  };

  const handleDeleteSuccess = () => {
    if (planToDelete) {
      deleteBusinessPlan(planToDelete.id);
      setPlanToDelete(null);
    }
  };

  // If viewing a specific plan
  if (id) {
    return (
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <BusinessPlanEditor
              businessPlanId={parseInt(id)}
              onBack={() => navigate('/dashboard/business-plans')}
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="max-w-7xl mx-auto w-full">
          <div className="p-6 space-y-6">
            {/* Header */}
            <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
              <div>
                <h1 className="text-2xl font-bold text-white mb-2">
                  {t('businessPlan.title')}
                </h1>
                <p className="text-gray-300">
                  {t('businessPlan.description')}
                </p>
              </div>
              <div className={`flex items-center space-x-3 mt-4 sm:mt-0 ${isRTL ? 'space-x-reverse' : ''}`}>
                <button
                  onClick={() => setShowAnalytics(!showAnalytics)}
                  className={`p-2 rounded-lg transition-colors ${showAnalytics ? 'bg-purple-600 hover:bg-purple-700' : 'bg-indigo-900/50 hover:bg-indigo-800/50'}`}
                  title={t('analytics.viewAnalytics', 'View Analytics')}
                >
                  <Activity size={18} className="text-gray-300" />
                </button>
                <button
                  onClick={() => setShowCreateForm(true)}
                  className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
                >
                  <Plus size={18} />
                  <span>{t('businessPlan.createNew')}</span>
                </button>
              </div>
            </div>

            {/* Create Form Modal */}
            {showCreateForm && (
              <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                <div className="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                  <BusinessPlanCreateForm
                    onSuccess={handleCreateSuccess}
                    onCancel={() => setShowCreateForm(false)}
                  />
                </div>
              </div>
            )}

            {/* Delete Modal */}
            {planToDelete && (
              <BusinessPlanDeleteModal
                businessPlan={planToDelete}
                isOpen={true}
                onClose={() => setPlanToDelete(null)}
                onSuccess={handleDeleteSuccess}
              />
            )}

            {/* Analytics Section */}
            {showAnalytics && (
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <h3 className="text-lg font-semibold text-white mb-4">
                  {t('analytics.businessPlanAnalytics', 'Business Plan Analytics')}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-indigo-900/30 rounded-lg p-4">
                    <div className="text-2xl font-bold text-white">{businessPlans.length}</div>
                    <div className="text-gray-300">{t('analytics.totalPlans', 'Total Plans')}</div>
                  </div>
                  <div className="bg-green-900/30 rounded-lg p-4">
                    <div className="text-2xl font-bold text-white">
                      {businessPlans.filter(p => p.status === 'completed').length}
                    </div>
                    <div className="text-gray-300">{t('analytics.completedPlans', 'Completed')}</div>
                  </div>
                  <div className="bg-yellow-900/30 rounded-lg p-4">
                    <div className="text-2xl font-bold text-white">
                      {Math.round(businessPlans.reduce((acc, p) => acc + p.completion_percentage, 0) / businessPlans.length || 0)}%
                    </div>
                    <div className="text-gray-300">{t('analytics.avgCompletion', 'Avg Completion')}</div>
                  </div>
                </div>
              </div>
            )}

            {/* Search and Filters */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
              <div className={`flex flex-col sm:flex-row gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className="flex-1 relative">
                  <Search size={18} className={`absolute top-3 text-gray-400 ${isRTL ? 'right-3' : 'left-3'}`} />
                  <input
                    type="text"
                    placeholder={t('common.search', 'Search...')}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className={`w-full bg-indigo-900/50 border border-indigo-700 rounded-lg py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 ${isRTL ? 'pr-10 pl-4' : 'pl-10 pr-4'}`}
                  />
                </div>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="bg-indigo-900/50 border border-indigo-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option value="all">{t('common.allStatuses', 'All Statuses')}</option>
                  <option value="draft">{t('status.draft', 'Draft')}</option>
                  <option value="in_review">{t('status.inReview', 'In Review')}</option>
                  <option value="completed">{t('status.completed', 'Completed')}</option>
                </select>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="bg-indigo-900/50 border border-indigo-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option value="updated_at">{t('sort.lastUpdated', 'Last Updated')}</option>
                  <option value="created_at">{t('sort.dateCreated', 'Date Created')}</option>
                  <option value="title">{t('sort.title', 'Title')}</option>
                  <option value="completion">{t('sort.completion', 'Completion')}</option>
                </select>
              </div>
            </div>

            {/* Business Plans List */}
            <BusinessPlanList
              onCreateNew={() => setShowCreateForm(true)}
              onEdit={handleEdit}
              onView={handleView}
              onDelete={handleDelete}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessPlanPage;
