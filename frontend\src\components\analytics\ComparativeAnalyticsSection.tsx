import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  BarChart2,
  ChevronDown,
  ChevronUp,
  ArrowUpRight,
  ArrowDownRight,
  Users
} from 'lucide-react';
import { ComparativeAnalytics } from '../../services/analyticsApi';
import { formatPercentage } from '../../utils/exportUtils';
import {
  BarChart, Bar, XAxis, YAxis, CartesianGrid,
  Tooltip, Legend, ResponsiveContainer, RadarChart,
  Radar, PolarGrid, PolarAngleAxis, PolarRadiusAxis
} from 'recharts';

interface ComparativeAnalyticsSectionProps {
  comparativeAnalytics: ComparativeAnalytics | null;
  expanded: boolean;
  onToggle: () => void;
}

const ComparativeAnalyticsSection: React.FC<ComparativeAnalyticsSectionProps> = ({
  comparativeAnalytics,
  expanded,
  onToggle
}) => {
  const { t } = useTranslation();

  if (!comparativeAnalytics) {
    return null;
  }

  // Prepare comparative data for radar chart
  const radarData = comparativeAnalytics.percentile_rankings?.percentiles ? [
    {
      subject: 'Progress',
      A: comparativeAnalytics.percentile_rankings.percentiles.progress_rate,
      fullMark: 100
    },
    {
      subject: 'Milestones',
      A: comparativeAnalytics.percentile_rankings.percentiles.milestone_completion_rate,
      fullMark: 100
    },
    {
      subject: 'Goals',
      A: comparativeAnalytics.percentile_rankings.percentiles.goal_achievement_rate,
      fullMark: 100
    },
    {
      subject: 'Team',
      A: comparativeAnalytics.percentile_rankings.percentiles.team_size,
      fullMark: 100
    },
    {
      subject: 'Mentorship',
      A: comparativeAnalytics.percentile_rankings.percentiles.mentor_engagement,
      fullMark: 100
    }
  ] : [];

  // Prepare data for comparison bar chart
  const barChartData = comparativeAnalytics.industry_averages?.averages ? [
    {
      name: 'Progress Rate',
      yours: comparativeAnalytics.percentile_rankings.percentiles.progress_rate,
      average: comparativeAnalytics.industry_averages.averages.progress_rate
    },
    {
      name: 'Milestone %',
      yours: comparativeAnalytics.percentile_rankings.percentiles.milestone_completion_rate,
      average: comparativeAnalytics.industry_averages.averages.milestone_completion_rate
    },
    {
      name: 'Goal %',
      yours: comparativeAnalytics.percentile_rankings.percentiles.goal_achievement_rate,
      average: comparativeAnalytics.industry_averages.averages.goal_achievement_rate
    },
    {
      name: 'Team Size',
      yours: comparativeAnalytics.percentile_rankings.percentiles.team_size,
      average: comparativeAnalytics.industry_averages.averages.team_size
    },
    {
      name: 'Mentor Eng.',
      yours: comparativeAnalytics.percentile_rankings.percentiles.mentor_engagement,
      average: comparativeAnalytics.industry_averages.averages.mentor_engagement
    }
  ] : [];

  return (
    <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-6 border border-indigo-800/50">
      <div
        className="flex items-center justify-between cursor-pointer"
        onClick={onToggle}
      >
        <h2 className="text-lg font-bold flex items-center">
          <BarChart2 size={18} className="text-blue-400 mr-2" />
          {t('incubator.analytics.comparative.title')}
        </h2>

        {expanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
      </div>

      {expanded && (
        <div className="mt-4 space-y-6">
          {/* Percentile Rankings */}
          <div className="bg-indigo-950/50 rounded-lg p-4 border border-indigo-800/50">
            <h3 className="text-md font-medium mb-3">{t('incubator.analytics.comparative.percentileRankings')}</h3>

            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <RadarChart outerRadius={90} width={730} height={250} data={radarData}>
                  <PolarGrid stroke="#334155" />
                  <PolarAngleAxis
                    dataKey="subject"
                    tick={{ fill: '#94a3b8' }}
                  />
                  <PolarRadiusAxis
                    angle={30}
                    domain={[0, 100]}
                    tick={{ fill: '#94a3b8' }}
                  />
                  <Radar
                    name="Your Business"
                    dataKey="A"
                    stroke="#8b5cf6"
                    fill="#8b5cf6"
                    fillOpacity={0.6}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#1e293b',
                      borderColor: '#475569',
                      color: '#f8fafc'
                    }}
                    labelStyle={{ color: '#f8fafc' }}
                  />
                  <Legend />
                </RadarChart>
              </ResponsiveContainer>
            </div>

            <div className="mt-2 text-sm text-gray-400">
              <div>
                {t('incubator.analytics.comparative.percentileDescription')}
              </div>
            </div>
          </div>

          {/* Comparison with Industry Averages */}
          <div className="bg-indigo-950/50 rounded-lg p-4 border border-indigo-800/50">
            <h3 className="text-md font-medium mb-3">{t('incubator.analytics.comparative.industryAverages')}</h3>

            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={barChartData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#334155" />
                  <XAxis
                    dataKey="name"
                    stroke="#94a3b8"
                    tick={{ fill: '#94a3b8' }}
                  />
                  <YAxis
                    stroke="#94a3b8"
                    tick={{ fill: '#94a3b8' }}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#1e293b',
                      borderColor: '#475569',
                      color: '#f8fafc'
                    }}
                    labelStyle={{ color: '#f8fafc' }}
                  />
                  <Legend />
                  <Bar dataKey="yours" name={t('incubator.analytics.comparative.yourBusiness')} fill="#8b5cf6" />
                  <Bar dataKey="average" name={t('incubator.analytics.comparative.industryAverage')} fill="#3b82f6" />
                </BarChart>
              </ResponsiveContainer>
            </div>

            <div className="mt-2 text-sm text-gray-400">
              <div>
                {t('incubator.analytics.comparative.industryAveragesDescription')}
              </div>
            </div>
          </div>

          {/* Similar Businesses */}
          <div className="bg-indigo-950/50 rounded-lg p-4 border border-indigo-800/50">
            <h3 className="text-md font-medium mb-3 flex items-center">
              <Users size={16} className="text-blue-400 mr-2" />
              {t('incubator.analytics.comparative.similarBusinesses')}
            </h3>

            {comparativeAnalytics.similar_ideas?.similar_ideas?.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-indigo-800/50">
                  <thead>
                    <tr>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">{t('incubator.analytics.comparative.business')}</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">{t('incubator.analytics.comparative.stage')}</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">{t('incubator.analytics.comparative.progress')}</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">{t('incubator.analytics.comparative.milestones')}</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">{t('incubator.analytics.comparative.goals')}</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-indigo-800/30">
                    {comparativeAnalytics.similar_ideas.similar_ideas.map((business) => (
                      <tr key={business.id}>
                        <td className="px-4 py-2 whitespace-nowrap text-sm">{business.title}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm">{business.current_stage}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm">{business.progress_rate.toFixed(1)}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm">{formatPercentage(business.milestone_completion_rate)}</td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm">{formatPercentage(business.goal_achievement_rate)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-4 text-gray-400">
                <div>{t('incubator.analytics.comparative.noSimilarBusinesses')}</div>
              </div>
            )}
          </div>

          {/* Competitive Advantages */}
          {comparativeAnalytics.competitive_advantages?.key_advantages?.length > 0 && (
            <div className="bg-indigo-950/50 rounded-lg p-4 border border-indigo-800/50">
              <h3 className="text-md font-medium mb-3 flex items-center">
                <ArrowUpRight size={16} className="text-green-400 mr-2" />
                {t('incubator.analytics.comparative.competitiveAdvantages')}
              </h3>

              <div className="space-y-3">
                {comparativeAnalytics.competitive_advantages.key_advantages.map((advantage, index) => (
                  <div key={index} className="bg-green-900/20 rounded-lg p-3 border border-green-800/30">
                    <div className="flex justify-between items-center mb-1">
                      <h4 className="font-medium">{advantage.area}</h4>
                      <span className="text-green-400 font-medium">{advantage.difference}</span>
                    </div>
                    <div className="text-sm text-gray-400 mb-2">
                      {t('incubator.analytics.comparative.yourValue')} {advantage.area.includes('Rate') ? advantage.value.toFixed(1) : formatPercentage(advantage.value)} |
                      {t('incubator.analytics.comparative.average')} {advantage.area.includes('Rate') ? advantage.average.toFixed(1) : formatPercentage(advantage.average)}
                    </div>
                    <p className="text-sm text-gray-400">
                      <span className="font-medium text-green-400">{t('incubator.analytics.comparative.impact')}:</span> {advantage.impact}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Competitive Disadvantages */}
          {comparativeAnalytics.competitive_disadvantages?.key_disadvantages?.length > 0 && (
            <div className="bg-indigo-950/50 rounded-lg p-4 border border-indigo-800/50">
              <h3 className="text-md font-medium mb-3 flex items-center">
                <ArrowDownRight size={16} className="text-red-400 mr-2" />
                {t('incubator.analytics.comparative.areasToImprove')}
              </h3>

              <div className="space-y-3">
                {comparativeAnalytics.competitive_disadvantages.key_disadvantages.map((disadvantage, index) => (
                  <div key={index} className="bg-red-900/20 rounded-lg p-3 border border-red-800/30">
                    <div className="flex justify-between items-center mb-1">
                      <h4 className="font-medium">{disadvantage.area}</h4>
                      <span className="text-red-400 font-medium">{disadvantage.difference}</span>
                    </div>
                    <p className="text-sm text-gray-400 mb-2">
                      {t('incubator.analytics.comparative.yourValue')} {disadvantage.area.includes('Rate') ? disadvantage.value.toFixed(1) : formatPercentage(disadvantage.value)} |
                      {t('incubator.analytics.comparative.average')} {disadvantage.area.includes('Rate') ? disadvantage.average.toFixed(1) : formatPercentage(disadvantage.average)}
                    </p>
                    <p className="text-sm text-gray-400">
                      <span className="font-medium text-red-400">{t('incubator.analytics.comparative.suggestion')}:</span> {disadvantage.suggestion}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ComparativeAnalyticsSection;
