/**
 * COMPREHENSIVE ROLE-BASED NAVIGATION DEBUGGER
 * Advanced debugging utility to diagnose role-based navigation issues
 */

import { getUserRole, hasAnyRole, canAccessRoute, UserRole } from './unifiedRoleManager';
import { NAVIGATION_ITEMS, getNavigationItemsForRole } from '../config/navigationConfig';
import { User } from '../services/api';

interface ComprehensiveDebugReport {
  timestamp: string;
  userAnalysis: {
    isAuthenticated: boolean;
    userObject: any;
    detectedRole: UserRole;
    roleSource: string;
    roleValidation: boolean;
  };
  navigationAnalysis: {
    totalItems: number;
    filteredItems: number;
    itemsByCategory: Record<string, number>;
    accessibleItems: string[];
    blockedItems: string[];
  };
  systemAnalysis: {
    reduxState: any;
    unifiedRoleManagerWorking: boolean;
    navigationConfigLoaded: boolean;
    sidebarMounted: boolean;
  };
  potentialIssues: Array<{
    severity: 'low' | 'medium' | 'high' | 'critical';
    category: string;
    issue: string;
    recommendation: string;
  }>;
}

/**
 * Get Redux store from window object
 */
function getReduxStore(): any {
  return (window as any).__REDUX_STORE__ || (window as any).store;
}

/**
 * Analyze user authentication and role detection
 */
function analyzeUser(): ComprehensiveDebugReport['userAnalysis'] {
  const store = getReduxStore();
  const authState = store?.getState()?.auth;
  const user = authState?.user;
  
  let roleSource = 'unknown';
  let detectedRole: UserRole = 'user';
  let roleValidation = false;
  
  if (user) {
    // Determine role source
    if (user.user_role) {
      roleSource = 'backend_user_role';
      detectedRole = user.user_role as UserRole;
    } else if (user.is_superuser) {
      roleSource = 'is_superuser_flag';
      detectedRole = 'super_admin';
    } else if (user.is_staff) {
      roleSource = 'is_staff_flag';
      detectedRole = 'admin';
    } else if (user.profile?.primary_role?.name) {
      roleSource = 'profile_primary_role';
      detectedRole = user.profile.primary_role.name as UserRole;
    } else {
      roleSource = 'default_fallback';
      detectedRole = 'user';
    }
    
    // Validate role detection
    const unifiedRoleResult = getUserRole(user);
    roleValidation = unifiedRoleResult === detectedRole;
  }
  
  return {
    isAuthenticated: authState?.isAuthenticated || false,
    userObject: user,
    detectedRole,
    roleSource,
    roleValidation
  };
}

/**
 * Analyze navigation filtering
 */
function analyzeNavigation(userRole: UserRole): ComprehensiveDebugReport['navigationAnalysis'] {
  const allItems = NAVIGATION_ITEMS;
  const filteredItems = getNavigationItemsForRole(userRole);
  
  const itemsByCategory: Record<string, number> = {};
  const accessibleItems: string[] = [];
  const blockedItems: string[] = [];
  
  // Categorize all items
  allItems.forEach(item => {
    if (item.allowedRoles.includes(userRole)) {
      accessibleItems.push(item.id);
      itemsByCategory[item.category] = (itemsByCategory[item.category] || 0) + 1;
    } else {
      blockedItems.push(item.id);
    }
  });
  
  return {
    totalItems: allItems.length,
    filteredItems: filteredItems.length,
    itemsByCategory,
    accessibleItems,
    blockedItems
  };
}

/**
 * Analyze system state
 */
function analyzeSystem(): ComprehensiveDebugReport['systemAnalysis'] {
  const store = getReduxStore();
  const reduxState = store?.getState();
  
  // Check if unified role manager is working
  let unifiedRoleManagerWorking = false;
  try {
    const testUser = { id: 1, username: 'test', email: '<EMAIL>', user_role: 'user' };
    const role = getUserRole(testUser as User);
    unifiedRoleManagerWorking = role === 'user';
  } catch (error) {
    unifiedRoleManagerWorking = false;
  }
  
  // Check if navigation config is loaded
  const navigationConfigLoaded = NAVIGATION_ITEMS && NAVIGATION_ITEMS.length > 0;
  
  // Check if sidebar is mounted (look for sidebar element in DOM)
  const sidebarMounted = document.querySelector('[class*="sidebar"]') !== null ||
                        document.querySelector('[class*="navigation"]') !== null ||
                        document.querySelector('nav') !== null;
  
  return {
    reduxState: reduxState ? {
      auth: reduxState.auth ? {
        isAuthenticated: reduxState.auth.isAuthenticated,
        isLoading: reduxState.auth.isLoading,
        hasUser: !!reduxState.auth.user,
        userKeys: reduxState.auth.user ? Object.keys(reduxState.auth.user) : []
      } : null,
      hasOtherSlices: Object.keys(reduxState).filter(key => key !== 'auth')
    } : null,
    unifiedRoleManagerWorking,
    navigationConfigLoaded,
    sidebarMounted
  };
}

/**
 * Identify potential issues
 */
function identifyIssues(
  userAnalysis: ComprehensiveDebugReport['userAnalysis'],
  navigationAnalysis: ComprehensiveDebugReport['navigationAnalysis'],
  systemAnalysis: ComprehensiveDebugReport['systemAnalysis']
): ComprehensiveDebugReport['potentialIssues'] {
  const issues: ComprehensiveDebugReport['potentialIssues'] = [];
  
  // User authentication issues
  if (!userAnalysis.isAuthenticated) {
    issues.push({
      severity: 'high',
      category: 'authentication',
      issue: 'User is not authenticated',
      recommendation: 'Check login flow and ensure user is properly authenticated'
    });
  }
  
  if (!userAnalysis.userObject) {
    issues.push({
      severity: 'critical',
      category: 'authentication',
      issue: 'User object is null or undefined',
      recommendation: 'Check Redux auth state and ensure user data is properly stored after login'
    });
  }
  
  if (!userAnalysis.roleValidation) {
    issues.push({
      severity: 'medium',
      category: 'role_detection',
      issue: 'Role detection inconsistency between manual analysis and unified role manager',
      recommendation: 'Check unified role manager logic and ensure it matches expected role determination'
    });
  }
  
  // Navigation issues
  if (navigationAnalysis.filteredItems === 0) {
    issues.push({
      severity: 'high',
      category: 'navigation',
      issue: 'No navigation items available for user role',
      recommendation: 'Check navigation configuration and ensure role has appropriate access'
    });
  }
  
  if (navigationAnalysis.filteredItems === navigationAnalysis.totalItems) {
    issues.push({
      severity: 'medium',
      category: 'navigation',
      issue: 'User has access to all navigation items (no role-based filtering)',
      recommendation: 'Check if user has super admin privileges or if role-based filtering is working correctly'
    });
  }
  
  // System issues
  if (!systemAnalysis.unifiedRoleManagerWorking) {
    issues.push({
      severity: 'critical',
      category: 'system',
      issue: 'Unified role manager is not functioning correctly',
      recommendation: 'Check unified role manager imports and function implementations'
    });
  }
  
  if (!systemAnalysis.navigationConfigLoaded) {
    issues.push({
      severity: 'critical',
      category: 'system',
      issue: 'Navigation configuration is not loaded',
      recommendation: 'Check navigation config imports and ensure NAVIGATION_ITEMS is properly exported'
    });
  }
  
  if (!systemAnalysis.sidebarMounted) {
    issues.push({
      severity: 'medium',
      category: 'ui',
      issue: 'Sidebar component does not appear to be mounted in DOM',
      recommendation: 'Check if sidebar component is properly rendered and mounted'
    });
  }
  
  if (!systemAnalysis.reduxState) {
    issues.push({
      severity: 'critical',
      category: 'system',
      issue: 'Redux store is not accessible',
      recommendation: 'Check Redux store configuration and ensure it is properly initialized'
    });
  }
  
  return issues;
}

/**
 * Generate comprehensive debug report
 */
export function generateComprehensiveDebugReport(): ComprehensiveDebugReport {
  console.log('🔍 GENERATING COMPREHENSIVE ROLE DEBUG REPORT');
  console.log('=' .repeat(60));
  
  const userAnalysis = analyzeUser();
  const navigationAnalysis = analyzeNavigation(userAnalysis.detectedRole);
  const systemAnalysis = analyzeSystem();
  const potentialIssues = identifyIssues(userAnalysis, navigationAnalysis, systemAnalysis);
  
  const report: ComprehensiveDebugReport = {
    timestamp: new Date().toISOString(),
    userAnalysis,
    navigationAnalysis,
    systemAnalysis,
    potentialIssues
  };
  
  // Log detailed report
  console.log('\n📊 USER ANALYSIS:');
  console.log(`   Authenticated: ${userAnalysis.isAuthenticated}`);
  console.log(`   Role: ${userAnalysis.detectedRole} (source: ${userAnalysis.roleSource})`);
  console.log(`   Role Validation: ${userAnalysis.roleValidation ? '✅' : '❌'}`);
  
  console.log('\n📊 NAVIGATION ANALYSIS:');
  console.log(`   Total Items: ${navigationAnalysis.totalItems}`);
  console.log(`   Filtered Items: ${navigationAnalysis.filteredItems}`);
  console.log(`   Accessible: ${navigationAnalysis.accessibleItems.join(', ')}`);
  console.log(`   Blocked: ${navigationAnalysis.blockedItems.join(', ')}`);
  
  console.log('\n📊 SYSTEM ANALYSIS:');
  console.log(`   Redux Store: ${systemAnalysis.reduxState ? '✅' : '❌'}`);
  console.log(`   Role Manager: ${systemAnalysis.unifiedRoleManagerWorking ? '✅' : '❌'}`);
  console.log(`   Navigation Config: ${systemAnalysis.navigationConfigLoaded ? '✅' : '❌'}`);
  console.log(`   Sidebar Mounted: ${systemAnalysis.sidebarMounted ? '✅' : '❌'}`);
  
  console.log('\n⚠️ POTENTIAL ISSUES:');
  if (potentialIssues.length === 0) {
    console.log('   No issues detected! ✅');
  } else {
    potentialIssues.forEach((issue, index) => {
      const severityIcon = {
        low: '🟡',
        medium: '🟠',
        high: '🔴',
        critical: '🚨'
      }[issue.severity];
      
      console.log(`   ${index + 1}. ${severityIcon} [${issue.category.toUpperCase()}] ${issue.issue}`);
      console.log(`      💡 ${issue.recommendation}`);
    });
  }
  
  console.log('\n🏁 COMPREHENSIVE DEBUG REPORT COMPLETED');
  console.log('=' .repeat(60));
  
  return report;
}

/**
 * Quick diagnostic function for browser console
 */
export function quickDiagnosis(): void {
  const report = generateComprehensiveDebugReport();
  
  if (report.potentialIssues.length === 0) {
    console.log('🎉 DIAGNOSIS: Role-based navigation system is working correctly!');
  } else {
    const criticalIssues = report.potentialIssues.filter(i => i.severity === 'critical').length;
    const highIssues = report.potentialIssues.filter(i => i.severity === 'high').length;
    
    if (criticalIssues > 0) {
      console.log(`🚨 DIAGNOSIS: ${criticalIssues} critical issue(s) found - system may not be working`);
    } else if (highIssues > 0) {
      console.log(`🔴 DIAGNOSIS: ${highIssues} high priority issue(s) found - check authentication`);
    } else {
      console.log('🟡 DIAGNOSIS: Minor issues found - system should be working with some limitations');
    }
  }
}

/**
 * Real-time monitoring for role-based navigation
 */
export function startRoleMonitoring(): void {
  console.log('🔄 Starting real-time role-based navigation monitoring...');

  let lastUserRole: UserRole | null = null;
  let lastNavigationCount = 0;

  const monitor = () => {
    try {
      const store = getReduxStore();
      const user = store?.getState()?.auth?.user;
      const currentRole = user ? getUserRole(user) : null;
      const currentNavCount = currentRole ? getNavigationItemsForRole(currentRole).length : 0;

      // Check for role changes
      if (currentRole !== lastUserRole) {
        console.log(`🔄 Role changed: ${lastUserRole} → ${currentRole}`);
        lastUserRole = currentRole;

        if (currentRole) {
          const navItems = getNavigationItemsForRole(currentRole);
          console.log(`📋 Navigation updated: ${navItems.length} items for ${currentRole}`);
          console.log(`   Items: ${navItems.map(item => item.id).join(', ')}`);
        }
      }

      // Check for navigation count changes
      if (currentNavCount !== lastNavigationCount) {
        console.log(`📊 Navigation count changed: ${lastNavigationCount} → ${currentNavCount}`);
        lastNavigationCount = currentNavCount;
      }

    } catch (error) {
      console.error('❌ Role monitoring error:', error);
    }
  };

  // Monitor every 2 seconds
  const intervalId = setInterval(monitor, 2000);

  // Store interval ID for cleanup
  (window as any).roleMonitoringInterval = intervalId;

  console.log('✅ Role monitoring started. Call stopRoleMonitoring() to stop.');
}

/**
 * Stop real-time monitoring
 */
export function stopRoleMonitoring(): void {
  const intervalId = (window as any).roleMonitoringInterval;
  if (intervalId) {
    clearInterval(intervalId);
    delete (window as any).roleMonitoringInterval;
    console.log('🛑 Role monitoring stopped.');
  } else {
    console.log('⚠️ No active role monitoring found.');
  }
}

/**
 * Test role switching simulation
 */
export function simulateRoleSwitch(targetRole: UserRole): void {
  console.log(`🎭 Simulating role switch to: ${targetRole}`);

  const store = getReduxStore();
  if (!store) {
    console.log('❌ Cannot simulate - Redux store not accessible');
    return;
  }

  const currentUser = store.getState()?.auth?.user;
  if (!currentUser) {
    console.log('❌ Cannot simulate - No current user');
    return;
  }

  // Create modified user object
  const simulatedUser = {
    ...currentUser,
    user_role: targetRole,
    is_superuser: targetRole === 'super_admin',
    is_staff: targetRole === 'admin' || targetRole === 'super_admin',
    is_admin: targetRole === 'admin' || targetRole === 'super_admin'
  };

  console.log('📊 Simulated navigation for role:', targetRole);
  const navItems = getNavigationItemsForRole(targetRole);
  console.log(`   Items (${navItems.length}):`, navItems.map(item => item.id));

  console.log('⚠️ Note: This is a simulation only. Actual role change requires backend update.');
}

// Make functions available in browser console
if (typeof window !== 'undefined') {
  (window as any).comprehensiveRoleDebug = {
    generateReport: generateComprehensiveDebugReport,
    quickDiagnosis,
    analyzeUser,
    analyzeNavigation,
    analyzeSystem,
    startMonitoring: startRoleMonitoring,
    stopMonitoring: stopRoleMonitoring,
    simulateRole: simulateRoleSwitch
  };

  console.log('🔧 Comprehensive Role Debugger loaded!');
  console.log('Available commands:');
  console.log('  comprehensiveRoleDebug.generateReport() - Full diagnostic report');
  console.log('  comprehensiveRoleDebug.quickDiagnosis() - Quick health check');
  console.log('  comprehensiveRoleDebug.startMonitoring() - Real-time monitoring');
  console.log('  comprehensiveRoleDebug.stopMonitoring() - Stop monitoring');
  console.log('  comprehensiveRoleDebug.simulateRole("admin") - Test role switching');
}
