/**
 * UNIFIED ROLE MANAGER - AUTHORITATIVE SOURCE
 * Single source of truth for all role and permission checking
 * Integrates with backend UserRole model and UserRoleAssignment system
 *
 * ⚠️ CRITICAL: This is the ONLY file for role management
 * DO NOT create duplicate role checking logic elsewhere
 */

import { User } from '../services/api';

// Define all user roles in the system (EXACTLY matches backend UserRole.ROLE_CHOICES)
export type UserRole = 'super_admin' | 'admin' | 'moderator' | 'mentor' | 'investor' | 'user';

// Define all permission levels (EXACTLY matches backend UserRole.PERMISSION_CHOICES)
export type PermissionLevel = 'read' | 'write' | 'moderate' | 'admin' | 'super_admin';

// AUTHORITATIVE Role hierarchy (matches backend permission system)
const ROLE_HIERARCHY: Record<UserRole, number> = {
  'super_admin': 6,
  'admin': 5,
  'moderator': 4,
  'mentor': 3,
  'investor': 2,
  'user': 1
};

// AUTHORITATIVE Permission hierarchy (matches backend permission system)
const PERMISSION_HIERARCHY: Record<PermissionLevel, number> = {
  'read': 1,
  'write': 2,
  'moderate': 3,
  'admin': 4,
  'super_admin': 5
};

// AUTHORITATIVE Role-based permissions mapping (EXACTLY matches backend serializers.py lines 108-116)
const ROLE_PERMISSIONS: Record<UserRole, PermissionLevel[]> = {
  'super_admin': ['read', 'write', 'moderate', 'admin', 'super_admin'],
  'admin': ['read', 'write', 'moderate', 'admin'],
  'moderator': ['read', 'write', 'moderate'],
  'mentor': ['read', 'write'],
  'investor': ['read', 'write'],
  'user': ['read']
};

/**
 * AUTHORITATIVE ROLE DETERMINATION - USES DEDICATED ROLE FILES
 * Single function to determine user's primary role - NO MORE DUPLICATES
 * Uses dedicated role detection functions from /roles/ directory
 */
export function getUserRole(user: User | null): UserRole {
  if (!user) return 'user';

  // Use the role determined by the backend UserSerializer (primary source)
  if (user.user_role) {
    return user.user_role as UserRole;
  }

  // Fallback logic - Check for highest roles first (Django fields)
  if (user.is_superuser) {
    return 'super_admin';
  } else if (user.is_staff) {
    return 'admin';
  }

  // Check profile-based roles
  if (user.profile) {
    // Check primary role first
    if (user.profile.primary_role?.name && ['mentor', 'investor', 'moderator'].includes(user.profile.primary_role.name)) {
      return user.profile.primary_role.name as UserRole;
    }

    // Check active roles for specialized roles
    if (user.profile.active_roles) {
      for (const role of user.profile.active_roles) {
        if (role.name && ['mentor', 'investor', 'moderator'].includes(role.name)) {
          return role.name as UserRole;
        }
      }
    }
  }

  // Default to regular user
  return 'user';
}

/**
 * EXCLUSIVE ROLE SYSTEM - matches backend ai_permissions.py get_user_roles()
 * Returns array with single primary role (exclusive roles system)
 * Each user has ONE primary role - higher roles don't inherit lower role permissions
 */
export function getUserRoles(user: User | null): UserRole[] {
  return [getUserRole(user)];
}

/**
 * AUTHORITATIVE PERMISSION DETERMINATION
 * Single function to determine user permissions based on backend data
 * EXACTLY matches backend serializers.py get_role_permissions() method (lines 101-117)
 */
export function getUserPermissions(user: User | null): PermissionLevel[] {
  if (!user) return ['read'];

  // Use permissions from backend if available (primary source)
  if (user.role_permissions && Array.isArray(user.role_permissions)) {
    return user.role_permissions as PermissionLevel[];
  }

  // Fallback to role-based permissions using AUTHORITATIVE mapping
  const userRole = getUserRole(user);
  return ROLE_PERMISSIONS[userRole] || ['read'];
}

/**
 * UNIFIED ROLE CHECKING
 * Single function to check if user has a specific role
 */
export function hasRole(user: User | null, roleName: UserRole): boolean {
  const userRole = getUserRole(user);
  return userRole === roleName;
}

/**
 * Check if user has any of the specified roles
 */
export function hasAnyRole(user: User | null, roleNames: UserRole[]): boolean {
  const userRole = getUserRole(user);
  return roleNames.includes(userRole);
}

/**
 * UNIFIED PERMISSION CHECKING
 * Single function to check if user has a specific permission
 */
export function hasPermission(user: User | null, permission: PermissionLevel): boolean {
  const userPermissions = getUserPermissions(user);
  return userPermissions.includes(permission);
}

/**
 * Check if user has any of the specified permissions
 */
export function hasAnyPermission(user: User | null, permissions: PermissionLevel[]): boolean {
  const userPermissions = getUserPermissions(user);
  return permissions.some(permission => userPermissions.includes(permission));
}

/**
 * UNIFIED ROLE HIERARCHY CHECKING
 * Check if user has a role at or above the specified level
 */
export function hasRoleLevel(user: User | null, minimumRole: UserRole): boolean {
  const userRole = getUserRole(user);
  const userLevel = ROLE_HIERARCHY[userRole];
  const minimumLevel = ROLE_HIERARCHY[minimumRole];

  return userLevel >= minimumLevel;
}

/**
 * UNIFIED PERMISSION HIERARCHY CHECKING
 * Check if user has a permission at or above the specified level
 */
export function hasPermissionLevel(user: User | null, minimumPermission: PermissionLevel): boolean {
  const userPermissions = getUserPermissions(user);
  const minimumLevel = PERMISSION_HIERARCHY[minimumPermission];

  return userPermissions.some(permission => PERMISSION_HIERARCHY[permission] >= minimumLevel);
}

/**
 * UNIFIED HIGHEST ROLE DETERMINATION
 * Get the highest role for the user
 */
export function getHighestRole(user: User | null): UserRole {
  return getUserRole(user);
}

/**
 * UNIFIED HIGHEST PERMISSION DETERMINATION
 * Get the highest permission level for the user
 */
export function getHighestPermission(user: User | null): PermissionLevel {
  const permissions = getUserPermissions(user);
  return permissions[0] || 'read'; // Already sorted by hierarchy
}

/**
 * UNIFIED ADMIN CHECKING
 * Check if user is an admin (admin or super_admin)
 */
export function isAdmin(user: User | null): boolean {
  return hasRoleLevel(user, 'admin');
}

/**
 * UNIFIED SUPER ADMIN CHECKING
 * Check if user is a super admin
 */
export function isSuperAdmin(user: User | null): boolean {
  return hasRole(user, 'super_admin');
}

// Duplicate functions removed - using the original implementations above

/**
 * UNIFIED ROUTE ACCESS CHECKING
 * Single function to check if user can access a route
 */
export function canAccessRoute(
  user: User | null,
  requiredRoles?: UserRole[],
  requiredPermissions?: PermissionLevel[],
  requireAuth: boolean = true
): boolean {
  // Check authentication requirement
  if (requireAuth && !user) {
    return false;
  }

  // Super admin can access everything
  if (isSuperAdmin(user)) {
    return true;
  }

  // Check role requirements
  if (requiredRoles && requiredRoles.length > 0) {
    if (!hasAnyRole(user, requiredRoles)) {
      return false;
    }
  }

  // Check permission requirements
  if (requiredPermissions && requiredPermissions.length > 0) {
    if (!hasAnyPermission(user, requiredPermissions)) {
      return false;
    }
  }

  return true;
}

/**
 * AUTHORITATIVE DASHBOARD ROUTE DETERMINATION
 * Get the appropriate dashboard route based on user's role
 */
export function getDashboardRoute(user: User | null): string {
  if (!user) return '/login';

  const userRole = getUserRole(user);

  const dashboardRoutes: Record<UserRole, string> = {
    'super_admin': '/super_admin',
    'admin': '/admin',
    'moderator': '/dashboard/moderation',
    'mentor': '/dashboard/mentorship',
    'investor': '/dashboard/investments',
    'user': '/dashboard'
  };

  return dashboardRoutes[userRole] || '/dashboard';
}

/**
 * UNIFIED USER TYPE DETERMINATION
 * Get the primary user type for display purposes
 */
export function getUserType(user: User | null): UserRole {
  return getHighestRole(user);
}

/**
 * DEBUG HELPER
 * Get detailed role and permission information for debugging
 */
export function getUserRoleDebugInfo(user: User | null) {
  if (!user) {
    return {
      user: null,
      roles: ['user'] as UserRole[],
      permissions: ['read'] as PermissionLevel[],
      highestRole: 'user' as UserRole,
      highestPermission: 'read' as PermissionLevel,
      isAdmin: false,
      isSuperAdmin: false,
      dashboardRoute: '/login'
    };
  }

  const roles = getUserRoles(user);
  const permissions = getUserPermissions(user);

  return {
    user: {
      username: user.username,
      is_superuser: user.is_superuser,
      is_admin: user.is_admin,
      is_staff: user.is_staff,
      profile: user.profile
    },
    roles,
    permissions,
    highestRole: getHighestRole(user),
    highestPermission: getHighestPermission(user),
    isAdmin: isAdmin(user),
    isSuperAdmin: isSuperAdmin(user),
    dashboardRoute: getDashboardRoute(user)
  };
}
