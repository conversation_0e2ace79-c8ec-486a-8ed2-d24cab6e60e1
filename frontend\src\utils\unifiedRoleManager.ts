/**
 * UNIFIED ROLE MANAGER
 * Single source of truth for all role and permission checking
 * Replaces all conflicting role checking functions throughout the app
 */

import { User } from '../services/api';

// Define all user roles in the system
export type UserRole = 'super_admin' | 'admin' | 'moderator' | 'mentor' | 'investor' | 'user';

// Define all permission levels
export type PermissionLevel = 'read' | 'write' | 'moderate' | 'admin' | 'super_admin';

// Role hierarchy (higher number = more permissions)
const ROLE_HIERARCHY: Record<UserRole, number> = {
  'super_admin': 6,
  'admin': 5,
  'moderator': 4,
  'mentor': 3,
  'investor': 2,
  'user': 1
};

// Permission hierarchy (higher number = more permissions)
const PERMISSION_HIERARCHY: Record<PermissionLevel, number> = {
  'super_admin': 5,
  'admin': 4,
  'moderate': 3,
  'write': 2,
  'read': 1
};

// Role to permission mapping
const ROLE_PERMISSIONS: Record<UserRole, PermissionLevel[]> = {
  'super_admin': ['super_admin', 'admin', 'moderate', 'write', 'read'],
  'admin': ['admin', 'moderate', 'write', 'read'],
  'moderator': ['moderate', 'write', 'read'],
  'mentor': ['write', 'read'],
  'investor': ['write', 'read'],
  'user': ['read']
};

/**
 * UNIFIED ROLE DETERMINATION
 * Single function to determine all user roles
 */
export function getUserRoles(user: User | null): UserRole[] {
  if (!user) return ['user'];

  // ✅ EXCLUSIVE ROLE SYSTEM: Each user has ONE primary role
  // Higher roles don't inherit lower role permissions for navigation

  // Check for highest role first (exclusive hierarchy)
  if (user.is_superuser) {
    return ['super_admin']; // Super admin ONLY
  }

  if (user.is_admin) {
    return ['admin']; // Admin ONLY (not user)
  }

  // Check for staff/moderator (if not already admin/super_admin)
  if (user.is_staff && !user.is_admin) {
    return ['moderator']; // Moderator ONLY (not user)
  }

  // Check primary role from profile for specialized roles
  if (user.profile?.primary_role?.name) {
    const primaryRole = user.profile.primary_role.name as UserRole;
    if (['mentor', 'investor'].includes(primaryRole)) {
      return [primaryRole]; // Mentor or Investor ONLY (not user)
    }
  }

  // Check active roles for mentor/investor
  if (user.profile?.active_roles && Array.isArray(user.profile.active_roles)) {
    for (const role of user.profile.active_roles) {
      const roleName = role.name as UserRole;
      if (['mentor', 'investor'].includes(roleName)) {
        return [roleName]; // First specialized role found
      }
    }
  }

  // Default to regular user ONLY if no specialized roles
  return ['user'];
}

/**
 * UNIFIED PERMISSION DETERMINATION
 * Single function to determine all user permissions
 */
export function getUserPermissions(user: User | null): PermissionLevel[] {
  const roles = getUserRoles(user);
  const permissions = new Set<PermissionLevel>();

  // Collect all permissions from all roles
  roles.forEach(role => {
    ROLE_PERMISSIONS[role].forEach(permission => {
      permissions.add(permission);
    });
  });

  // Convert to array and sort by hierarchy (highest first)
  const permissionArray = Array.from(permissions);
  permissionArray.sort((a, b) => PERMISSION_HIERARCHY[b] - PERMISSION_HIERARCHY[a]);

  return permissionArray;
}

/**
 * UNIFIED ROLE CHECKING
 * Single function to check if user has a specific role
 */
export function hasRole(user: User | null, roleName: UserRole): boolean {
  const userRoles = getUserRoles(user);
  return userRoles.includes(roleName);
}

/**
 * UNIFIED PERMISSION CHECKING
 * Single function to check if user has a specific permission
 */
export function hasPermission(user: User | null, permission: PermissionLevel): boolean {
  const userPermissions = getUserPermissions(user);
  return userPermissions.includes(permission);
}

/**
 * UNIFIED ROLE HIERARCHY CHECKING
 * Check if user has a role at or above the specified level
 */
export function hasRoleLevel(user: User | null, minimumRole: UserRole): boolean {
  const userRoles = getUserRoles(user);
  const minimumLevel = ROLE_HIERARCHY[minimumRole];

  return userRoles.some(role => ROLE_HIERARCHY[role] >= minimumLevel);
}

/**
 * UNIFIED PERMISSION HIERARCHY CHECKING
 * Check if user has a permission at or above the specified level
 */
export function hasPermissionLevel(user: User | null, minimumPermission: PermissionLevel): boolean {
  const userPermissions = getUserPermissions(user);
  const minimumLevel = PERMISSION_HIERARCHY[minimumPermission];

  return userPermissions.some(permission => PERMISSION_HIERARCHY[permission] >= minimumLevel);
}

/**
 * UNIFIED HIGHEST ROLE DETERMINATION
 * Get the highest role for the user
 */
export function getHighestRole(user: User | null): UserRole {
  const roles = getUserRoles(user);
  return roles[0] || 'user'; // Already sorted by hierarchy
}

/**
 * UNIFIED HIGHEST PERMISSION DETERMINATION
 * Get the highest permission level for the user
 */
export function getHighestPermission(user: User | null): PermissionLevel {
  const permissions = getUserPermissions(user);
  return permissions[0] || 'read'; // Already sorted by hierarchy
}

/**
 * UNIFIED ADMIN CHECKING
 * Check if user is an admin (admin or super_admin)
 */
export function isAdmin(user: User | null): boolean {
  return hasRoleLevel(user, 'admin');
}

/**
 * UNIFIED SUPER ADMIN CHECKING
 * Check if user is a super admin
 */
export function isSuperAdmin(user: User | null): boolean {
  return hasRole(user, 'super_admin');
}

/**
 * UNIFIED MULTIPLE ROLE CHECKING
 * Check if user has any of the specified roles
 */
export function hasAnyRole(user: User | null, roles: UserRole[]): boolean {
  const userRoles = getUserRoles(user);
  return roles.some(role => userRoles.includes(role));
}

/**
 * UNIFIED MULTIPLE PERMISSION CHECKING
 * Check if user has any of the specified permissions
 */
export function hasAnyPermission(user: User | null, permissions: PermissionLevel[]): boolean {
  const userPermissions = getUserPermissions(user);
  return permissions.some(permission => userPermissions.includes(permission));
}

/**
 * UNIFIED ROUTE ACCESS CHECKING
 * Single function to check if user can access a route
 */
export function canAccessRoute(
  user: User | null,
  requiredRoles?: UserRole[],
  requiredPermissions?: PermissionLevel[],
  requireAuth: boolean = true
): boolean {
  // Check authentication requirement
  if (requireAuth && !user) {
    return false;
  }

  // Super admin can access everything
  if (isSuperAdmin(user)) {
    return true;
  }

  // Check role requirements
  if (requiredRoles && requiredRoles.length > 0) {
    if (!hasAnyRole(user, requiredRoles)) {
      return false;
    }
  }

  // Check permission requirements
  if (requiredPermissions && requiredPermissions.length > 0) {
    if (!hasAnyPermission(user, requiredPermissions)) {
      return false;
    }
  }

  return true;
}

/**
 * UNIFIED DASHBOARD ROUTE DETERMINATION
 * Get the appropriate dashboard route for a user based on their highest role
 */
export function getDashboardRoute(user: User | null): string {
  if (!user) return '/login';

  const highestRole = getHighestRole(user);

  const dashboardRoutes: Record<UserRole, string> = {
    'super_admin': '/super_admin',
    'admin': '/admin',
    'moderator': '/dashboard/moderator',
    'mentor': '/dashboard/mentor',
    'investor': '/dashboard/investor',
    'user': '/dashboard'
  };

  return dashboardRoutes[highestRole] || '/dashboard';
}

/**
 * UNIFIED USER TYPE DETERMINATION
 * Get the primary user type for display purposes
 */
export function getUserType(user: User | null): UserRole {
  return getHighestRole(user);
}

/**
 * DEBUG HELPER
 * Get detailed role and permission information for debugging
 */
export function getUserRoleDebugInfo(user: User | null) {
  if (!user) {
    return {
      user: null,
      roles: ['user'] as UserRole[],
      permissions: ['read'] as PermissionLevel[],
      highestRole: 'user' as UserRole,
      highestPermission: 'read' as PermissionLevel,
      isAdmin: false,
      isSuperAdmin: false,
      dashboardRoute: '/login'
    };
  }

  const roles = getUserRoles(user);
  const permissions = getUserPermissions(user);

  return {
    user: {
      username: user.username,
      is_superuser: user.is_superuser,
      is_admin: user.is_admin,
      is_staff: user.is_staff,
      profile: user.profile
    },
    roles,
    permissions,
    highestRole: getHighestRole(user),
    highestPermission: getHighestPermission(user),
    isAdmin: isAdmin(user),
    isSuperAdmin: isSuperAdmin(user),
    dashboardRoute: getDashboardRoute(user)
  };
}
