/**
 * UNIFIED ROLE MANAGER
 * Single source of truth for all role and permission checking
 * Integrates with backend UserRole model and UserRoleAssignment system
 */

import { User } from '../services/api';

// Define all user roles in the system (matches backend UserRole.ROLE_CHOICES)
export type UserRole = 'super_admin' | 'admin' | 'moderator' | 'mentor' | 'investor' | 'user';

// Define all permission levels (matches backend UserRole.PERMISSION_CHOICES)
export type PermissionLevel = 'read' | 'write' | 'moderate' | 'admin' | 'super_admin';

// Role hierarchy (higher number = more permissions)
const ROLE_HIERARCHY: Record<UserRole, number> = {
  'super_admin': 6,
  'admin': 5,
  'moderator': 4,
  'mentor': 3,
  'investor': 2,
  'user': 1
};

// Permission hierarchy (higher number = more permissions)
const PERMISSION_HIERARCHY: Record<PermissionLevel, number> = {
  'super_admin': 5,
  'admin': 4,
  'moderate': 3,
  'write': 2,
  'read': 1
};

// Role to permission mapping
const ROLE_PERMISSIONS: Record<UserRole, PermissionLevel[]> = {
  'super_admin': ['super_admin', 'admin', 'moderate', 'write', 'read'],
  'admin': ['admin', 'moderate', 'write', 'read'],
  'moderator': ['moderate', 'write', 'read'],
  'mentor': ['write', 'read'],
  'investor': ['write', 'read'],
  'user': ['read']
};

/**
 * UNIFIED ROLE DETERMINATION
 * Single function to determine user's primary role based on backend data
 */
export function getUserRole(user: User | null): UserRole {
  if (!user) return 'user';

  // Use the role determined by the backend UserSerializer
  if (user.user_role) {
    return user.user_role as UserRole;
  }

  // Fallback to profile role if available
  if (user.profile?.role) {
    return user.profile.role as UserRole;
  }

  // Final fallback based on Django fields
  if (user.is_superuser) {
    return 'super_admin';
  } else if (user.is_staff || user.is_admin) {
    return 'admin';
  }

  return 'user';
}

/**
 * Get all roles for a user (for backward compatibility)
 */
export function getUserRoles(user: User | null): UserRole[] {
  return [getUserRole(user)];
}

/**
 * UNIFIED PERMISSION DETERMINATION
 * Single function to determine user permissions based on backend data
 */
export function getUserPermissions(user: User | null): PermissionLevel[] {
  if (!user) return ['read'];

  // Use permissions from backend if available
  if (user.role_permissions && Array.isArray(user.role_permissions)) {
    return user.role_permissions as PermissionLevel[];
  }

  // Fallback to role-based permissions
  const userRole = getUserRole(user);
  return ROLE_PERMISSIONS[userRole] || ['read'];
}

/**
 * UNIFIED ROLE CHECKING
 * Single function to check if user has a specific role
 */
export function hasRole(user: User | null, roleName: UserRole): boolean {
  const userRole = getUserRole(user);
  return userRole === roleName;
}

/**
 * Check if user has any of the specified roles
 */
export function hasAnyRole(user: User | null, roleNames: UserRole[]): boolean {
  const userRole = getUserRole(user);
  return roleNames.includes(userRole);
}

/**
 * UNIFIED PERMISSION CHECKING
 * Single function to check if user has a specific permission
 */
export function hasPermission(user: User | null, permission: PermissionLevel): boolean {
  const userPermissions = getUserPermissions(user);
  return userPermissions.includes(permission);
}

/**
 * Check if user has any of the specified permissions
 */
export function hasAnyPermission(user: User | null, permissions: PermissionLevel[]): boolean {
  const userPermissions = getUserPermissions(user);
  return permissions.some(permission => userPermissions.includes(permission));
}

/**
 * UNIFIED ROLE HIERARCHY CHECKING
 * Check if user has a role at or above the specified level
 */
export function hasRoleLevel(user: User | null, minimumRole: UserRole): boolean {
  const userRole = getUserRole(user);
  const userLevel = ROLE_HIERARCHY[userRole];
  const minimumLevel = ROLE_HIERARCHY[minimumRole];

  return userLevel >= minimumLevel;
}

/**
 * UNIFIED PERMISSION HIERARCHY CHECKING
 * Check if user has a permission at or above the specified level
 */
export function hasPermissionLevel(user: User | null, minimumPermission: PermissionLevel): boolean {
  const userPermissions = getUserPermissions(user);
  const minimumLevel = PERMISSION_HIERARCHY[minimumPermission];

  return userPermissions.some(permission => PERMISSION_HIERARCHY[permission] >= minimumLevel);
}

/**
 * UNIFIED HIGHEST ROLE DETERMINATION
 * Get the highest role for the user
 */
export function getHighestRole(user: User | null): UserRole {
  return getUserRole(user);
}

/**
 * UNIFIED HIGHEST PERMISSION DETERMINATION
 * Get the highest permission level for the user
 */
export function getHighestPermission(user: User | null): PermissionLevel {
  const permissions = getUserPermissions(user);
  return permissions[0] || 'read'; // Already sorted by hierarchy
}

/**
 * UNIFIED ADMIN CHECKING
 * Check if user is an admin (admin or super_admin)
 */
export function isAdmin(user: User | null): boolean {
  return hasRoleLevel(user, 'admin');
}

/**
 * UNIFIED SUPER ADMIN CHECKING
 * Check if user is a super admin
 */
export function isSuperAdmin(user: User | null): boolean {
  return hasRole(user, 'super_admin');
}

// Duplicate functions removed - using the original implementations above

/**
 * UNIFIED ROUTE ACCESS CHECKING
 * Single function to check if user can access a route
 */
export function canAccessRoute(
  user: User | null,
  requiredRoles?: UserRole[],
  requiredPermissions?: PermissionLevel[],
  requireAuth: boolean = true
): boolean {
  // Check authentication requirement
  if (requireAuth && !user) {
    return false;
  }

  // Super admin can access everything
  if (isSuperAdmin(user)) {
    return true;
  }

  // Check role requirements
  if (requiredRoles && requiredRoles.length > 0) {
    if (!hasAnyRole(user, requiredRoles)) {
      return false;
    }
  }

  // Check permission requirements
  if (requiredPermissions && requiredPermissions.length > 0) {
    if (!hasAnyPermission(user, requiredPermissions)) {
      return false;
    }
  }

  return true;
}

/**
 * UNIFIED DASHBOARD ROUTE DETERMINATION
 * Get the appropriate dashboard route for a user based on their highest role
 */
export function getDashboardRoute(user: User | null): string {
  if (!user) return '/login';

  const highestRole = getHighestRole(user);

  const dashboardRoutes: Record<UserRole, string> = {
    'super_admin': '/super_admin',
    'admin': '/admin',
    'moderator': '/dashboard/moderator',
    'mentor': '/dashboard/mentor',
    'investor': '/dashboard/investor',
    'user': '/dashboard'
  };

  return dashboardRoutes[highestRole] || '/dashboard';
}

/**
 * UNIFIED USER TYPE DETERMINATION
 * Get the primary user type for display purposes
 */
export function getUserType(user: User | null): UserRole {
  return getHighestRole(user);
}

/**
 * DEBUG HELPER
 * Get detailed role and permission information for debugging
 */
export function getUserRoleDebugInfo(user: User | null) {
  if (!user) {
    return {
      user: null,
      roles: ['user'] as UserRole[],
      permissions: ['read'] as PermissionLevel[],
      highestRole: 'user' as UserRole,
      highestPermission: 'read' as PermissionLevel,
      isAdmin: false,
      isSuperAdmin: false,
      dashboardRoute: '/login'
    };
  }

  const roles = getUserRoles(user);
  const permissions = getUserPermissions(user);

  return {
    user: {
      username: user.username,
      is_superuser: user.is_superuser,
      is_admin: user.is_admin,
      is_staff: user.is_staff,
      profile: user.profile
    },
    roles,
    permissions,
    highestRole: getHighestRole(user),
    highestPermission: getHighestPermission(user),
    isAdmin: isAdmin(user),
    isSuperAdmin: isSuperAdmin(user),
    dashboardRoute: getDashboardRoute(user)
  };
}
