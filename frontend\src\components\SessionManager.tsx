import React, { useEffect } from 'react';
import { useSessionManager } from '../hooks/useSessionManager';

/**
 * Session Manager Component
 * Handles session restoration and monitoring globally
 * This component should be mounted at the app root level
 */
const SessionManager: React.FC = () => {
  const { isAuthenticated, user } = useSessionManager();

  useEffect(() => {
    if (isAuthenticated && user) {
      // Only log once per session to reduce console spam
      const sessionKey = `session-logged-${user.id}`;
      if (!sessionStorage.getItem(sessionKey)) {
        console.log(`✅ Session active for user: ${user.username}`);
        sessionStorage.setItem(sessionKey, 'true');
      }
    }
  }, [isAuthenticated, user?.id]); // Only depend on user ID to reduce re-renders

  // This component doesn't render anything visible
  return null;
};

export default SessionManager;
