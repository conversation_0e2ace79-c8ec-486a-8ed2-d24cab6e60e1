/**
 * NAVIGATION CONFIGURATION TESTS
 * Tests for centralized navigation configuration and role-based access
 */

import {
  NAVIGATION_ITEMS,
  getNavigationItemsForRole,
  canAccessNavItem,
  NavItem
} from '../navigationConfig';
import { UserRole } from '../../utils/unifiedRoleManager';

describe('Navigation Configuration Tests', () => {
  
  describe('NAVIGATION_ITEMS Structure', () => {
    test('should have all required navigation items', () => {
      expect(NAVIGATION_ITEMS).toBeDefined();
      expect(Array.isArray(NAVIGATION_ITEMS)).toBe(true);
      expect(NAVIGATION_ITEMS.length).toBeGreaterThan(0);
    });

    test('should have valid structure for each navigation item', () => {
      NAVIGATION_ITEMS.forEach((item: NavItem) => {
        expect(item).toHaveProperty('id');
        expect(item).toHaveProperty('name');
        expect(item).toHaveProperty('path');
        expect(item).toHaveProperty('icon');
        expect(item).toHaveProperty('allowedRoles');
        expect(item).toHaveProperty('category');
        
        expect(typeof item.id).toBe('string');
        expect(typeof item.name).toBe('string');
        expect(typeof item.path).toBe('string');
        expect(Array.isArray(item.allowedRoles)).toBe(true);
        expect(typeof item.category).toBe('string');
      });
    });

    test('should have unique IDs for all navigation items', () => {
      const ids = NAVIGATION_ITEMS.map(item => item.id);
      const uniqueIds = new Set(ids);
      expect(ids.length).toBe(uniqueIds.size);
    });

    test('should have valid role assignments', () => {
      const validRoles: UserRole[] = ['super_admin', 'admin', 'moderator', 'mentor', 'investor', 'user'];
      
      NAVIGATION_ITEMS.forEach((item: NavItem) => {
        item.allowedRoles.forEach(role => {
          expect(validRoles).toContain(role);
        });
      });
    });

    test('should have valid categories', () => {
      const validCategories = ['main', 'content', 'system', 'security', 'super_admin', 'ai'];
      
      NAVIGATION_ITEMS.forEach((item: NavItem) => {
        expect(validCategories).toContain(item.category);
      });
    });
  });

  describe('Role-based Navigation Access', () => {
    test('super_admin should have access to all navigation items', () => {
      const superAdminItems = getNavigationItemsForRole('super_admin');
      
      // Super admin should see all items
      expect(superAdminItems.length).toBe(NAVIGATION_ITEMS.length);
      
      // Verify super admin can access critical items
      const criticalItems = ['super-admin-dashboard', 'system-monitoring', 'ai-system-management'];
      criticalItems.forEach(itemId => {
        expect(canAccessNavItem('super_admin', itemId)).toBe(true);
      });
    });

    test('admin should have access to admin and general items', () => {
      const adminItems = getNavigationItemsForRole('admin');
      
      // Admin should see admin and general items
      expect(adminItems.length).toBeGreaterThan(0);
      
      // Should access admin items
      expect(canAccessNavItem('admin', 'user-management')).toBe(true);
      expect(canAccessNavItem('admin', 'admin-analytics')).toBe(true);
      expect(canAccessNavItem('admin', 'system-settings')).toBe(true);
      
      // Should NOT access super admin items
      expect(canAccessNavItem('admin', 'super-admin-dashboard')).toBe(false);
      expect(canAccessNavItem('admin', 'system-monitoring')).toBe(false);
    });

    test('mentor should have access to mentor-specific and general items', () => {
      const mentorItems = getNavigationItemsForRole('mentor');
      
      expect(mentorItems.length).toBeGreaterThan(0);
      
      // Should access mentor items
      expect(canAccessNavItem('mentor', 'mentorship-sessions')).toBe(true);
      expect(canAccessNavItem('mentor', 'mentees')).toBe(true);
      expect(canAccessNavItem('mentor', 'business-ideas')).toBe(true);
      expect(canAccessNavItem('mentor', 'business-plans')).toBe(true);
      
      // Should NOT access admin items
      expect(canAccessNavItem('mentor', 'user-management')).toBe(false);
      expect(canAccessNavItem('mentor', 'system-settings')).toBe(false);
    });

    test('investor should have access to investor-specific and general items', () => {
      const investorItems = getNavigationItemsForRole('investor');
      
      expect(investorItems.length).toBeGreaterThan(0);
      
      // Should access investor items
      expect(canAccessNavItem('investor', 'investment-opportunities')).toBe(true);
      expect(canAccessNavItem('investor', 'portfolio')).toBe(true);
      expect(canAccessNavItem('investor', 'business-ideas')).toBe(true);
      expect(canAccessNavItem('investor', 'business-plans')).toBe(true);
      
      // Should NOT access admin items
      expect(canAccessNavItem('investor', 'user-management')).toBe(false);
      expect(canAccessNavItem('investor', 'system-settings')).toBe(false);
    });

    test('moderator should have access to moderation and general items', () => {
      const moderatorItems = getNavigationItemsForRole('moderator');
      
      expect(moderatorItems.length).toBeGreaterThan(0);
      
      // Should access moderator items
      expect(canAccessNavItem('moderator', 'content-moderation')).toBe(true);
      expect(canAccessNavItem('moderator', 'user-moderation')).toBe(true);
      
      // Should NOT access business items
      expect(canAccessNavItem('moderator', 'business-ideas')).toBe(false);
      expect(canAccessNavItem('moderator', 'business-plans')).toBe(false);
      
      // Should NOT access admin items
      expect(canAccessNavItem('moderator', 'user-management')).toBe(false);
    });

    test('regular user should have access to basic items only', () => {
      const userItems = getNavigationItemsForRole('user');
      
      expect(userItems.length).toBeGreaterThan(0);
      
      // Should access basic items
      expect(canAccessNavItem('user', 'dashboard')).toBe(true);
      expect(canAccessNavItem('user', 'business-ideas')).toBe(true);
      expect(canAccessNavItem('user', 'business-plans')).toBe(true);
      expect(canAccessNavItem('user', 'profile')).toBe(true);
      expect(canAccessNavItem('user', 'settings')).toBe(true);
      
      // Should NOT access admin items
      expect(canAccessNavItem('user', 'user-management')).toBe(false);
      expect(canAccessNavItem('user', 'system-settings')).toBe(false);
      expect(canAccessNavItem('user', 'super-admin-dashboard')).toBe(false);
      
      // Should NOT access role-specific items
      expect(canAccessNavItem('user', 'mentorship-sessions')).toBe(false);
      expect(canAccessNavItem('user', 'investment-opportunities')).toBe(false);
      expect(canAccessNavItem('user', 'content-moderation')).toBe(false);
    });
  });

  describe('Navigation Security Tests', () => {
    test('should not allow access to non-existent navigation items', () => {
      const roles: UserRole[] = ['super_admin', 'admin', 'moderator', 'mentor', 'investor', 'user'];
      
      roles.forEach(role => {
        expect(canAccessNavItem(role, 'non-existent-item')).toBe(false);
      });
    });

    test('should properly categorize high-risk items', () => {
      const highRiskItems = NAVIGATION_ITEMS.filter(item => 
        item.riskLevel === 'critical' || item.riskLevel === 'high'
      );
      
      highRiskItems.forEach(item => {
        // High-risk items should only be accessible to super_admin
        if (item.riskLevel === 'critical') {
          expect(item.allowedRoles).toEqual(['super_admin']);
        }
      });
    });

    test('should ensure proper role separation', () => {
      // Business-focused roles should not access system management
      const businessRoles: UserRole[] = ['user', 'mentor', 'investor'];
      const systemItems = NAVIGATION_ITEMS.filter(item => 
        item.category === 'system' || item.category === 'super_admin'
      );
      
      systemItems.forEach(item => {
        businessRoles.forEach(role => {
          if (!item.allowedRoles.includes(role)) {
            expect(canAccessNavItem(role, item.id)).toBe(false);
          }
        });
      });
    });

    test('should ensure all users can access basic navigation', () => {
      const basicItems = ['dashboard', 'profile', 'settings'];
      const allRoles: UserRole[] = ['super_admin', 'admin', 'moderator', 'mentor', 'investor', 'user'];
      
      basicItems.forEach(itemId => {
        allRoles.forEach(role => {
          expect(canAccessNavItem(role, itemId)).toBe(true);
        });
      });
    });
  });

  describe('Navigation Consistency Tests', () => {
    test('should have consistent path patterns', () => {
      NAVIGATION_ITEMS.forEach(item => {
        // All paths should start with /
        expect(item.path).toMatch(/^\//);
        
        // Admin paths should start with /admin or /super_admin
        if (item.allowedRoles.includes('admin') && !item.allowedRoles.includes('user')) {
          if (item.allowedRoles.includes('super_admin') && item.allowedRoles.length === 1) {
            expect(item.path).toMatch(/^\/super_admin/);
          }
        }
      });
    });

    test('should have proper role hierarchy in allowedRoles', () => {
      NAVIGATION_ITEMS.forEach(item => {
        // If super_admin is allowed, it should be in the array
        if (item.category === 'super_admin') {
          expect(item.allowedRoles).toContain('super_admin');
        }
        
        // If admin is allowed for system items, super_admin should also be allowed
        if (item.category === 'system' && item.allowedRoles.includes('admin')) {
          expect(item.allowedRoles).toContain('super_admin');
        }
      });
    });
  });
});
