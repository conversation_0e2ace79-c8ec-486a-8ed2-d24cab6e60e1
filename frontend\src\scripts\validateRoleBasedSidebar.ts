/**
 * ROLE-BASED SIDEBAR VALIDATION SCRIPT
 * Comprehensive validation of the role-based sidebar navigation system
 */

import { getUserRole, hasAnyRole, canAccessRoute, UserRole } from '../utils/unifiedRoleManager';
import { NAVIGATION_ITEMS, getNavigationItemsForRole, canAccessNavItem } from '../config/navigationConfig';
// Removed import - functionality moved to sidebarDebugger
import { User } from '../services/api';

interface ValidationResult {
  category: string;
  test: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
  details?: any;
}

class SidebarValidator {
  private results: ValidationResult[] = [];

  /**
   * Add a validation result
   */
  private addResult(category: string, test: string, status: 'PASS' | 'FAIL' | 'WARNING', message: string, details?: any) {
    this.results.push({ category, test, status, message, details });
  }

  /**
   * Test 1: Verify navigation configuration integrity
   */
  private testNavigationConfig() {
    const category = 'Navigation Configuration';

    // Test 1.1: All navigation items have required fields
    const invalidItems = NAVIGATION_ITEMS.filter(item => 
      !item.id || !item.name || !item.path || !item.icon || !item.allowedRoles || item.allowedRoles.length === 0
    );

    if (invalidItems.length === 0) {
      this.addResult(category, 'Required Fields', 'PASS', 'All navigation items have required fields');
    } else {
      this.addResult(category, 'Required Fields', 'FAIL', 
        `${invalidItems.length} items missing required fields`, 
        invalidItems.map(item => item.id)
      );
    }

    // Test 1.2: All icons are string-based (not JSX)
    const jsxIconItems = NAVIGATION_ITEMS.filter(item => typeof item.icon !== 'string');
    
    if (jsxIconItems.length === 0) {
      this.addResult(category, 'Icon Format', 'PASS', 'All icons are properly formatted as strings');
    } else {
      this.addResult(category, 'Icon Format', 'FAIL', 
        `${jsxIconItems.length} items have JSX icons instead of strings`, 
        jsxIconItems.map(item => item.id)
      );
    }

    // Test 1.3: All paths are valid
    const invalidPaths = NAVIGATION_ITEMS.filter(item => 
      !item.path.startsWith('/') || item.path.includes(' ') || item.path.length < 2
    );

    if (invalidPaths.length === 0) {
      this.addResult(category, 'Path Validation', 'PASS', 'All navigation paths are valid');
    } else {
      this.addResult(category, 'Path Validation', 'FAIL', 
        `${invalidPaths.length} items have invalid paths`, 
        invalidPaths.map(item => ({ id: item.id, path: item.path }))
      );
    }
  }

  /**
   * Test 2: Verify role-based filtering works correctly
   */
  private testRoleBasedFiltering() {
    const category = 'Role-Based Filtering';
    const roles: UserRole[] = ['user', 'mentor', 'investor', 'moderator', 'admin', 'super_admin'];

    roles.forEach(role => {
      const items = getNavigationItemsForRole(role);
      const invalidItems = items.filter(item => !item.allowedRoles.includes(role));

      if (invalidItems.length === 0) {
        this.addResult(category, `${role} Filtering`, 'PASS', 
          `All ${items.length} items for ${role} are properly filtered`);
      } else {
        this.addResult(category, `${role} Filtering`, 'FAIL', 
          `${invalidItems.length} items incorrectly shown to ${role}`, 
          invalidItems.map(item => item.id)
        );
      }
    });
  }

  /**
   * Test 3: Verify role hierarchy and permissions
   */
  private testRoleHierarchy() {
    const category = 'Role Hierarchy';

    // Test 3.1: Super admin should see all items
    const superAdminItems = getNavigationItemsForRole('super_admin');
    const totalSystemItems = NAVIGATION_ITEMS.filter(item => 
      item.allowedRoles.includes('super_admin')
    ).length;

    if (superAdminItems.length === totalSystemItems) {
      this.addResult(category, 'Super Admin Access', 'PASS', 
        `Super admin can access all ${superAdminItems.length} allowed items`);
    } else {
      this.addResult(category, 'Super Admin Access', 'FAIL', 
        `Super admin missing access to ${totalSystemItems - superAdminItems.length} items`);
    }

    // Test 3.2: Regular users should not see admin items
    const userItems = getNavigationItemsForRole('user');
    const adminOnlyItems = userItems.filter(item => 
      item.category === 'system' || item.category === 'super_admin'
    );

    if (adminOnlyItems.length === 0) {
      this.addResult(category, 'User Restrictions', 'PASS', 
        'Regular users cannot access admin-only items');
    } else {
      this.addResult(category, 'User Restrictions', 'FAIL', 
        `Regular users can access ${adminOnlyItems.length} admin-only items`, 
        adminOnlyItems.map(item => item.id)
      );
    }
  }

  /**
   * Test 4: Verify unified role manager integration
   */
  private testRoleManagerIntegration() {
    const category = 'Role Manager Integration';

    // Create test users for each role
    const testUsers: Record<UserRole, User> = {
      user: { id: 1, username: 'user', email: '<EMAIL>', first_name: 'User', last_name: 'Test', is_admin: false, is_staff: false, is_superuser: false, user_role: 'user' },
      mentor: { id: 2, username: 'mentor', email: '<EMAIL>', first_name: 'Mentor', last_name: 'Test', is_admin: false, is_staff: false, is_superuser: false, user_role: 'mentor' },
      investor: { id: 3, username: 'investor', email: '<EMAIL>', first_name: 'Investor', last_name: 'Test', is_admin: false, is_staff: false, is_superuser: false, user_role: 'investor' },
      moderator: { id: 4, username: 'moderator', email: '<EMAIL>', first_name: 'Moderator', last_name: 'Test', is_admin: false, is_staff: false, is_superuser: false, user_role: 'moderator' },
      admin: { id: 5, username: 'admin', email: '<EMAIL>', first_name: 'Admin', last_name: 'Test', is_admin: true, is_staff: true, is_superuser: false, user_role: 'admin' },
      super_admin: { id: 6, username: 'superadmin', email: '<EMAIL>', first_name: 'Super', last_name: 'Admin', is_admin: true, is_staff: true, is_superuser: true, user_role: 'super_admin' }
    };

    Object.entries(testUsers).forEach(([expectedRole, user]) => {
      const detectedRole = getUserRole(user);
      
      if (detectedRole === expectedRole) {
        this.addResult(category, `${expectedRole} Detection`, 'PASS', 
          `Role correctly detected as ${detectedRole}`);
      } else {
        this.addResult(category, `${expectedRole} Detection`, 'FAIL', 
          `Expected ${expectedRole}, got ${detectedRole}`);
      }
    });
  }

  /**
   * Test 5: Verify no duplicate navigation items
   */
  private testNoDuplicates() {
    const category = 'Duplicate Prevention';

    // Check for duplicate IDs
    const ids = NAVIGATION_ITEMS.map(item => item.id);
    const duplicateIds = ids.filter((id, index) => ids.indexOf(id) !== index);

    if (duplicateIds.length === 0) {
      this.addResult(category, 'Unique IDs', 'PASS', 'All navigation items have unique IDs');
    } else {
      this.addResult(category, 'Unique IDs', 'FAIL', 
        `Found duplicate IDs: ${duplicateIds.join(', ')}`);
    }

    // Check for duplicate paths
    const paths = NAVIGATION_ITEMS.map(item => item.path);
    const duplicatePaths = paths.filter((path, index) => paths.indexOf(path) !== index);

    if (duplicatePaths.length === 0) {
      this.addResult(category, 'Unique Paths', 'PASS', 'All navigation items have unique paths');
    } else {
      this.addResult(category, 'Unique Paths', 'FAIL', 
        `Found duplicate paths: ${duplicatePaths.join(', ')}`);
    }
  }

  /**
   * Run all validation tests
   */
  public validate(): ValidationResult[] {
    this.results = [];

    console.log('🧪 Starting Role-Based Sidebar Validation...\n');

    this.testNavigationConfig();
    this.testRoleBasedFiltering();
    this.testRoleHierarchy();
    this.testRoleManagerIntegration();
    this.testNoDuplicates();

    return this.results;
  }

  /**
   * Generate a comprehensive validation report
   */
  public generateReport(): string {
    const results = this.validate();
    
    let report = '🔍 ROLE-BASED SIDEBAR VALIDATION REPORT\n';
    report += '=' .repeat(60) + '\n\n';

    const categories = [...new Set(results.map(r => r.category))];
    
    let totalTests = 0;
    let passedTests = 0;
    let failedTests = 0;
    let warnings = 0;

    categories.forEach(category => {
      report += `📋 ${category}\n`;
      report += '-' .repeat(40) + '\n';

      const categoryResults = results.filter(r => r.category === category);
      
      categoryResults.forEach(result => {
        totalTests++;
        const icon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⚠️';
        report += `${icon} ${result.test}: ${result.message}\n`;
        
        if (result.details) {
          report += `   Details: ${JSON.stringify(result.details, null, 2)}\n`;
        }

        if (result.status === 'PASS') passedTests++;
        else if (result.status === 'FAIL') failedTests++;
        else warnings++;
      });
      
      report += '\n';
    });

    report += '=' .repeat(60) + '\n';
    report += `📊 SUMMARY\n`;
    report += `Total Tests: ${totalTests}\n`;
    report += `✅ Passed: ${passedTests}\n`;
    report += `❌ Failed: ${failedTests}\n`;
    report += `⚠️ Warnings: ${warnings}\n`;
    report += `Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%\n\n`;

    if (failedTests === 0) {
      report += '🎉 ALL TESTS PASSED! Role-based sidebar is working correctly.\n';
    } else {
      report += '⚠️ SOME TESTS FAILED. Please review the issues above.\n';
    }

    return report;
  }
}

/**
 * Main validation function
 */
export function validateRoleBasedSidebar(): boolean {
  const validator = new SidebarValidator();
  const report = validator.generateReport();
  
  console.log(report);
  
  // Also run the navigation tests
  console.log('\n' + generateNavigationTestReport());
  
  const results = validator.validate();
  const hasFailures = results.some(r => r.status === 'FAIL');
  
  return !hasFailures;
}

// Export for use in other files
export { SidebarValidator, ValidationResult };

/**
 * Quick test function that can be called from browser console
 */
(window as any).testSidebar = validateRoleBasedSidebar;
