/**
 * Moderation API Service
 * Handles all moderation-related API calls
 */

import { apiRequest } from './api';

// Types for moderation
export interface ContentItem {
  id: string;
  type: 'post' | 'comment' | 'resource' | 'business_idea';
  title: string;
  content: string;
  author: {
    id: string;
    name: string;
    email: string;
  };
  createdAt: string;
  reportedAt?: string;
  reportedBy?: string;
  reportReason?: string;
  status: 'pending' | 'approved' | 'rejected' | 'flagged';
  priority: 'low' | 'medium' | 'high';
  category: string;
  flags: number;
  views: number;
}

export interface UserAccount {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  status: 'active' | 'suspended' | 'banned';
  joinDate: string;
  lastActive: string;
  totalPosts: number;
  totalComments: number;
  reportsReceived: number;
  reportsSubmitted: number;
  warningsIssued: number;
  profileCompleteness: number;
  verificationStatus: 'verified' | 'pending' | 'unverified';
}

export interface ModerationAction {
  id: string;
  userId: string;
  userName: string;
  action: 'warning' | 'suspension' | 'ban' | 'content_removal';
  reason: string;
  duration?: string;
  moderatorId: string;
  moderatorName: string;
  timestamp: string;
  notes?: string;
}

/**
 * Content Moderation API
 */
export const contentModerationApi = {
  // Get all content items for moderation
  async getContentItems(): Promise<ContentItem[]> {
    try {
      const response = await apiRequest<{ results: ContentItem[] }>('/admin/content-moderation/', 'GET');
      return response.results || [];
    } catch (error) {
      console.error('Failed to fetch content items:', error);
      throw error;
    }
  },

  // Update content item status
  async updateContentStatus(id: string, status: ContentItem['status'], reason?: string): Promise<ContentItem> {
    try {
      const response = await apiRequest<ContentItem>(`/admin/content-moderation/${id}/`, 'PATCH', {
        status,
        reason
      });
      return response;
    } catch (error) {
      console.error('Failed to update content status:', error);
      throw error;
    }
  },

  // Delete content item
  async deleteContent(id: string, reason: string): Promise<void> {
    try {
      await apiRequest(`/admin/content-moderation/${id}/`, 'DELETE', { reason });
    } catch (error) {
      console.error('Failed to delete content:', error);
      throw error;
    }
  }
};

/**
 * User Moderation API
 */
export const userModerationApi = {
  // Get all users for moderation
  async getUsers(): Promise<UserAccount[]> {
    try {
      const response = await apiRequest<{ results: UserAccount[] }>('/admin/user-moderation/', 'GET');
      return response.results || [];
    } catch (error) {
      console.error('Failed to fetch users:', error);
      throw error;
    }
  },

  // Update user status
  async updateUserStatus(id: string, status: UserAccount['status'], reason?: string): Promise<UserAccount> {
    try {
      const response = await apiRequest<UserAccount>(`/admin/user-moderation/${id}/`, 'PATCH', {
        status,
        reason
      });
      return response;
    } catch (error) {
      console.error('Failed to update user status:', error);
      throw error;
    }
  },

  // Apply moderation action
  async applyModerationAction(action: Omit<ModerationAction, 'id' | 'timestamp'>): Promise<ModerationAction> {
    try {
      const response = await apiRequest<ModerationAction>('/admin/moderation-actions/', 'POST', action);
      return response;
    } catch (error) {
      console.error('Failed to apply moderation action:', error);
      throw error;
    }
  },

  // Get moderation history
  async getModerationHistory(userId?: string): Promise<ModerationAction[]> {
    try {
      const url = userId ? `/admin/moderation-actions/?user=${userId}` : '/admin/moderation-actions/';
      const response = await apiRequest<{ results: ModerationAction[] }>(url, 'GET');
      return response.results || [];
    } catch (error) {
      console.error('Failed to fetch moderation history:', error);
      throw error;
    }
  }
};

/**
 * Forum Moderation API
 */
export const forumModerationApi = {
  // Get forum threads for moderation
  async getForumThreads(): Promise<any[]> {
    try {
      const response = await apiRequest<{ results: any[] }>('/admin/forum-moderation/', 'GET');
      return response.results || [];
    } catch (error) {
      console.error('Failed to fetch forum threads:', error);
      throw error;
    }
  },

  // Moderate forum thread
  async moderateThread(threadId: string, action: string, reason?: string): Promise<any> {
    try {
      const response = await apiRequest(`/admin/forum-moderation/${threadId}/`, 'PATCH', {
        action,
        reason
      });
      return response;
    } catch (error) {
      console.error('Failed to moderate thread:', error);
      throw error;
    }
  }
};

/**
 * Reports Management API
 */
export const reportsApi = {
  // Get all reports
  async getReports(): Promise<any[]> {
    try {
      const response = await apiRequest<{ results: any[] }>('/admin/reports/', 'GET');
      return response.results || [];
    } catch (error) {
      console.error('Failed to fetch reports:', error);
      throw error;
    }
  },

  // Update report status
  async updateReportStatus(reportId: string, status: string, resolution?: string): Promise<any> {
    try {
      const response = await apiRequest(`/admin/reports/${reportId}/`, 'PATCH', {
        status,
        resolution
      });
      return response;
    } catch (error) {
      console.error('Failed to update report status:', error);
      throw error;
    }
  }
};

// Export all moderation APIs
export const moderationApi = {
  content: contentModerationApi,
  users: userModerationApi,
  forum: forumModerationApi,
  reports: reportsApi
};

export default moderationApi;
