import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { 
  Target, 
  Plus, 
  Edit, 
  Trash2, 
  Calendar, 
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Filter,
  TrendingUp
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useAppSelector } from '../../store/hooks';
import { isAdmin } from '../../utils/unifiedRoleManager';
import AuthenticatedLayout from '../../components/layout/AuthenticatedLayout';
import { BusinessIdea, businessIdeasAPI } from '../../services/incubatorApi';
import GoalForm from '../../components/incubator/GoalForm';
import ConfirmDialog from '../../components/common/ConfirmDialog';
import useCRUD from '../../hooks/useCRUD';

interface Goal {
  id: number;
  title: string;
  description: string;
  target_date: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'delayed' | 'cancelled';
  timeframe: 'short_term' | 'medium_term' | 'long_term';
  business_idea: number;
  target_value?: string;
  current_value?: string;
  measurement_unit?: string;
  completion_notes?: string;
  created_at: string;
  updated_at: string;
}

// Mock API for goals (replace with actual API)
const goalsAPI = {
  getGoals: async (): Promise<Goal[]> => {
    // This would be replaced with actual API call
    return [];
  },
  createGoal: async (data: Partial<Goal>): Promise<Goal> => {
    // This would be replaced with actual API call
    return { id: Date.now(), ...data } as Goal;
  },
  updateGoal: async (id: number, data: Partial<Goal>): Promise<Goal> => {
    // This would be replaced with actual API call
    return { id, ...data } as Goal;
  },
  deleteGoal: async (id: number): Promise<void> => {
    // This would be replaced with actual API call
  }
};

const GoalsPage: React.FC = () => {
  const { businessIdeaId } = useParams<{ businessIdeaId: string }>();
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);

  const [businessIdea, setBusinessIdea] = useState<BusinessIdea | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showGoalForm, setShowGoalForm] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [editingGoal, setEditingGoal] = useState<Goal | null>(null);
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [timeframeFilter, setTimeframeFilter] = useState<string | null>(null);

  // CRUD operations for goals
  const goalsCRUD = useCRUD({
    create: goalsAPI.createGoal,
    read: () => goalsAPI.getGoals(),
    update: goalsAPI.updateGoal,
    delete: goalsAPI.deleteGoal
  }, {
    onSuccess: (operation) => {
      if (operation === 'create') {
        setShowGoalForm(false);
      } else if (operation === 'update') {
        setEditingGoal(null);
        setShowGoalForm(false);
      } else if (operation === 'delete') {
        setShowDeleteDialog(false);
      }
    }
  });

  useEffect(() => {
    if (businessIdeaId) {
      fetchBusinessIdea();
      goalsCRUD.readItems();
    }
  }, [businessIdeaId]);

  const fetchBusinessIdea = async () => {
    if (!businessIdeaId) return;
    
    try {
      setLoading(true);
      setError(null);
      const idea = await businessIdeasAPI.getBusinessIdea(parseInt(businessIdeaId));
      setBusinessIdea(idea);
    } catch (err) {
      setError(err instanceof Error ? err.message : t('common.error.unknown'));
    } finally {
      setLoading(false);
    }
  };

  const handleCreateGoal = () => {
    setEditingGoal(null);
    setShowGoalForm(true);
  };

  const handleEditGoal = (goal: Goal) => {
    setEditingGoal(goal);
    setShowGoalForm(true);
  };

  const handleDeleteGoal = (goal: Goal) => {
    goalsCRUD.setSelectedItem(goal);
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    if (goalsCRUD.selectedItem) {
      await goalsCRUD.deleteItem(goalsCRUD.selectedItem.id);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle size={16} className="text-green-400" />;
      case 'in_progress':
        return <Clock size={16} className="text-blue-400" />;
      case 'delayed':
        return <AlertTriangle size={16} className="text-yellow-400" />;
      case 'cancelled':
        return <XCircle size={16} className="text-red-400" />;
      default:
        return <Target size={16} className="text-gray-400" />;
    }
  };

  const getTimeframeColor = (timeframe: string) => {
    switch (timeframe) {
      case 'short_term':
        return 'bg-green-600';
      case 'medium_term':
        return 'bg-yellow-600';
      case 'long_term':
        return 'bg-blue-600';
      default:
        return 'bg-gray-600';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-400 bg-green-900/30';
      case 'in_progress':
        return 'text-blue-400 bg-blue-900/30';
      case 'delayed':
        return 'text-yellow-400 bg-yellow-900/30';
      case 'cancelled':
        return 'text-red-400 bg-red-900/30';
      default:
        return 'text-gray-400 bg-gray-900/30';
    }
  };

  const calculateProgress = (goal: Goal) => {
    if (!goal.target_value || !goal.current_value) return null;
    
    const target = parseFloat(goal.target_value);
    const current = parseFloat(goal.current_value);
    
    if (isNaN(target) || isNaN(current) || target === 0) return null;
    
    return Math.min(Math.round((current / target) * 100), 100);
  };

  const filteredGoals = goalsCRUD.data.filter(goal => {
    const matchesStatus = !statusFilter || goal.status === statusFilter;
    const matchesTimeframe = !timeframeFilter || goal.timeframe === timeframeFilter;
    return matchesStatus && matchesTimeframe;
  });

  const canEdit = businessIdea && user && (
    businessIdea.owner.id === user.id ||
    businessIdea.collaborators?.some(collab => collab.id === user.id) ||
    isAdmin(user) // FIXED: Use unified role manager
  );

  if (loading) {
    return (
      <AuthenticatedLayout>
      <div className={`min-h-screen bg-gradient-to-b from-indigo-950 to-purple-950 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className={`p-4 md:p-6 lg:p-8 ${isRTL ? "flex-row-reverse" : ""}`}>
          <div className="max-w-7xl mx-auto w-full">
            <div className="flex justify-center items-center h-64">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
                </div>
        </div>
      </div>
    </AuthenticatedLayout>
    );
  }

  if (error || !businessIdea) {
    return (
      <AuthenticatedLayout>
        <div className="text-center py-12">
          <Target size={48} className="mx-auto text-gray-600 mb-4" />
          <h3 className="text-lg font-medium text-gray-400 mb-2">
            {error || t('incubator.businessIdea.notFound')}
          </h3>
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout>
      <div className="p-6">
        {/* Header */}
        <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div>
            <h1 className="text-2xl font-bold text-white">
              {t('incubator.goals.title')}
            </h1>
            <p className="text-gray-400 mt-1">
              {t('incubator.goals.forIdea')}: {businessIdea.title}
            </p>
          </div>

          {canEdit && (
            <button
              onClick={handleCreateGoal}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors flex items-center text-white"
            >
              <Plus size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
              {t('incubator.goals.create')}
            </button>
          )}
        </div>

        {/* Filters */}
        <div className={`flex gap-4 mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <button
              onClick={() => setStatusFilter(null)}
              className={`px-3 py-2 rounded-lg text-sm flex items-center ${
                statusFilter === null ? 'bg-gray-700 text-white' : 'bg-gray-800 text-gray-400 hover:text-white'
              }`}
            >
              <Filter size={14} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
              {t('common.all')}
            </button>
            {['not_started', 'in_progress', 'completed', 'delayed', 'cancelled'].map(status => (
              <button
                key={status}
                onClick={() => setStatusFilter(status)}
                className={`px-3 py-2 rounded-lg text-sm flex items-center ${
                  statusFilter === status ? 'bg-gray-700 text-white' : 'bg-gray-800 text-gray-400 hover:text-white'
                }`}
              >
                {getStatusIcon(status)}
                <span className={`${isRTL ? 'mr-1' : 'ml-1'}`}>
                  {t(`incubator.goal.status.${status}`)}
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* Goals List */}
        {goalsCRUD.isLoading ? (
          <div className="flex justify-center py-12">
            <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        ) : filteredGoals.length > 0 ? (
          <div className="space-y-4">
            {filteredGoals.map((goal) => {
              const progress = calculateProgress(goal);
              return (
                <div
                  key={goal.id}
                  className="bg-gray-800/50 rounded-lg p-6 border border-gray-700 hover:border-gray-600 transition-colors"
                >
                  <div className={`flex items-start justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className="flex-1">
                      <div className={`flex items-center gap-3 mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <h3 className="text-lg font-semibold text-white">{goal.title}</h3>
                        <span className={`px-2 py-1 rounded text-xs ${getTimeframeColor(goal.timeframe)}`}>
                          {t(`incubator.goal.timeframe.${goal.timeframe}`)}
                        </span>
                        <span className={`px-2 py-1 rounded text-xs ${getStatusColor(goal.status)}`}>
                          {getStatusIcon(goal.status)}
                          <span className={`${isRTL ? 'mr-1' : 'ml-1'}`}>
                            {t(`incubator.goal.status.${goal.status}`)}
                          </span>
                        </span>
                      </div>
                      <p className={`text-gray-300 mb-3 ${isRTL ? 'text-right' : ''}`}>
                        {goal.description}
                      </p>
                      
                      {/* Progress Bar */}
                      {progress !== null && (
                        <div className="mb-3">
                          <div className={`flex justify-between items-center mb-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <span className="text-sm text-gray-400">
                              {t('incubator.goal.progress')}
                            </span>
                            <span className="text-sm text-blue-400">
                              {goal.current_value} / {goal.target_value} {goal.measurement_unit}
                            </span>
                          </div>
                          <div className="w-full bg-gray-700 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${progress}%` }}
                            ></div>
                          </div>
                          <div className={`text-xs text-gray-400 mt-1 ${isRTL ? 'text-right' : ''}`}>
                            {progress}% {t('common.complete')}
                          </div>
                        </div>
                      )}

                      <div className={`flex items-center gap-4 text-sm text-gray-400 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <Calendar size={14} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
                          {t('incubator.goal.targetDate')}: {new Date(goal.target_date).toLocaleDateString()}
                        </div>
                        {goal.target_value && goal.measurement_unit && (
                          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <TrendingUp size={14} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
                            {t('incubator.goal.target')}: {goal.target_value} {goal.measurement_unit}
                          </div>
                        )}
                      </div>
                      
                      {goal.completion_notes && (
                        <div className="mt-3 p-3 bg-blue-900/20 rounded-lg">
                          <p className={`text-blue-300 text-sm ${isRTL ? 'text-right' : ''}`}>
                            <strong>{t('incubator.goal.completionNotes')}:</strong> {goal.completion_notes}
                          </p>
                        </div>
                      )}
                    </div>

                    {canEdit && (
                      <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <button
                          onClick={() => handleEditGoal(goal)}
                          className="p-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
                          title={t('common.edit')}
                        >
                          <Edit size={16} />
                        </button>
                        <button
                          onClick={() => handleDeleteGoal(goal)}
                          className="p-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
                          title={t('common.delete')}
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-12">
            <Target size={48} className="mx-auto text-gray-600 mb-4" />
            <h3 className="text-lg font-medium text-gray-400 mb-2">
              {statusFilter || timeframeFilter
                ? t('incubator.goals.noFilteredGoals')
                : t('incubator.goals.noGoals')
              }
            </h3>
            {canEdit && !statusFilter && !timeframeFilter && (
              <button
                onClick={handleCreateGoal}
                className="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors text-white"
              >
                {t('incubator.goals.createFirst')}
              </button>
            )}
          </div>
        )}
      </div>

      {/* Modals */}
      {showGoalForm && businessIdea && (
        <GoalForm
          goal={editingGoal || undefined}
          businessIdea={businessIdea}
          onSave={editingGoal ? 
            (data) => goalsCRUD.updateItem(editingGoal.id, data) :
            goalsCRUD.createItem
          }
          onCancel={() => {
            setShowGoalForm(false);
            setEditingGoal(null);
          }}
          isLoading={goalsCRUD.isLoading}
          mode={editingGoal ? 'edit' : 'create'}
        />
      )}

      <ConfirmDialog
        isOpen={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        onConfirm={confirmDelete}
        title={t('incubator.goals.deleteTitle')}
        message={t('incubator.goals.deleteMessage', { 
          title: goalsCRUD.selectedItem?.title 
        })}
        confirmText={t('common.delete')}
        type="danger"
        isLoading={goalsCRUD.isLoading}
      />
    </AuthenticatedLayout>
  );
};

export default GoalsPage;
