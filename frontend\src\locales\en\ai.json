{"ai": {"consolidatedAssistant": "Consolidated AI Assistant", "title": "🤖 AI Assistant", "dashboard": "AI Dashboard", "recommendations": "AI Recommendations", "loading": "Loading Your AI Activity...", "fetchingPersonal": "Fetching your personal AI recommendations", "viewDashboard": "View Dashboard", "monitors": "Monitors", "intelligent": {"alerts": "Intelligent Alerts", "matching": {"with": "Intelligent matching with mentors and investors based on your profile"}}, "clear": {"understanding": {"of": "Clear understanding of"}}, "time": {"estimates": "Time Estimates"}, "continuous": {"analysis": "Continuous analysis"}, "regular": {"insights": {"and": "Regular insights and optimization"}}, "comprehensive": {"assessment": {"of": "Comprehensive assessment of your business progress"}}, "swot": {"analysis": "SWOT Analysis"}, "competitor": {"research": "Competitor Research"}, "risk": {"warnings": "Risk Warnings", "analysis": "Risk Analysis"}, "proactive": {"recommendations": "Proactive Recommendations", "guidance": "Proactive guidance based on your business stage and goals"}, "ai": {"actively": {"helps": "AI actively helps you build better businesses"}}, "progress": {"tracking": "Progress Tracking", "visibility": "Progress visibility across all your initiatives", "title": "AI Analysis Progress", "completeness": "Completeness"}, "personalized": {"recommendations": "Personalized Recommendations"}, "smart": {"action": {"management": "Smart Action Management"}, "deadlines": "Smart Deadlines", "networking": "Smart Networking"}, "validation": "Validation", "market": {"trends": "Market Trends"}, "realtime": "Real-time", "opportunity": {"alerts": "Opportunity Alerts"}, "competitive": {"intelligence": "Competitive Intelligence"}, "growth": {"predictions": "Growth Predictions"}, "network": {"analysis": "Network Analysis"}, "tracks": {"your": {"progress": "Tracks your progress and activity"}}, "analyzes": "Analyzes", "understands": {"context": {"and": "Understands context and challenges"}}, "recommends": "Recommends", "suggests": {"specific": {"actions": "Suggests specific actions"}}, "guides": "Guides", "helps": {"you": {"execute": "Helps you execute"}}, "multiple": "Multiple", "updates": "Updates", "accuracy": "Accuracy", "accurate": {"recommendations": "Accurate recommendations"}, "languages": "Languages", "multi": "Multi", "chat": {"title": "AI Chat", "description": "Advanced AI conversation with intelligent responses", "aiDescription": "Your intelligent AI assistant for business insights and support", "yasmeenTitle": "<PERSON><PERSON><PERSON> AI Assistant", "welcomeMessage": "Hello {{userName}}! I'm <PERSON><PERSON><PERSON>, your AI assistant. How can I help you with your business ideas today?", "errorMessage": "Sorry, there was an error processing your message. Please try again.", "authErrorMessage": "Your session has expired. Please refresh the page or log in again to continue chatting.", "sessionRefreshed": "Session refreshed successfully! You can now continue chatting.", "sessionRefreshFailed": "Session refresh failed. Please log in again to continue.", "sessionRefreshError": "Unable to refresh session. Please try logging in again.", "refreshing": "Refreshing...", "refreshSession": "Refresh Session", "messagePlaceholder": "Type your message here...", "connected": "Connected", "disconnected": "Disconnected", "enhanced": "Enhanced", "cultural": "Cultural Context", "mlInsights": "ML Insights", "advancedWorkflows": "Advanced Workflows", "complexQuery": "Complex query - Advanced workflow will be used"}, "beyondChat": "AI Beyond Chat", "predictiveIntelligence": "Predictive Intelligence", "enhancedAnalytics": "Enhanced Analytics Demo", "status": {"title": "AI Status", "description": "Monitor the health and performance of AI services and integrations", "active": "AI workers are actively enhancing your business ideas", "inactive": "AI workers are currently inactive", "processing": "AI is processing your requests", "error": "AI services are experiencing issues", "connected": "Connected", "disconnected": "Disconnected", "healthy": "Healthy", "degraded": "Degraded", "down": "Down", "accessDenied": "Access Denied", "superAdminOnly": "AI system status monitoring is only available to super administrators for security reasons."}, "smartTemplateRecommendations": "Smart Template Recommendations", "recommendationsDescription": "AI-powered template suggestions based on your business needs", "active": "AI Active", "enhancement": {"title": "AI-Enhanced Creation Experience", "description": "Our AI will provide real-time suggestions, auto-complete fields, and help optimize your business idea as you work."}, "realTimeEnhancement": "Real-time AI Enhancement", "smartCompletion": "Smart Auto-completion", "milestone": {"tracking": "Milestone Tracking", "reminders": "Milestone reminders and progress tracking"}, "context": {"analysis": "Context Analysis"}, "business": {"health": {"monitoring": "Business Health Monitoring"}}, "bottleneck": {"detection": "Bottleneck Detection"}, "prioritized": {"actions": {"with": "Prioritized actions with intelligent scheduling"}}, "priority": {"ranking": "Priority Ranking"}, "quickActions": "Quick AI Actions", "actions": {"marketAnalysis": "Run Market Analysis", "competitorAnalysis": "Analyze Competitors", "generateIdeas": "Generate Related Ideas"}, "predictive": {"analytics": "Predictive Analytics"}, "aipowered": {"predictions": {"for": "AI-powered predictions for success and market timing"}}, "success": {"prediction": "Success Prediction"}, "mentor": {"matching": "Mentor Matching"}, "investor": {"compatibility": "Investor Compatibility"}, "strategic": {"partnerships": "Strategic Partnerships"}, "personal": {"title": "Your AI Assistant", "working": "AI is actively working on your business ideas", "offline": "AI is currently offline", "cta": {"title": "Get AI Help with Your Ideas!", "description": "AI analyzes your business ideas and provides personalized recommendations.", "button": "View AI Dashboard"}}, "stats": {"actionsToday": "Actions Today", "ideasEnhanced": "Ideas Enhanced", "opportunities": "Opportunities", "uptime": "AI Uptime"}, "recentWork": "Recent AI Work for You", "recentActions": {"title": "Recent AI Actions", "noActions": "No recent AI actions", "viewAll": "View All Actions"}, "cta": {"title": "AI is Working for You 24/7!", "description": "Your AI assistants continuously analyze and enhance your business ideas automatically.", "button": "View Full Dashboard"}, "showMore": "Show More", "analysis": {"title": "AI Analysis", "description": "Get intelligent insights and analysis of your community data and engagement patterns", "subtitle": "Deep insights and analytics from your AI assistant", "performance": "AI Performance Overview", "ideasAnalyzed": "Ideas Analyzed", "opportunitiesFound": "Opportunities Found", "risksIdentified": "Risks Identified", "recommendationsGenerated": "Recommendations Generated", "trends": "AI Activity Trends", "categories": "Analysis Categories", "comingSoon": "Advanced Analytics Coming Soon", "comingSoonDesc": "We're working on advanced charts, predictive analytics, and detailed insights.", "businessIdeaRequired": "Business Idea Required", "selectBusinessIdea": "Please select a business idea to access AI-powered analysis and insights", "browseBusinessIdeas": "Browse Business Ideas", "loadingBusinessIdeas": "Loading your business ideas...", "chooseIdeaDescription": "Choose one of your business ideas to get AI-powered analysis and insights", "analyzeIdea": "Analyze →", "advancedBusinessAnalysis": "Advanced Business Analysis", "comprehensiveAnalysisDesc": "Comprehensive analysis using advanced workflows and AI", "analyzing": "Analyzing...", "startAnalysis": "Start Analysis", "advancedWorkflows": "Advanced Workflows", "mlInsights": "ML Insights", "culturalContext": "Cultural Context", "processing": "Processing..."}, "timeRange": {"week": "Last 7 days", "month": "Last 30 days", "quarter": "Last 3 months"}, "access": {"title": "AI Access", "description": "View your AI capabilities and role-based access", "currentAccess": "Your Current AI Access", "activeRoles": "Active Roles", "aiFeatures": "AI Features", "chatLimit": "Chat Limit/Hour", "yourRoles": "Your Roles", "rateLimits": "Your AI Limits", "availableFeatures": "Available AI Features"}, "limits": {"chat": "Chat Messages", "analysis": "Analyses", "generation": "Generations", "perHour": "per hour", "perDay": "per day", "limit": "Limit", "hour": "hour", "day": "day"}, "yourAIAccess": "Your AI Access Level", "chatLimit": "Chat Messages", "analysisLimit": "Analysis", "generationLimit": "Generation", "availableCapabilities": "Available AI Capabilities", "upgradeAccess": "Upgrade Your AI Access", "upgradeDescription": "Apply for mentor or investor roles to unlock advanced AI capabilities and higher usage limits.", "applyForRole": "Apply for Advanced Role", "moreCapabilities": "Need More AI Capabilities?", "upgradeInfo": "Mentors and investors get access to advanced AI features, higher usage limits, and specialized tools for their roles.", "learnMentorship": "Learn About Mentorship", "learnInvesting": "Learn About Investing", "statusDetails": {"title": "AI Status", "description": "Monitor the health and performance of AI services and integrations"}, "settings": {"title": "AI Settings", "description": "Configure and customize your AI experience", "aiSettings": "AI Settings", "preferredLanguage": "Preferred Language", "autoDetect": "Auto Detect", "serviceManagement": "Service Management", "refreshStatus": "Refresh Status", "availableCapabilities": "Available Capabilities"}, "capabilities": {"advancedAI": "Advanced AI", "advancedAIDesc": "Sophisticated AI models with enhanced reasoning and contextual understanding", "workflows": "AI Workflows", "workflowsDesc": "Automated multi-step processes for complex business analysis and insights", "mlInsights": "ML Insights", "mlInsightsDesc": "Machine learning powered analytics for predictive business intelligence", "culturalContext": "Cultural Context", "culturalContextDesc": "Arabic language processing with cultural awareness and regional insights"}, "comingSoon": "AI Recommendations Coming Soon", "comingSoonDescription": "AI-powered template recommendations will be available soon"}, "aiGeneration": {"title": "AI Template Generator", "subtitle": "Create personalized business plan templates with AI", "description": "Use artificial intelligence to generate customized business plan templates tailored to your specific industry, business model, and requirements.", "startGenerating": "Start Generating", "generateNow": "Generate Now", "howItWorks": "How It Works", "whyChooseAI": "Why Choose AI Generation?", "whatUsersSay": "What Users Say", "readyToStart": "Ready to Start?", "readyToStartDesc": "Join thousands of entrepreneurs who have created successful business plans with our AI-powered template generator.", "simpleProcess": "Simple 4-Step Process", "stats": {"industries": "Industries", "sectionTypes": "Section Types", "avgTime": "Avg Time", "satisfaction": "Satisfaction"}, "features": {"intelligentAnalysis": "Intelligent Analysis", "intelligentAnalysisDesc": "AI analyzes your business description to understand your industry, target market, and unique requirements.", "industrySpecific": "Industry-Specific Templates", "industrySpecificDesc": "Generate templates tailored to your specific industry with relevant sections and guidance.", "fastGeneration": "Fast Generation", "fastGenerationDesc": "Get your customized business plan template in minutes, not hours.", "optimizedSuccess": "Optimized for Success", "optimizedSuccessDesc": "Templates are designed based on successful business plans and industry best practices."}, "benefits": {"saveTime": "Save Time", "expertGuidance": "Expert Guidance", "customized": "Fully Customized", "comprehensive": "Comprehensive", "aiPowered": "AI-Powered"}, "process": {"step1": "Describe Your Business", "step2": "AI Analyzes Requirements", "step3": "Template Generated", "step4": "Start Writing Your Plan"}, "step1": {"title": "Tell Us About Your Business"}, "businessDescription": "Business Description", "businessDescriptionPlaceholder": "Describe your business idea, industry, and target market...", "complexity": "Complexity", "sections": "Sections", "estimatedTime": "Estimated Time", "recommendations": {"marketResearch": "Conduct thorough market research", "financialProjections": "Develop detailed financial projections", "competitiveAnalysis": "Perform competitive analysis"}}, "workflows": {"title": "AI Workflows", "description": "Automated AI processes for your business", "businessPlan": "Business Plan Generation", "businessPlanDesc": "Generate comprehensive business plans automatically", "marketResearch": "Market Research Engine", "marketResearchDesc": "Automated market analysis and competitor research", "mentorMatching": "Mentor Matching", "active": "AI Workflows Active"}, "cta": {"title": "AI is Working for You 24/7!", "description": "Your AI assistants continuously analyze and enhance your business ideas automatically."}, "smart": {"forms": "Smart Forms", "formsDescription": "AI automatically fills forms and suggests improvements"}, "auto": {"content": {"generation": "Auto Content Generation"}, "contentDescription": "Automatically generate business plans and presentations"}, "executive": {"summary": {"company": "Executive Summary"}}, "company": {"description": "Company Description"}, "market": {"analysis": "Market Analysis"}, "competitive": {"analysis": "Competitive Analysis"}, "marketing": {"strategy": "Marketing Strategy"}, "operations": {"plan": "Operations Plan"}, "management": {"team": "Management Team"}, "financial": {"projections": "Financial Projections"}, "riskAssessment": "Risk Assessment", "marketSizing": "Market Sizing", "trendIdentification": "Trend Identification", "opportunityMapping": "Opportunity Mapping", "products": {"services": "Products & Services"}, "funding": {"request": "Funding Request"}, "gemini": {"assistant": "Gemini AI Assistant"}, "intelligent": {"business": {"analysis": "Intelligent Business Analysis", "advisor": "Intelligent Business Advisor"}}, "comprehensive": {"business": {"idea": "Comprehensive business idea analysis with viability scoring and recommendations"}}, "monitors": {"your": {"progress": "Monitors your progress and provides proactive recommendations based on your current business stage"}}, "proactive": {"recommendations": "Proactive Recommendations"}, "swot": {"analysis": "SWOT Analysis"}, "competitor": {"research": "Competitor Research"}, "risk": {"assessment": "Risk Assessment"}}