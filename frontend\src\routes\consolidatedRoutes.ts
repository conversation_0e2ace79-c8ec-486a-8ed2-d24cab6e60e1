/**
 * CONSOLIDATED ROUTES
 * Single file containing all application routes organized by access level
 * Replaces the fragmented 7-file routing system
 */

import { lazy } from 'react';
import { RouteConfig, createPublicRoute, createAuthRoute, createUserRoute, createRoleRoute, createAdminRoute, createSuperAdminRoute, createAIRoute, createUnifiedRoute } from './routeConfig';

// ===== EAGERLY LOADED COMPONENTS =====
// Core authentication pages
import LoginPage from '../pages/LoginPage';
import RegisterPage from '../pages/RegisterPage';
import HomePage from '../pages/HomePage';
import LogoutPage from '../pages/LogoutPage';
import { FeaturesPage } from '../pages/FeaturesPage';

// ===== LAZY LOADED COMPONENTS =====

// === PUBLIC COMPONENTS ===
const BusinessIncubator = lazy(() => import('../components/incubator/BusinessIncubator'));
const ReactQueryDemo = lazy(() => import('../components/examples/ReactQueryDemo'));
const AdvancedSearchPage = lazy(() => import('../pages/dashboard/AdvancedSearchPage'));
const TemplateBrowserPage = lazy(() => import('../pages/TemplateBrowserPage'));
const TemplateTestPage = lazy(() => import('../pages/TemplateTestPage'));
const TemplateSystemStatusPage = lazy(() => import('../pages/TemplateSystemStatusPage'));
const NewFeaturesTestPage = lazy(() => import('../pages/NewFeaturesTestPage'));
const UserProfilePage = lazy(() => import('../pages/UserProfilePage'));
const AppDiagnosticsPage = lazy(() => import('../pages/AppDiagnosticsPage'));

// === USER COMPONENTS ===
const UserDashboardPage = lazy(() => import('../pages/dashboard/UserDashboardPage'));
const BusinessIdeasPage = lazy(() => import('../pages/dashboard/BusinessIdeasPage'));
const BusinessPlansPage = lazy(() => import('../pages/dashboard/BusinessPlansPage'));
const TemplatesPage = lazy(() => import('../pages/dashboard/TemplatesPage'));
const PostsPage = lazy(() => import('../pages/dashboard/PostsPage'));
const EventsPage = lazy(() => import('../pages/dashboard/EventsPage'));
const ForumPage = lazy(() => import('../pages/ForumPage'));
const UserSettings = lazy(() => import('../pages/UserSettings'));
const ConsolidatedAIPage = lazy(() => import('../pages/ConsolidatedAIPage'));
const ResourcesPage = lazy(() => import('../pages/dashboard/ResourcesPage'));
const AnalyticsPage = lazy(() => import('../pages/dashboard/AnalyticsPage'));
const SystemValidationPage = lazy(() => import('../pages/SystemValidationPage'));
const AppHealthDashboard = lazy(() => import('../pages/AppHealthDashboard'));

// === ROLE-SPECIFIC DASHBOARD COMPONENTS ===
const ModeratorDashboardPage = lazy(() => import('../pages/moderator/ModeratorDashboardPage'));
const MentorDashboardPage = lazy(() => import('../pages/dashboard/MentorDashboardPage'));
const InvestorDashboardPage = lazy(() => import('../pages/investor/InvestorDashboardPage'));

// === MODERATOR COMPONENTS ===
const ContentModerationPage = lazy(() => import('../pages/dashboard/ContentModerationPage'));
const UserModerationPage = lazy(() => import('../pages/dashboard/ContentModerationPage'));
const ForumModerationPage = lazy(() => import('../pages/dashboard/ForumModerationPage'));
const ReportsManagementPage = lazy(() => import('../pages/dashboard/ReportsManagementPage'));
const ModerationAnalyticsPage = lazy(() => import('../pages/dashboard/ModerationAnalyticsPage'));

// === MENTOR COMPONENTS ===
const MentorshipDashboardPage = lazy(() => import('../pages/dashboard/MentorshipDashboardPage')); // For mentees seeking mentors
const MentorshipSessionsPage = lazy(() => import('../pages/dashboard/MentorshipSessionsPage'));
const MenteesManagementPage = lazy(() => import('../pages/dashboard/MenteesManagementPage'));
const MentorProfilePage = lazy(() => import('../pages/dashboard/MentorProfilePage'));
const MentorAnalyticsPage = lazy(() => import('../pages/dashboard/MentorAnalyticsPage'));
const MentorAvailabilityPage = lazy(() => import('../pages/dashboard/MentorAvailabilityPage'));
const MentorshipSessionSchedulePage = lazy(() => import('../pages/dashboard/MentorshipSessionSchedulePage'));
const MentorshipCalendarPage = lazy(() => import('../pages/dashboard/MentorshipCalendarPage'));
const MentorshipResourcesPage = lazy(() => import('../pages/dashboard/MentorshipResourcesPage'));

// === COMMUNITY COMPONENTS ===
const ForumsPage = lazy(() => import('../pages/dashboard/ForumsPage'));

// === AI COMPONENTS ===
const AIDashboardPage = lazy(() => import('../pages/dashboard/AIDashboardPage'));

// === INVESTMENT COMPONENTS ===
const InvestmentsDashboardPage = lazy(() => import('../pages/dashboard/InvestmentsDashboardPage'));

// === INVESTOR COMPONENTS ===
const InvestmentOpportunitiesPage = lazy(() => import('../pages/dashboard/InvestorOpportunitiesPage'));
const PortfolioManagementPage = lazy(() => import('../pages/dashboard/PortfolioManagementPage'));
const InvestorAnalyticsPage = lazy(() => import('../pages/dashboard/InvestorAnalyticsPage'));
const DueDiligencePage = lazy(() => import('../pages/dashboard/DueDiligencePage'));
const InvestorProfilePage = lazy(() => import('../pages/dashboard/InvestorProfilePage'));

// === ADMIN COMPONENTS ===
const AdminDashboardPage = lazy(() => import('../pages/admin/AdminDashboardPage'));
const UsersManagement = lazy(() => import('../components/admin/users/UsersManagement'));
const EventsManagement = lazy(() => import('../components/admin/content/EventsManagement'));
const ResourcesManagement = lazy(() => import('../components/admin/content/ResourcesManagement'));
const PostsManagementPage = lazy(() => import('../components/admin/content/posts/PostsManagementPage'));
const ModerationDashboard = lazy(() => import('../components/dashboard/moderator-dashboard/ModeratorDashboard'));
const CommunicationCenter = lazy(() => import('../components/admin/communication/CommunicationCenter'));
const ApiManagement = lazy(() => import('../components/admin/api/ApiManagement'));
const PerformanceCenter = lazy(() => import('../components/admin/performance/PerformanceCenter'));
const AISystemPage = lazy(() => import('../pages/admin/AISystemPage'));
const AdminAnalyticsPage = lazy(() => import('../pages/admin/AdminAnalyticsPage'));

// === SUPER ADMIN COMPONENTS ===
const SuperAdminDashboardPage = lazy(() => import('../pages/super-admin/SuperAdminDashboardPage'));
const SystemManagement = lazy(() => import('../components/admin/super-admin/SystemManagement'));
const UserImpersonation = lazy(() => import('../components/admin/super-admin/UserImpersonation'));
const AISystemManagement = lazy(() => import('../components/admin/super-admin/AISystemManagement'));
const AIConfiguration = lazy(() => import('../components/admin/super-admin/AIConfiguration'));
const AIAnalytics = lazy(() => import('../components/admin/super-admin/AIAnalytics'));
const AIMonitoring = lazy(() => import('../components/admin/super-admin/AIMonitoring'));
const SecurityCenter = lazy(() => import('../components/admin/super-admin/SecurityCenter'));
const APIManagement = lazy(() => import('../components/admin/super-admin/APIManagement'));
const SystemLogsPage = lazy(() => import('../pages/admin/SystemLogsPage'));
const SystemMonitoringPage = lazy(() => import('../components/admin/system/SystemMonitoring'));
const AdminSettingsPage = lazy(() => import('../components/admin/settings/AdminSettings'));

/**
 * ===== PUBLIC ROUTES =====
 * Routes that don't require authentication
 */
export const publicRoutes: RouteConfig[] = [
  // Core public pages
  createPublicRoute('/', HomePage),
  createPublicRoute('/features', FeaturesPage),

  // Auth pages (no layout wrapper)
  createAuthRoute('/login', LoginPage),
  createAuthRoute('/register', RegisterPage),
  createAuthRoute('/logout', LogoutPage),

  // Public features
  createPublicRoute('/incubator', BusinessIncubator, 'Loading business incubator...'),
  createPublicRoute('/react-query-demo', ReactQueryDemo, 'Loading React Query demo...'),

  // Forum
  createPublicRoute('/forum', ForumPage, 'Loading forum...'),

  // Public search and templates
  createPublicRoute('/search', AdvancedSearchPage, 'Loading search...'),
  createPublicRoute('/templates', TemplateBrowserPage, 'Loading template browser...'),
  createPublicRoute('/templates/test', TemplateTestPage, 'Loading template test...'),
  createPublicRoute('/templates/status', TemplateSystemStatusPage, 'Loading system status...'),

  // Public testing and diagnostics
  createPublicRoute('/test/new-features', NewFeaturesTestPage, 'Loading new features test...'),
  createPublicRoute('/diagnostics', AppDiagnosticsPage, 'Loading diagnostics...'),
  createPublicRoute('/system-validation', SystemValidationPage, 'Loading system validation...'),
  createPublicRoute('/app-health', AppHealthDashboard, 'Loading application health dashboard...')
];

/**
 * ===== USER ROUTES =====
 * Routes using UNIFIED role management - no hardcoded arrays
 */
export const userRoutes: RouteConfig[] = [
  // Main dashboard - uses centralized role mapping
  createUnifiedRoute('/dashboard', UserDashboardPage, 'Loading dashboard...'),

  // Core user functionality - uses centralized role mapping
  createUnifiedRoute('/dashboard/business-ideas', BusinessIdeasPage, 'Loading business ideas...'),
  createUnifiedRoute('/dashboard/business-ideas/new', BusinessIdeasPage, 'Loading business idea creator...'),
  createUnifiedRoute('/dashboard/business-plans', BusinessPlansPage, 'Loading business plans...'),
  createUnifiedRoute('/dashboard/templates', TemplatesPage, 'Loading templates...'),
  createUnifiedRoute('/dashboard/posts', PostsPage, 'Loading posts...'),
  createUnifiedRoute('/dashboard/events', EventsPage, 'Loading events...'),
  createUnifiedRoute('/dashboard/resources', ResourcesPage, 'Loading resources...'),
  createUnifiedRoute('/dashboard/analytics', AnalyticsPage, 'Loading analytics...'),

  // Incubator functionality - uses centralized role mapping
  createUnifiedRoute('/dashboard/incubator', BusinessIncubator, 'Loading incubator...'),

  // User settings and profile - uses centralized role mapping
  createUnifiedRoute('/settings', UserSettings, 'Loading settings...'),
  createUnifiedRoute('/profile', UserProfilePage, 'Loading profile...'),

  // AI functionality - uses centralized role mapping
  createUnifiedRoute('/dashboard/ai', AIDashboardPage, 'Loading AI Dashboard...'),
  createUnifiedRoute('/dashboard/chat', ConsolidatedAIPage, 'Loading chat...'), // FIXED: Path to match navigation

  // Community features - uses centralized role mapping
  createUnifiedRoute('/dashboard/forums', ForumsPage, 'Loading forums...'),

  // Mentorship for regular users (mentees) - uses centralized role mapping
  createUnifiedRoute('/dashboard/find-mentor', MentorshipDashboardPage, 'Loading mentorship finder...')
];

/**
 * ===== MODERATOR ROUTES =====
 * Routes using UNIFIED role management - no hardcoded arrays
 */
export const moderatorRoutes: RouteConfig[] = [
  // Moderator dashboard - uses centralized role mapping
  createUnifiedRoute('/dashboard/moderator', ModeratorDashboardPage, 'Loading moderator dashboard...'),

  // Content moderation - uses centralized role mapping
  createUnifiedRoute('/dashboard/moderation/content', ContentModerationPage, 'Loading content moderation...'),
  createUnifiedRoute('/dashboard/moderation/users', UserModerationPage, 'Loading user moderation...'),
  createUnifiedRoute('/dashboard/moderation/forum', ForumModerationPage, 'Loading forum moderation...'),
  createUnifiedRoute('/dashboard/moderation/reports', ReportsManagementPage, 'Loading reports management...'),
  createUnifiedRoute('/dashboard/moderation/analytics', ModerationAnalyticsPage, 'Loading moderation analytics...')
];

/**
 * ===== MENTOR ROUTES =====
 * Routes using UNIFIED role management - no hardcoded arrays
 */
export const mentorRoutes: RouteConfig[] = [
  // Mentor dashboard - uses centralized role mapping
  createUnifiedRoute('/dashboard/mentor', MentorDashboardPage, 'Loading mentor dashboard...'),

  // Mentorship management - uses centralized role mapping
  createUnifiedRoute('/dashboard/mentorship', MenteesManagementPage, 'Loading mentorship dashboard...'), // Default mentorship route
  createUnifiedRoute('/dashboard/mentorship/sessions', MentorshipSessionsPage, 'Loading mentorship sessions...'),
  createUnifiedRoute('/dashboard/mentorship/mentees', MenteesManagementPage, 'Loading mentees management...'),
  createUnifiedRoute('/dashboard/mentorship/calendar', MentorshipCalendarPage, 'Loading mentorship calendar...'),
  createUnifiedRoute('/dashboard/mentorship/profile', MentorProfilePage, 'Loading mentor profile...'),
  createUnifiedRoute('/dashboard/mentorship/analytics', MentorAnalyticsPage, 'Loading mentor analytics...'),
  createUnifiedRoute('/dashboard/mentorship/availability', MentorAvailabilityPage, 'Loading availability management...'),
  createUnifiedRoute('/dashboard/mentorship/sessions/schedule', MentorshipSessionSchedulePage, 'Loading session scheduler...'),
  createUnifiedRoute('/dashboard/mentorship/resources', MentorshipResourcesPage, 'Loading mentorship resources...')
];

/**
 * ===== INVESTOR ROUTES =====
 * Routes using UNIFIED role management - no hardcoded arrays
 */
export const investorRoutes: RouteConfig[] = [
  // Investor dashboard - uses centralized role mapping
  createUnifiedRoute('/dashboard/investor', InvestorDashboardPage, 'Loading investor dashboard...'),
  createUnifiedRoute('/dashboard/investments', InvestmentsDashboardPage, 'Loading investments dashboard...'),

  // Investment management - uses centralized role mapping
  createUnifiedRoute('/dashboard/investment/opportunities', InvestmentOpportunitiesPage, 'Loading investment opportunities...'), // FIXED: Path to match navigation
  createUnifiedRoute('/dashboard/investment/portfolio', PortfolioManagementPage, 'Loading portfolio management...'), // FIXED: Path to match navigation
  createUnifiedRoute('/dashboard/investments/analytics', InvestorAnalyticsPage, 'Loading investor analytics...'),
  createUnifiedRoute('/dashboard/investments/due-diligence', DueDiligencePage, 'Loading due diligence...'),
  createUnifiedRoute('/dashboard/investments/profile', InvestorProfilePage, 'Loading investor profile...')
];

/**
 * ===== ADMIN ROUTES =====
 * Routes using UNIFIED role management - no hardcoded arrays
 */
export const adminRoutes: RouteConfig[] = [
  // Admin dashboard - uses centralized role mapping
  createUnifiedRoute('/admin', AdminDashboardPage, 'Loading admin dashboard...'),

  // User management - uses centralized role mapping
  createUnifiedRoute('/admin/users', UsersManagement, 'Loading users management...'),

  // Analytics - uses centralized role mapping
  createUnifiedRoute('/admin/analytics', AdminAnalyticsPage, 'Loading admin analytics...'),

  // Content management - uses centralized role mapping
  createUnifiedRoute('/admin/events', EventsManagement, 'Loading events management...'),
  createUnifiedRoute('/admin/resources', ResourcesManagement, 'Loading resources management...'),
  createUnifiedRoute('/admin/posts', PostsManagementPage, 'Loading posts management...'),
  createUnifiedRoute('/admin/moderation', ModerationDashboard, 'Loading moderation dashboard...'),

  // Communication and system - uses centralized role mapping
  createUnifiedRoute('/admin/communication', CommunicationCenter, 'Loading communication center...'),
  createUnifiedRoute('/admin/api', ApiManagement, 'Loading API management...'),
  createUnifiedRoute('/admin/performance', PerformanceCenter, 'Loading performance center...'),

  // Settings - uses centralized role mapping
  createUnifiedRoute('/admin/settings', AdminSettingsPage, 'Loading admin settings...'), // ADDED: Missing route

  // AI system management (admin-level) - uses centralized role mapping
  createUnifiedRoute('/admin/ai-system', AISystemPage, 'Loading AI system management...')
];

/**
 * ===== SUPER ADMIN ROUTES =====
 * Routes using UNIFIED role management - no hardcoded arrays
 */
export const superAdminRoutes: RouteConfig[] = [
  // Super Admin dashboard - uses centralized role mapping
  createUnifiedRoute('/super_admin', SuperAdminDashboardPage, 'Loading Super Admin dashboard...'),

  // Super Admin exclusive features - uses centralized role mapping
  createUnifiedRoute('/super_admin/system-management', SystemManagement, 'Loading system management...'),
  createUnifiedRoute('/super_admin/user-impersonation', UserImpersonation, 'Loading user impersonation...'),
  createUnifiedRoute('/super_admin/ai-system-management', AISystemManagement, 'Loading AI system management...'),
  createUnifiedRoute('/super_admin/ai-configuration', AIConfiguration, 'Loading AI configuration...'),
  createUnifiedRoute('/super_admin/ai-analytics', AIAnalytics, 'Loading AI analytics...'),
  createUnifiedRoute('/super_admin/ai-monitoring', AIMonitoring, 'Loading AI monitoring...'),
  createUnifiedRoute('/super_admin/security', SecurityCenter, 'Loading security center...'),
  createUnifiedRoute('/super_admin/performance', PerformanceCenter, 'Loading performance center...'),
  createUnifiedRoute('/super_admin/api', APIManagement, 'Loading API management...'),
  createUnifiedRoute('/super_admin/communication', CommunicationCenter, 'Loading communication center...'),
  createUnifiedRoute('/super_admin/system-logs', SystemLogsPage, 'Loading system logs...'),
  createUnifiedRoute('/super_admin/monitoring', SystemMonitoringPage, 'Loading system monitoring...') // ADDED: Missing route
];

/**
 * ===== ALL ROUTES COMBINED =====
 * Export all routes in a single array for easy consumption
 */
export const allRoutes: RouteConfig[] = [
  ...publicRoutes,
  ...userRoutes,
  ...moderatorRoutes,
  ...mentorRoutes,
  ...investorRoutes,
  ...adminRoutes,
  ...superAdminRoutes
];

// Export individual route arrays for backward compatibility
export {
  publicRoutes as consolidatedPublicRoutes,
  userRoutes as consolidatedUserRoutes,
  moderatorRoutes as consolidatedModeratorRoutes,
  mentorRoutes as consolidatedMentorRoutes,
  investorRoutes as consolidatedInvestorRoutes,
  adminRoutes as consolidatedAdminRoutes,
  superAdminRoutes as consolidatedSuperAdminRoutes
};
