/**
 * CONSOLIDATED ROUTES
 * Single file containing all application routes organized by access level
 * Replaces the fragmented 7-file routing system
 */

import { lazy } from 'react';
import { RouteConfig, createPublicRoute, createAuthRoute, createUserRoute, createRoleRoute, createAdminRoute, createSuperAdminRoute, createAIRoute } from './routeConfig';

// ===== EAGERLY LOADED COMPONENTS =====
// Core authentication pages
import LoginPage from '../pages/LoginPage';
import RegisterPage from '../pages/RegisterPage';
import HomePage from '../pages/HomePage';
import LogoutPage from '../pages/LogoutPage';
import { FeaturesPage } from '../pages/FeaturesPage';

// ===== LAZY LOADED COMPONENTS =====

// === PUBLIC COMPONENTS ===
const BusinessIncubator = lazy(() => import('../components/incubator/BusinessIncubator'));
const ReactQueryDemo = lazy(() => import('../components/examples/ReactQueryDemo'));
const AdvancedSearchPage = lazy(() => import('../pages/dashboard/AdvancedSearchPage'));
const TemplateBrowserPage = lazy(() => import('../pages/TemplateBrowserPage'));
const TemplateTestPage = lazy(() => import('../pages/TemplateTestPage'));
const TemplateSystemStatusPage = lazy(() => import('../pages/TemplateSystemStatusPage'));
const NewFeaturesTestPage = lazy(() => import('../pages/NewFeaturesTestPage'));
const UserProfilePage = lazy(() => import('../pages/UserProfilePage'));
const AppDiagnosticsPage = lazy(() => import('../pages/AppDiagnosticsPage'));

// === USER COMPONENTS ===
const UserDashboardPage = lazy(() => import('../pages/dashboard/UserDashboardPage'));
const BusinessIdeasPage = lazy(() => import('../pages/dashboard/BusinessIdeasPage'));
const BusinessPlansPage = lazy(() => import('../pages/dashboard/BusinessPlansPage'));
const TemplatesPage = lazy(() => import('../pages/dashboard/TemplatesPage'));
const PostsPage = lazy(() => import('../pages/dashboard/PostsPage'));
const EventsPage = lazy(() => import('../pages/dashboard/EventsPage'));
const ForumPage = lazy(() => import('../pages/ForumPage'));
const UserSettings = lazy(() => import('../pages/UserSettings'));
const ConsolidatedAIPage = lazy(() => import('../pages/ConsolidatedAIPage'));
const ResourcesPage = lazy(() => import('../pages/dashboard/ResourcesPage'));
const AnalyticsPage = lazy(() => import('../pages/dashboard/AnalyticsPage'));
const SystemValidationPage = lazy(() => import('../pages/SystemValidationPage'));
const AppHealthDashboard = lazy(() => import('../pages/AppHealthDashboard'));

// === ROLE-SPECIFIC DASHBOARD COMPONENTS ===
const ModeratorDashboardPage = lazy(() => import('../pages/moderator/ModeratorDashboardPage'));
const MentorDashboardPage = lazy(() => import('../pages/mentor/MentorDashboardPage'));
const InvestorDashboardPage = lazy(() => import('../pages/investor/InvestorDashboardPage'));

// === MODERATOR COMPONENTS ===
const ContentModerationPage = lazy(() => import('../pages/dashboard/ContentModerationPage'));
const UserModerationPage = lazy(() => import('../pages/dashboard/UserModerationPage'));
const ForumModerationPage = lazy(() => import('../pages/dashboard/ForumModerationPage'));
const ReportsManagementPage = lazy(() => import('../pages/dashboard/ReportsManagementPage'));
const ModerationAnalyticsPage = lazy(() => import('../pages/dashboard/ModerationAnalyticsPage'));

// === MENTOR COMPONENTS ===
const MentorshipSessionsPage = lazy(() => import('../pages/dashboard/MentorshipSessionsPage'));
const MenteesManagementPage = lazy(() => import('../pages/dashboard/MenteesManagementPage'));
const MentorProfilePage = lazy(() => import('../pages/dashboard/MentorProfilePage'));
const MentorAnalyticsPage = lazy(() => import('../pages/dashboard/MentorAnalyticsPage'));
const MentorAvailabilityPage = lazy(() => import('../pages/dashboard/MentorAvailabilityPage'));

// === INVESTOR COMPONENTS ===
const InvestmentOpportunitiesPage = lazy(() => import('../pages/dashboard/InvestmentOpportunitiesPage'));
const PortfolioManagementPage = lazy(() => import('../pages/dashboard/PortfolioManagementPage'));
const InvestorAnalyticsPage = lazy(() => import('../pages/dashboard/InvestorAnalyticsPage'));
const DueDiligencePage = lazy(() => import('../pages/dashboard/DueDiligencePage'));
const InvestorProfilePage = lazy(() => import('../pages/dashboard/InvestorProfilePage'));

// === ADMIN COMPONENTS ===
const AdminDashboardPage = lazy(() => import('../pages/admin/AdminDashboardPage'));
const UsersManagement = lazy(() => import('../components/admin/users/UsersManagement'));
const EventsManagement = lazy(() => import('../components/admin/content/EventsManagement'));
const ResourcesManagement = lazy(() => import('../components/admin/content/ResourcesManagement'));
const PostsManagementPage = lazy(() => import('../components/admin/content/posts/PostsManagementPage'));
const ModerationDashboard = lazy(() => import('../components/dashboard/moderator-dashboard/ModeratorDashboard'));
const CommunicationCenter = lazy(() => import('../components/admin/communication/CommunicationCenter'));
const ApiManagement = lazy(() => import('../components/admin/api/ApiManagement'));
const PerformanceCenter = lazy(() => import('../components/admin/performance/PerformanceCenter'));
const AISystemPage = lazy(() => import('../pages/admin/AISystemPage'));

// === SUPER ADMIN COMPONENTS ===
const SuperAdminDashboardPage = lazy(() => import('../pages/super-admin/SuperAdminDashboardPage'));
const SystemManagement = lazy(() => import('../components/admin/super-admin/SystemManagement'));
const UserImpersonation = lazy(() => import('../components/admin/super-admin/UserImpersonation'));
const AISystemManagement = lazy(() => import('../components/admin/super-admin/AISystemManagement'));
const AIConfiguration = lazy(() => import('../components/admin/super-admin/AIConfiguration'));
const AIAnalytics = lazy(() => import('../components/admin/super-admin/AIAnalytics'));
const AIMonitoring = lazy(() => import('../components/admin/super-admin/AIMonitoring'));
const SecurityCenter = lazy(() => import('../components/admin/super-admin/SecurityCenter'));
const APIManagement = lazy(() => import('../components/admin/super-admin/APIManagement'));
const SystemLogsPage = lazy(() => import('../pages/admin/SystemLogsPage'));

/**
 * ===== PUBLIC ROUTES =====
 * Routes that don't require authentication
 */
export const publicRoutes: RouteConfig[] = [
  // Core public pages
  createPublicRoute('/', HomePage),
  createPublicRoute('/features', FeaturesPage),

  // Auth pages (no layout wrapper)
  createAuthRoute('/login', LoginPage),
  createAuthRoute('/register', RegisterPage),
  createAuthRoute('/logout', LogoutPage),

  // Public features
  createPublicRoute('/incubator', BusinessIncubator, 'Loading business incubator...'),
  createPublicRoute('/react-query-demo', ReactQueryDemo, 'Loading React Query demo...'),

  // Public search and templates
  createPublicRoute('/search', AdvancedSearchPage, 'Loading search...'),
  createPublicRoute('/templates', TemplateBrowserPage, 'Loading template browser...'),
  createPublicRoute('/templates/test', TemplateTestPage, 'Loading template test...'),
  createPublicRoute('/templates/status', TemplateSystemStatusPage, 'Loading system status...'),

  // Public testing and diagnostics
  createPublicRoute('/test/new-features', NewFeaturesTestPage, 'Loading new features test...'),
  createPublicRoute('/profile', UserProfilePage, 'Loading user profile...'),
  createPublicRoute('/diagnostics', AppDiagnosticsPage, 'Loading diagnostics...'),
  createPublicRoute('/system-validation', SystemValidationPage, 'Loading system validation...'),
  createPublicRoute('/app-health', AppHealthDashboard, 'Loading application health dashboard...')
];

/**
 * ===== USER ROUTES =====
 * Routes for authenticated users (all roles can access)
 */
export const userRoutes: RouteConfig[] = [
  // Main dashboard - accessible to all authenticated users
  createUserRoute('/dashboard', UserDashboardPage, 'Loading dashboard...'),

  // Core user functionality
  createUserRoute('/dashboard/business-ideas', BusinessIdeasPage, 'Loading business ideas...'),
  createUserRoute('/dashboard/business-ideas/new', BusinessIdeasPage, 'Loading business idea creator...'),
  createUserRoute('/dashboard/business-plans', BusinessPlansPage, 'Loading business plans...'),
  createUserRoute('/dashboard/templates', TemplatesPage, 'Loading templates...'),
  createUserRoute('/dashboard/posts', PostsPage, 'Loading posts...'),
  createUserRoute('/dashboard/events', EventsPage, 'Loading events...'),
  createUserRoute('/dashboard/forum', ForumPage, 'Loading forum...'),
  createUserRoute('/dashboard/resources', ResourcesPage, 'Loading resources...'),
  createUserRoute('/dashboard/analytics', AnalyticsPage, 'Loading analytics...'),

  // Incubator functionality - redirect to public incubator
  createUserRoute('/dashboard/incubator', BusinessIncubator, 'Loading incubator...'),

  // User settings and profile
  createUserRoute('/settings', UserSettings, 'Loading settings...'),

  // AI functionality - accessible to all users
  createAIRoute('/chat', ConsolidatedAIPage, 'Loading AI Assistant...'),
  createAIRoute('/chat/enhanced', ConsolidatedAIPage, 'Loading Enhanced AI...'),
  createAIRoute('/chat/business-advisor', ConsolidatedAIPage, 'Loading Business Advisor...')
];

/**
 * ===== MODERATOR ROUTES =====
 * Routes specific to moderators
 */
export const moderatorRoutes: RouteConfig[] = [
  // Moderator dashboard
  createRoleRoute('/dashboard/moderator', ModeratorDashboardPage, ['moderator'], ['moderate'], 'Loading moderator dashboard...'),

  // Content moderation
  createRoleRoute('/dashboard/moderation/content', ContentModerationPage, ['moderator'], ['moderate'], 'Loading content moderation...'),
  createRoleRoute('/dashboard/moderation/users', UserModerationPage, ['moderator'], ['moderate'], 'Loading user moderation...'),
  createRoleRoute('/dashboard/moderation/forum', ForumModerationPage, ['moderator'], ['moderate'], 'Loading forum moderation...'),
  createRoleRoute('/dashboard/moderation/reports', ReportsManagementPage, ['moderator'], ['moderate'], 'Loading reports management...'),
  createRoleRoute('/dashboard/moderation/analytics', ModerationAnalyticsPage, ['moderator'], ['moderate'], 'Loading moderation analytics...')
];

/**
 * ===== MENTOR ROUTES =====
 * Routes specific to mentors
 */
export const mentorRoutes: RouteConfig[] = [
  // Mentor dashboard
  createRoleRoute('/dashboard/mentor', MentorDashboardPage, ['mentor'], ['read', 'write'], 'Loading mentor dashboard...'),

  // Mentorship management
  createRoleRoute('/dashboard/mentorship/sessions', MentorshipSessionsPage, ['mentor'], ['read', 'write'], 'Loading mentorship sessions...'),
  createRoleRoute('/dashboard/mentorship/mentees', MenteesManagementPage, ['mentor'], ['read', 'write'], 'Loading mentees management...'),
  createRoleRoute('/dashboard/mentorship/profile', MentorProfilePage, ['mentor'], ['read', 'write'], 'Loading mentor profile...'),
  createRoleRoute('/dashboard/mentorship/analytics', MentorAnalyticsPage, ['mentor'], ['read'], 'Loading mentor analytics...'),
  createRoleRoute('/dashboard/mentorship/availability', MentorAvailabilityPage, ['mentor'], ['read', 'write'], 'Loading availability management...')
];

/**
 * ===== INVESTOR ROUTES =====
 * Routes specific to investors
 */
export const investorRoutes: RouteConfig[] = [
  // Investor dashboard
  createRoleRoute('/dashboard/investor', InvestorDashboardPage, ['investor'], ['read', 'write'], 'Loading investor dashboard...'),
  createRoleRoute('/dashboard/investments', InvestorDashboardPage, ['investor'], ['read', 'write'], 'Loading investment dashboard...'),

  // Investment management
  createRoleRoute('/dashboard/investments/opportunities', InvestmentOpportunitiesPage, ['investor'], ['read', 'write'], 'Loading investment opportunities...'),
  createRoleRoute('/dashboard/investments/portfolio', PortfolioManagementPage, ['investor'], ['read', 'write'], 'Loading portfolio management...'),
  createRoleRoute('/dashboard/investments/analytics', InvestorAnalyticsPage, ['investor'], ['read'], 'Loading investor analytics...'),
  createRoleRoute('/dashboard/investments/due-diligence', DueDiligencePage, ['investor'], ['read', 'write'], 'Loading due diligence...'),
  createRoleRoute('/dashboard/investments/profile', InvestorProfilePage, ['investor'], ['read', 'write'], 'Loading investor profile...')
];

/**
 * ===== ADMIN ROUTES =====
 * Routes for administrators
 */
export const adminRoutes: RouteConfig[] = [
  // Admin dashboard
  createAdminRoute('/admin', AdminDashboardPage, 'Loading admin dashboard...'),

  // User management
  createAdminRoute('/admin/users', UsersManagement, 'Loading users management...'),

  // Content management
  createAdminRoute('/admin/events', EventsManagement, 'Loading events management...'),
  createAdminRoute('/admin/resources', ResourcesManagement, 'Loading resources management...'),
  createAdminRoute('/admin/posts', PostsManagementPage, 'Loading posts management...'),
  createAdminRoute('/admin/moderation', ModerationDashboard, 'Loading moderation dashboard...'),

  // Communication and system
  createAdminRoute('/admin/communication', CommunicationCenter, 'Loading communication center...'),
  createAdminRoute('/admin/api', ApiManagement, 'Loading API management...'),
  createAdminRoute('/admin/performance', PerformanceCenter, 'Loading performance center...'),

  // AI system management (admin-level)
  createAdminRoute('/admin/ai-system', AISystemPage, 'Loading AI system management...')
];

/**
 * ===== SUPER ADMIN ROUTES =====
 * Routes exclusive to super administrators
 */
export const superAdminRoutes: RouteConfig[] = [
  // Super Admin dashboard
  createSuperAdminRoute('/super_admin', SuperAdminDashboardPage, 'Loading Super Admin dashboard...'),

  // Super Admin exclusive features
  createSuperAdminRoute('/super_admin/system-management', SystemManagement, 'Loading system management...'),
  createSuperAdminRoute('/super_admin/user-impersonation', UserImpersonation, 'Loading user impersonation...'),
  createSuperAdminRoute('/super_admin/ai-system-management', AISystemManagement, 'Loading AI system management...'),
  createSuperAdminRoute('/super_admin/ai-configuration', AIConfiguration, 'Loading AI configuration...'),
  createSuperAdminRoute('/super_admin/ai-analytics', AIAnalytics, 'Loading AI analytics...'),
  createSuperAdminRoute('/super_admin/ai-monitoring', AIMonitoring, 'Loading AI monitoring...'),
  createSuperAdminRoute('/super_admin/security', SecurityCenter, 'Loading security center...'),
  createSuperAdminRoute('/super_admin/performance', PerformanceCenter, 'Loading performance center...'),
  createSuperAdminRoute('/super_admin/api', APIManagement, 'Loading API management...'),
  createSuperAdminRoute('/super_admin/communication', CommunicationCenter, 'Loading communication center...'),
  createSuperAdminRoute('/super_admin/system-logs', SystemLogsPage, 'Loading system logs...')
];

/**
 * ===== ALL ROUTES COMBINED =====
 * Export all routes in a single array for easy consumption
 */
export const allRoutes: RouteConfig[] = [
  ...publicRoutes,
  ...userRoutes,
  ...moderatorRoutes,
  ...mentorRoutes,
  ...investorRoutes,
  ...adminRoutes,
  ...superAdminRoutes
];

// Export individual route arrays for backward compatibility
export {
  publicRoutes as consolidatedPublicRoutes,
  userRoutes as consolidatedUserRoutes,
  moderatorRoutes as consolidatedModeratorRoutes,
  mentorRoutes as consolidatedMentorRoutes,
  investorRoutes as consolidatedInvestorRoutes,
  adminRoutes as consolidatedAdminRoutes,
  superAdminRoutes as consolidatedSuperAdminRoutes
};
