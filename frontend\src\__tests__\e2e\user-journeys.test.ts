/**
 * Comprehensive User Journey Tests
 * Tests complete business workflows from different user perspectives
 */

import { test, expect, Page, Browser } from '@playwright/test';

// Test configuration
const BASE_URL = process.env.REACT_APP_BASE_URL || 'http://localhost:3000';
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Test users for different roles
const TEST_USERS = {
  entrepreneur: {
    email: '<EMAIL>',
    password: 'testpassword123',
    firstName: 'John',
    lastName: 'Entrepreneur'
  },
  mentor: {
    email: '<EMAIL>',
    password: 'testpassword123',
    firstName: 'Jane',
    lastName: 'Mentor'
  },
  investor: {
    email: '<EMAIL>',
    password: 'testpassword123',
    firstName: 'Bob',
    lastName: 'Investor'
  },
  admin: {
    email: '<EMAIL>',
    password: 'testpassword123',
    firstName: 'Admin',
    lastName: 'User'
  }
};

// Helper functions
async function checkBackendHealth(): Promise<boolean> {
  try {
    const response = await fetch(`${API_BASE_URL}/api/health/`, { 
      method: 'GET',
      timeout: 5000 
    });
    return response.ok;
  } catch {
    return false;
  }
}

async function registerUser(page: Page, userType: keyof typeof TEST_USERS) {
  const user = TEST_USERS[userType];
  
  await page.goto(`${BASE_URL}/register`);
  await page.waitForSelector('[data-testid="register-form"]');
  
  await page.fill('[data-testid="first-name-input"]', user.firstName);
  await page.fill('[data-testid="last-name-input"]', user.lastName);
  await page.fill('[data-testid="email-input"]', user.email);
  await page.fill('[data-testid="password-input"]', user.password);
  await page.fill('[data-testid="confirm-password-input"]', user.password);
  
  // Select user role if available
  if (userType !== 'entrepreneur') {
    await page.selectOption('[data-testid="role-select"]', userType);
  }
  
  await page.click('[data-testid="register-button"]');
  
  // Handle different registration outcomes
  try {
    await page.waitForURL('**/dashboard/**', { timeout: 10000 });
    return true;
  } catch {
    // Check for verification required
    const verificationMessage = page.locator('[data-testid="verification-message"]');
    if (await verificationMessage.isVisible()) {
      console.log(`User ${userType} registered but needs verification`);
      return false;
    }
    throw new Error(`Registration failed for ${userType}`);
  }
}

async function loginUser(page: Page, userType: keyof typeof TEST_USERS) {
  const user = TEST_USERS[userType];
  
  await page.goto(`${BASE_URL}/login`);
  await page.waitForSelector('[data-testid="login-form"]');
  
  await page.fill('[data-testid="email-input"]', user.email);
  await page.fill('[data-testid="password-input"]', user.password);
  await page.click('[data-testid="login-button"]');
  
  // Wait for successful login
  await page.waitForURL('**/dashboard/**', { timeout: 15000 });
}

async function createBusinessIdea(page: Page, ideaData: any) {
  await page.goto(`${BASE_URL}/dashboard/business-ideas/create`);
  await page.waitForSelector('[data-testid="business-idea-form"]');
  
  await page.fill('[data-testid="title-input"]', ideaData.title);
  await page.fill('[data-testid="description-textarea"]', ideaData.description);
  await page.fill('[data-testid="problem-statement-textarea"]', ideaData.problemStatement);
  await page.fill('[data-testid="solution-description-textarea"]', ideaData.solutionDescription);
  await page.fill('[data-testid="target-audience-textarea"]', ideaData.targetAudience);
  
  if (ideaData.tags) {
    for (const tag of ideaData.tags) {
      await page.fill('[data-testid="tags-input"]', tag);
      await page.press('[data-testid="tags-input"]', 'Enter');
    }
  }
  
  await page.click('[data-testid="submit-button"]');
  await page.waitForURL('**/dashboard/business-ideas/**');
  
  return page.url().split('/').pop(); // Return the idea ID
}

test.describe('Complete User Journeys', () => {
  let backendAvailable: boolean;
  
  test.beforeAll(async () => {
    backendAvailable = await checkBackendHealth();
    console.log(`Backend available: ${backendAvailable}`);
  });

  test.describe('Entrepreneur Journey', () => {
    test('should complete full entrepreneur workflow', async ({ page, browser }) => {
      test.setTimeout(120000); // 2 minutes for complete workflow
      
      // Step 1: Registration
      if (backendAvailable) {
        await registerUser(page, 'entrepreneur');
      } else {
        // Mock successful registration
        await page.goto(`${BASE_URL}/dashboard`);
        await page.evaluate(() => {
          localStorage.setItem('auth_token', 'mock_token');
          localStorage.setItem('user_role', 'entrepreneur');
        });
      }
      
      // Step 2: Create Business Idea
      const businessIdeaData = {
        title: 'E2E Test Startup Idea',
        description: 'A revolutionary platform for testing end-to-end workflows',
        problemStatement: 'Current testing tools lack comprehensive user journey validation',
        solutionDescription: 'Our platform provides complete workflow testing capabilities',
        targetAudience: 'Software development teams and QA professionals',
        tags: ['testing', 'automation', 'software']
      };
      
      let ideaId: string;
      if (backendAvailable) {
        ideaId = await createBusinessIdea(page, businessIdeaData);
      } else {
        // Mock business idea creation
        await page.goto(`${BASE_URL}/dashboard/business-ideas`);
        ideaId = 'mock-idea-123';
      }
      
      // Verify business idea appears in dashboard
      await page.goto(`${BASE_URL}/dashboard/business-ideas`);
      await expect(page.locator('text=' + businessIdeaData.title)).toBeVisible();
      
      // Step 3: Create Business Plan from Template
      await page.goto(`${BASE_URL}/templates`);
      await page.waitForSelector('[data-testid="template-grid"]');
      
      // Select a template
      const firstTemplate = page.locator('[data-testid="template-card"]').first();
      await firstTemplate.click();
      
      await page.waitForSelector('[data-testid="template-details"]');
      await page.click('[data-testid="use-template-button"]');
      
      // Fill business plan details
      await page.waitForSelector('[data-testid="business-plan-form"]');
      await page.fill('[data-testid="plan-title-input"]', 'E2E Test Business Plan');
      
      if (backendAvailable) {
        await page.selectOption('[data-testid="business-idea-select"]', ideaId);
      }
      
      await page.click('[data-testid="create-plan-button"]');
      
      // Verify plan creation
      await page.waitForURL('**/dashboard/business-plans/**');
      await expect(page.locator('text=E2E Test Business Plan')).toBeVisible();
      
      // Step 4: Seek Mentorship
      await page.goto(`${BASE_URL}/dashboard/mentorship`);
      await page.waitForSelector('[data-testid="mentor-search"]');
      
      // Search for mentors
      await page.fill('[data-testid="mentor-search-input"]', 'technology');
      await page.click('[data-testid="search-button"]');
      
      // Request mentorship from first available mentor
      const firstMentor = page.locator('[data-testid="mentor-card"]').first();
      if (await firstMentor.isVisible()) {
        await firstMentor.locator('[data-testid="request-mentorship-button"]').click();
        
        await page.waitForSelector('[data-testid="mentorship-request-modal"]');
        await page.fill('[data-testid="request-message-textarea"]', 'I would like guidance on my startup idea.');
        await page.click('[data-testid="send-request-button"]');
        
        await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
      }
      
      // Step 5: Apply for Funding
      await page.goto(`${BASE_URL}/dashboard/funding`);
      await page.waitForSelector('[data-testid="funding-opportunities"]');
      
      // Browse funding opportunities
      const firstOpportunity = page.locator('[data-testid="funding-opportunity-card"]').first();
      if (await firstOpportunity.isVisible()) {
        await firstOpportunity.click();
        
        await page.waitForSelector('[data-testid="funding-details"]');
        await page.click('[data-testid="apply-button"]');
        
        // Fill funding application
        await page.waitForSelector('[data-testid="funding-application-form"]');
        await page.fill('[data-testid="funding-amount-input"]', '50000');
        await page.fill('[data-testid="use-of-funds-textarea"]', 'Product development and market validation');
        await page.click('[data-testid="submit-application-button"]');
        
        await expect(page.locator('[data-testid="application-success-message"]')).toBeVisible();
      }
      
      // Step 6: Track Progress
      await page.goto(`${BASE_URL}/dashboard`);
      await page.waitForSelector('[data-testid="dashboard-overview"]');
      
      // Verify all activities are reflected in dashboard
      await expect(page.locator('[data-testid="business-ideas-count"]')).toContainText('1');
      await expect(page.locator('[data-testid="business-plans-count"]')).toContainText('1');
    });
  });

  test.describe('Mentor Journey', () => {
    test('should complete full mentor workflow', async ({ page }) => {
      test.setTimeout(90000); // 1.5 minutes for mentor workflow

      // Step 1: Registration and Profile Setup
      if (backendAvailable) {
        await registerUser(page, 'mentor');
      } else {
        await page.goto(`${BASE_URL}/dashboard`);
        await page.evaluate(() => {
          localStorage.setItem('auth_token', 'mock_mentor_token');
          localStorage.setItem('user_role', 'mentor');
        });
      }

      // Step 2: Complete Mentor Profile
      await page.goto(`${BASE_URL}/dashboard/profile`);
      await page.waitForSelector('[data-testid="mentor-profile-form"]');

      await page.fill('[data-testid="company-input"]', 'Tech Innovations Inc');
      await page.fill('[data-testid="position-input"]', 'Senior Technology Advisor');
      await page.fill('[data-testid="years-experience-input"]', '15');
      await page.fill('[data-testid="bio-textarea"]', 'Experienced technology leader with expertise in startup scaling and product development.');

      // Select expertise areas
      await page.check('[data-testid="expertise-technology"]');
      await page.check('[data-testid="expertise-product-development"]');
      await page.check('[data-testid="expertise-scaling"]');

      // Set availability
      await page.selectOption('[data-testid="availability-select"]', 'weekdays');
      await page.fill('[data-testid="max-mentees-input"]', '5');
      await page.check('[data-testid="accepting-mentees-checkbox"]');

      await page.click('[data-testid="save-profile-button"]');
      await expect(page.locator('[data-testid="profile-success-message"]')).toBeVisible();

      // Step 3: Browse Mentorship Requests
      await page.goto(`${BASE_URL}/dashboard/mentorship/requests`);
      await page.waitForSelector('[data-testid="mentorship-requests"]');

      // Accept a mentorship request
      const firstRequest = page.locator('[data-testid="mentorship-request-card"]').first();
      if (await firstRequest.isVisible()) {
        await firstRequest.locator('[data-testid="view-request-button"]').click();

        await page.waitForSelector('[data-testid="request-details-modal"]');
        await page.click('[data-testid="accept-request-button"]');

        // Set up mentorship schedule
        await page.waitForSelector('[data-testid="schedule-setup-form"]');
        await page.selectOption('[data-testid="meeting-frequency-select"]', 'weekly');
        await page.fill('[data-testid="session-duration-input"]', '60');
        await page.click('[data-testid="confirm-mentorship-button"]');

        await expect(page.locator('[data-testid="mentorship-confirmed-message"]')).toBeVisible();
      }

      // Step 4: Conduct Mentorship Session
      await page.goto(`${BASE_URL}/dashboard/mentorship/sessions`);
      await page.waitForSelector('[data-testid="upcoming-sessions"]');

      const upcomingSession = page.locator('[data-testid="session-card"]').first();
      if (await upcomingSession.isVisible()) {
        await upcomingSession.locator('[data-testid="start-session-button"]').click();

        // Simulate session activities
        await page.waitForSelector('[data-testid="session-interface"]');
        await page.fill('[data-testid="session-notes-textarea"]', 'Discussed product-market fit and go-to-market strategy.');

        // Add action items
        await page.click('[data-testid="add-action-item-button"]');
        await page.fill('[data-testid="action-item-input"]', 'Research competitor pricing models');
        await page.click('[data-testid="save-action-item-button"]');

        // End session
        await page.click('[data-testid="end-session-button"]');
        await page.fill('[data-testid="session-feedback-textarea"]', 'Great progress on business model validation.');
        await page.click('[data-testid="submit-feedback-button"]');

        await expect(page.locator('[data-testid="session-completed-message"]')).toBeVisible();
      }

      // Step 5: Review Analytics
      await page.goto(`${BASE_URL}/dashboard/analytics`);
      await page.waitForSelector('[data-testid="mentor-analytics"]');

      // Verify analytics are displayed
      await expect(page.locator('[data-testid="total-mentees-count"]')).toBeVisible();
      await expect(page.locator('[data-testid="sessions-conducted-count"]')).toBeVisible();
      await expect(page.locator('[data-testid="average-rating"]')).toBeVisible();
    });
  });

  test.describe('Investor Journey', () => {
    test('should complete full investor workflow', async ({ page }) => {
      test.setTimeout(90000); // 1.5 minutes for investor workflow

      // Step 1: Registration and Profile Setup
      if (backendAvailable) {
        await registerUser(page, 'investor');
      } else {
        await page.goto(`${BASE_URL}/dashboard`);
        await page.evaluate(() => {
          localStorage.setItem('auth_token', 'mock_investor_token');
          localStorage.setItem('user_role', 'investor');
        });
      }

      // Step 2: Complete Investor Profile
      await page.goto(`${BASE_URL}/dashboard/profile`);
      await page.waitForSelector('[data-testid="investor-profile-form"]');

      await page.fill('[data-testid="organization-input"]', 'Venture Capital Partners');
      await page.fill('[data-testid="investment-focus-textarea"]', 'Early-stage technology startups with strong product-market fit');
      await page.fill('[data-testid="min-investment-input"]', '25000');
      await page.fill('[data-testid="max-investment-input"]', '500000');

      // Select investment stages
      await page.check('[data-testid="stage-seed"]');
      await page.check('[data-testid="stage-series-a"]');

      // Select industries
      await page.check('[data-testid="industry-technology"]');
      await page.check('[data-testid="industry-fintech"]');
      await page.check('[data-testid="industry-healthcare"]');

      await page.click('[data-testid="save-profile-button"]');
      await expect(page.locator('[data-testid="profile-success-message"]')).toBeVisible();

      // Step 3: Browse Investment Opportunities
      await page.goto(`${BASE_URL}/dashboard/opportunities`);
      await page.waitForSelector('[data-testid="investment-opportunities"]');

      // Filter opportunities
      await page.selectOption('[data-testid="stage-filter"]', 'seed');
      await page.selectOption('[data-testid="industry-filter"]', 'technology');
      await page.click('[data-testid="apply-filters-button"]');

      // Review an opportunity
      const firstOpportunity = page.locator('[data-testid="opportunity-card"]').first();
      if (await firstOpportunity.isVisible()) {
        await firstOpportunity.click();

        await page.waitForSelector('[data-testid="opportunity-details"]');

        // Review business plan
        await page.click('[data-testid="view-business-plan-button"]');
        await page.waitForSelector('[data-testid="business-plan-viewer"]');

        // Navigate through plan sections
        await page.click('[data-testid="executive-summary-tab"]');
        await page.click('[data-testid="market-analysis-tab"]');
        await page.click('[data-testid="financial-projections-tab"]');

        // Express interest
        await page.click('[data-testid="express-interest-button"]');
        await page.waitForSelector('[data-testid="interest-form"]');
        await page.fill('[data-testid="interest-message-textarea"]', 'Interested in learning more about your go-to-market strategy.');
        await page.click('[data-testid="send-interest-button"]');

        await expect(page.locator('[data-testid="interest-sent-message"]')).toBeVisible();
      }

      // Step 4: Schedule Due Diligence
      await page.goto(`${BASE_URL}/dashboard/due-diligence`);
      await page.waitForSelector('[data-testid="due-diligence-pipeline"]');

      const activeOpportunity = page.locator('[data-testid="pipeline-opportunity"]').first();
      if (await activeOpportunity.isVisible()) {
        await activeOpportunity.locator('[data-testid="schedule-meeting-button"]').click();

        await page.waitForSelector('[data-testid="meeting-scheduler"]');
        await page.selectOption('[data-testid="meeting-type-select"]', 'initial-discussion');
        await page.fill('[data-testid="meeting-date-input"]', '2024-12-31');
        await page.fill('[data-testid="meeting-time-input"]', '14:00');
        await page.click('[data-testid="schedule-button"]');

        await expect(page.locator('[data-testid="meeting-scheduled-message"]')).toBeVisible();
      }

      // Step 5: Review Portfolio
      await page.goto(`${BASE_URL}/dashboard/portfolio`);
      await page.waitForSelector('[data-testid="portfolio-overview"]');

      // Verify portfolio analytics
      await expect(page.locator('[data-testid="total-investments-count"]')).toBeVisible();
      await expect(page.locator('[data-testid="portfolio-value"]')).toBeVisible();
      await expect(page.locator('[data-testid="active-opportunities-count"]')).toBeVisible();
    });
  });
});
