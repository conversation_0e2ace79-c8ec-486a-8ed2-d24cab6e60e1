import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAppSelector } from '../../store/hooks';
import { RouteConfig } from '../../routes/routeConfig';
import { canAccessRoute, UserRole, PermissionLevel } from '../../utils/unifiedRoleManager';
// Simple role routing using centralized role utilities

interface RoleRouteProps {
  config: RouteConfig;
  children: React.ReactNode;
}

/**
 * RoleRoute component that handles role-based access control for routes
 */
const RoleRoute: React.FC<RoleRouteProps> = ({ config, children }) => {
  const location = useLocation();
  const { user, isAuthenticated, isLoading } = useAppSelector((state) => state.auth);

  // Show loading state while authentication is being checked
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Check if user has access to this route using the unified role manager
  const hasAccess = canAccessRoute(
    user,
    config.roles as UserRole[],
    config.permissions as PermissionLevel[],
    config.requireAuth !== false
  );

  // If user doesn't have access, redirect to appropriate page
  if (!hasAccess) {
    const redirectTo = config.redirectTo || '/login';
    
    // If not authenticated, redirect to login with return URL
    if (!isAuthenticated) {
      return <Navigate to={`/login?returnUrl=${encodeURIComponent(location.pathname)}`} replace />;
    }
    
    // If authenticated but insufficient permissions, redirect to configured path
    return <Navigate to={redirectTo} replace />;
  }

  // User has access, render the children
  return <>{children}</>;
};

export default RoleRoute;
