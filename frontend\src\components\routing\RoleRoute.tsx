import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAppSelector } from '../../store/hooks';
import { RouteConfig } from '../../routes/routeConfig';
import { canAccessRoute, getUserRole, getDashboardRoute, UserRole, PermissionLevel } from '../../utils/unifiedRoleManager';
// ✅ UNIFIED: Role routing using AUTHORITATIVE RBAC system

interface RoleRouteProps {
  config: RouteConfig;
  children: React.ReactNode;
}

/**
 * ✅ AUTHORITATIVE ROLE ROUTE COMPONENT
 * Handles role-based access control using unified RBAC system
 */
const RoleRoute: React.FC<RoleRouteProps> = ({ config, children }) => {
  const location = useLocation();
  const { user, isAuthenticated, isLoading } = useAppSelector((state) => state.auth);

  // Show loading state while authentication is being checked
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // ✅ UNIFIED: Check access using AUTHORITATIVE role manager
  const hasAccess = canAccessRoute(
    user,
    config.roles as UserRole[],
    config.permissions as PermissionLevel[],
    config.requireAuth !== false
  );

  // If user doesn't have access, redirect to appropriate page
  if (!hasAccess) {
    // If not authenticated, redirect to login with return URL
    if (!isAuthenticated) {
      return <Navigate to={`/login?returnUrl=${encodeURIComponent(location.pathname)}`} replace />;
    }

    // ✅ UNIFIED: Use role-based dashboard routing for authenticated users
    const fallbackPath = config.redirectTo || getDashboardRoute(user);
    return <Navigate to={fallbackPath} replace />;
  }

  // User has access, render the children
  return <>{children}</>;
};

export default RoleRoute;
