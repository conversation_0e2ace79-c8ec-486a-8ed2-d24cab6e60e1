/**
 * AI Status Indicator
 * Shows AI activity status in the navbar
 */

import React, { useState } from 'react';
import { Bot, Activity, Zap, ChevronDown } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useCentralizedAI } from '../../hooks/useCentralizedAI';
// Removed useAutomaticAI import - functionality moved to useCentralizedAI
import { automaticAiUtils } from '../../services/automaticAiApi';

interface AIStatusIndicatorProps {
  className?: string;
  variant?: 'navbar' | 'sidebar' | 'compact';
}

export const AIStatusIndicator: React.FC<AIStatusIndicatorProps> = ({
  className = '',
  variant = 'navbar'
}) => {
  const { t } = useTranslation();
  const [showDropdown, setShowDropdown] = useState(false);
  
  const {
    stats,
    recentActions,
    isAIRunning,
    performanceStatus,
    isLoading
  } = useCentralizedAI();

  if (isLoading) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse" />
        <span className="text-sm text-gray-500">{t('ai.monitors.loading', 'AI Loading...')}</span>
      </div>
    );
  }

  const getStatusColor = () => {
    if (!isAIRunning) return 'bg-red-500';
    if (performanceStatus.color === 'green') return 'bg-green-500';
    if (performanceStatus.color === 'blue') return 'bg-blue-500';
    if (performanceStatus.color === 'orange') return 'bg-orange-500';
    return 'bg-red-500';
  };

  const getStatusText = () => {
    if (!isAIRunning) return 'AI Offline';
    return `AI Active (${stats?.total_actions_today || 0} today)`;
  };

  if (variant === 'compact') {
    return (
      <div className={`flex items-center space-x-1 ${className}`}>
        <div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
        <span className="text-xs font-medium">{stats?.total_actions_today || 0}</span>
      </div>
    );
  }

  if (variant === 'sidebar') {
    return (
      <div className={`p-3 bg-gray-50 dark:bg-gray-800 rounded-lg ${className}`}>
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
            <Bot className="h-4 w-4 text-purple-500" />
            <span className="text-sm font-medium">AI Status</span>
          </div>
        </div>
        <div className="text-xs text-gray-600 dark:text-gray-300">
          {getStatusText()}
        </div>
        {stats && (
          <div className="mt-2 grid grid-cols-2 gap-2 text-xs">
            <div>
              <span className="text-gray-500">Enhanced:</span>
              <span className="ml-1 font-medium">{stats.ideas_enhanced}</span>
            </div>
            <div>
              <span className="text-gray-500">Opportunities:</span>
              <span className="ml-1 font-medium">{stats.opportunities_found}</span>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Navbar variant (default)
  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setShowDropdown(!showDropdown)}
        className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
      >
        <div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
        <Bot className="h-4 w-4 text-purple-500" />
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
          AI ({stats?.total_actions_today || 0})
        </span>
        <ChevronDown className="h-3 w-3 text-gray-500" />
      </button>

      {/* Dropdown */}
      {showDropdown && (
        <div className="absolute top-full right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
          <div className="p-4">
            {/* Header */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <Bot className="h-5 w-5 text-purple-500" />
                <span className="font-semibold text-gray-900 dark:text-white">AI Activity</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
                <span className="text-sm text-gray-600 dark:text-gray-300">
                  {performanceStatus.status}
                </span>
              </div>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-3 mb-4">
              <div className="text-center">
                <div className="text-lg font-bold text-purple-600 dark:text-purple-400">
                  {stats?.total_actions_today || 0}
                </div>
                <div className="text-xs text-gray-500">Actions Today</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-green-600 dark:text-green-400">
                  {stats?.ideas_enhanced || 0}
                </div>
                <div className="text-xs text-gray-500">Enhanced</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                  {stats?.opportunities_found || 0}
                </div>
                <div className="text-xs text-gray-500">Opportunities</div>
              </div>
            </div>

            {/* Recent Actions */}
            <div className="mb-4">
              <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">
                Recent AI Actions
              </h4>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {recentActions.slice(0, 3).map((action, index) => (
                  <div key={action.id || index} className="flex items-start space-x-2 p-2 bg-gray-50 dark:bg-gray-700 rounded">
                    <Activity className="h-3 w-3 text-gray-500 mt-1 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <div className="text-xs font-medium text-gray-900 dark:text-white truncate">
                        {action.title}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {automaticAiUtils.timeAgo(action.timestamp)} • {action.business_idea}
                      </div>
                    </div>
                  </div>
                ))}
                {recentActions.length === 0 && (
                  <div className="text-xs text-gray-500 dark:text-gray-400 text-center py-2">
                    No recent AI activity
                  </div>
                )}
              </div>
            </div>

            {/* Actions */}
            <div className="flex space-x-2">
              <a
                href="/ai/automatic"
                className="flex-1 bg-purple-600 hover:bg-purple-700 text-white text-xs font-medium py-2 px-3 rounded text-center transition-colors"
                onClick={() => setShowDropdown(false)}
              >
                View Dashboard
              </a>
              <a
                href="/ai"
                className="flex-1 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 text-xs font-medium py-2 px-3 rounded text-center transition-colors"
                onClick={() => setShowDropdown(false)}
              >
                AI Assistant
              </a>
            </div>
          </div>
        </div>
      )}

      {/* Click outside to close */}
      {showDropdown && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowDropdown(false)}
        />
      )}
    </div>
  );
};

export default AIStatusIndicator;
