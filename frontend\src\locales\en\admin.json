{"admin": {"yasmeenAI": "<PERSON><PERSON><PERSON> AI Assistant", "analytics": {"activeUsersByTimePeriod": "Active Users by Time Period", "comments": "Comments", "contentEngagementOverTime": "Content Engagement Over Time", "dailyActive": "Daily Active", "engagementRate": "Engagement Rate", "enhancedDashboard": "Enhanced Dashboard", "events": "Events", "likesReceived": "<PERSON><PERSON> Re<PERSON>", "monthlyActive": "Monthly Active", "perUser": "Per User", "posts": "Posts", "postsCommentsByMonth": "Posts & Comments by Month", "postsPerUser": "Posts Per User", "resources": "Resources", "retentionRate": "Retention Rate", "topActiveUsers": "Top Active Users", "topUserActivity": "Top User Activity", "totalUsers": "Total Users", "userEngagement": "User Engagement", "userEngagementLevels": "User Engagement Levels", "weeklyActive": "Weekly Active"}, "settings": {"title": "<PERSON><PERSON>s", "description": "Configure system settings and preferences"}, "keyMetrics": "Key Metrics", "quickActions": "Quick Actions", "systemStatus": "System Status & Notifications", "welcomeBack": "Welcome back", "communityToday": "Here's what's happening in your community today", "systemOnline": "System Online", "activeUsers": "Active Users", "todayActivity": "Today's Activity", "growthRate": "Growth Rate", "securityNotice": "Security Notice", "adminNote": "Note: You are accessing the admin panel. Handle data with care.", "totalUsers": "Total Users", "fromLastMonth": "from last month", "membership": "Membership", "reviewApplications": "Review Applications", "events": "Events", "manageEvents": "Manage Events", "posts": "Posts", "moderatePosts": "Moderate Posts", "manageUsers": "Manage Users", "users": {"title": "User Management", "description": "Manage user accounts and permissions"}, "viewAnalytics": "View Analytics", "moderation": "Moderation", "moderateContent": "Moderate Content", "clickToManage": "<PERSON>lick to manage", "systemHealth": "System Health", "allSystemsOperational": "All Systems Operational", "lastUpdated": "Last updated", "realTimeMonitoring": "Real-time monitoring", "notifications": {"title": "Notifications", "allRead": "All notifications read", "noNotifications": "No notifications", "allCaughtUp": "You're all caught up!"}, "unreadOnly": "Unread Only", "analyticsOverview": "Analytics Overview", "platformInsights": "Platform insights and trends", "advancedCharts": "Advanced Charts", "userGrowth": "User Growth", "monthlyGrowth": "Monthly growth trend", "activityDistribution": "Activity Distribution", "contentBreakdown": "Content breakdown", "totalItems": "Total Items", "thisWeek": "This Week", "revenueChart": "Revenue vs Target", "monthlyRevenue": "Monthly revenue performance", "userEngagement": "User Engagement", "engagementMetrics": "Key engagement metrics", "geographicDistribution": "Geographic Distribution", "usersByCountry": "Users by country", "systemPerformance": "System Performance", "performanceMetrics": "Real-time performance metrics", "totalEvents": "Total Events", "totalPosts": "Total Posts", "activeToday": "Active Today", "serverStatus": "Server Status", "serverUptime": "Server uptime", "databaseHealth": "Database Health", "databasePerformance": "Database performance", "networkLatency": "Network Latency", "averageResponseTime": "Average response time", "storageUsage": "Storage Usage", "diskSpaceUsed": "Disk space used", "cpuUsage": "CPU Usage", "processorLoad": "Processor load", "memoryUsage": "Memory Usage", "ramUtilization": "RAM utilization", "someIssuesDetected": "Some Issues Detected", "excellent": "Excellent", "chat": {"title": "AI Chat Management", "description": "Advanced AI conversation and analysis tools for administrators", "featuresOverview": "Overview of all AI capabilities and features", "aiDescription": "Advanced AI conversation and analysis tools for administrators", "businessIdeaRequired": "Business Idea Required", "selectBusinessIdea": "Please select a business idea to run analysis", "browseBusinessIdeas": "Browse Business Ideas", "aiSettings": "AI Settings", "configureAI": "Configure AI preferences and capabilities", "languageSettings": "Language Settings", "preferredLanguage": "Preferred Language", "autoDetect": "Auto Detect", "languageNote": "Language settings affect AI responses and analysis", "serviceManagement": "Service Management", "refreshStatus": "Refresh Status", "refreshNote": "Check the current status of AI services", "availableCapabilities": "Available Capabilities", "aiOnline": "AI Online", "aiOffline": "AI Offline", "lastUpdated": "Last Updated", "aiFeatures": "AI Features", "connectionError": "Connection Error", "systemStatus": "System Status", "aiService": "AI Service", "responseTime": "Response Time", "welcomeMessage": "Welcome to the AI Administration Panel", "errorMessage": "Error connecting to AI services", "yasmeenTitle": "Yasmeen AI Administration", "connected": "Connected", "enhanced": "Enhanced", "messagePlaceholder": "Type your message here...", "mlInsights": "ML Insights"}, "incubator": {"title": "Incubator Management", "description": "Manage business ideas and incubation programs", "searchIdeas": "Search business ideas...", "stage": "Stage", "moderate": "Moderate", "moderateIdea": "Moderate Idea", "ideaDetails": "Idea Details", "noIdeasFound": "No Ideas Found", "adjustFilters": "Try adjusting your search filters", "noIdeasYet": "No business ideas have been submitted yet", "stages": {"concept": "Concept", "validation": "Validation", "development": "Development", "scaling": "Sc<PERSON>", "established": "Established"}, "form": {"title": "Business Idea Form", "currentStage": "Current Stage"}}, "generalSettings": "General Settings", "basic": {"site": {"configuration": "Basic site configuration and preferences"}}, "securitySettings": "Security Settings", "emailSettings": "<PERSON><PERSON>s", "smtp": {"configuration": {"and": "SMTP configuration and email templates"}}, "notificationSettings": "Notification Settings", "system": {"notifications": {"and": "System notifications and alerts configuration"}}, "apiSettings": "API Settings", "databaseSettings": "Database Settings", "database": {"backup": {"and": "Database backup and maintenance settings"}}, "settingsDescription": "Manage system settings and configuration", "siteName": "Site Name", "siteDescription": "Site Description", "adminEmail": "<PERSON><PERSON>", "timezone": "Timezone", "utc": {"language": {"en": "UTC (English)"}}, "eastern": {"time": "Eastern Time"}, "pacific": {"time": "Pacific Time"}, "london": "London", "tokyo": "Tokyo", "business": {"incubator": {"platform": "Business Incubator Platform"}}, "sessionTimeout": "Session Timeout", "maxLoginAttempts": "<PERSON> Login Attempts", "requireTwoFactor": "Require Two-Factor Authentication", "smtpHost": "SMTP Host", "smtpPort": "SMTP Port", "emailFromAddress": "Email From Address", "emailNotifications": "Email Notifications", "maintenanceMode": "Maintenance Mode", "resourcesManagement": "Resources Management", "manageLearningResources": "Manage learning resources and educational content", "addNewResource": "Add New Resource", "review": {"and": {"moderate": "Review and moderate user-submitted content"}}, "select": {"all": "Select All"}, "view": {"details": "View Details"}, "moderate": "Moderate", "participation": {"rate": "Participation Rate"}, "nonparticipants": "Non-participants", "subscribed": {"users": "Subscribed Users"}, "nonsubscribed": "Non-subscribed", "forum": {"title": "Forum Management", "description": "Manage forum categories, threads, and user interactions", "searchThreads": "Search threads...", "allThreads": "All Threads", "pendingReview": "Pending Review", "refresh": "Refresh", "noThreadsPending": "No threads pending review", "loadingData": "Loading forum data...", "yourReputation": "Your Reputation", "signInForReputation": "Sign in to view your reputation", "topContributors": "Top Contributors", "guidelines": {"title": "Community Guidelines", "respectful": "Be respectful and constructive in all interactions", "stayOnTopic": "Stay on topic and keep discussions relevant", "shareKnowledge": "Share knowledge and help fellow entrepreneurs", "upvote": "Upvote helpful content and good contributions", "report": "Report inappropriate content or behavior"}}, "avg": {"posts": {"per": "Avg Posts Per"}}, "user": {"participation": {"rate": "User Participation Rate"}}, "subscription": {"rate": "Subscription Rate"}, "overview": "Overview", "activity": "Activity", "contributors": "Contributors", "popular": {"threads": "Popular Threads"}, "engagement": "Engagement", "na": "N/A", "location": "Location", "expertise": "Expertise", "userActions": {"updated": {"successfully": "Updated successfully"}, "created": {"successfully": "Created successfully"}, "deleted": {"successfully": "Deleted successfully"}}, "add": {"new": {"user": "Add New User"}}, "delete": {"user": "Delete User"}, "resources": "Resources", "bulkActions": {"itemsSelected": "items selected", "approveSelected": "Approve Selected", "rejectSelected": "Reject Selected", "deleteSelected": "Delete Selected", "makeAdmin": "Make Admin", "removeAdmin": "Remove <PERSON>", "confirmApprovePosts": "Are you sure you want to approve the selected posts?", "confirmRejectPosts": "Are you sure you want to reject the selected posts?", "confirmDeletePosts": "Are you sure you want to delete the selected posts?", "confirmApproveEvents": "Are you sure you want to approve the selected events?", "confirmRejectEvents": "Are you sure you want to reject the selected events?", "confirmDeleteEvents": "Are you sure you want to delete the selected events?", "confirmMakeAdmin": "Are you sure you want to make the selected users administrators?", "confirmRemoveAdmin": "Are you sure you want to remove admin privileges from the selected users?", "confirmDeleteUsers": "Are you sure you want to delete the selected users?"}, "activeEvents": "Active Events", "communityPosts": "Community Posts", "upcomingEvents": "Upcoming Events", "attendees": "Attendees", "noUpcomingEvents": "No upcoming events", "noRecentActivity": "No recent activity", "likes": "<PERSON>s", "newUsers": "New Users", "newUsersBy": "New Users By", "eventsByMonth": "Events By Month", "resourcesByType": "Resources By Type", "resourceDistribution": "Resource Distribution", "popularPosts": "Popular Posts", "mostLikedPosts": "Most Liked Posts", "aiSystem": "AI System", "ai": {"systemManagement": "AI System Management", "configuration": "AI Configuration", "analytics": "AI Analytics", "monitoring": "AI Monitoring", "monitorAndManage": "Monitor and manage AI services and capabilities", "consolidatedService": "Consolidated AI Service", "mainUnifiedService": "Main unified AI service for the platform", "geminiAI": "Gemini AI", "geminiIntegration": "Google Gemini AI integration", "langchain": "<PERSON><PERSON><PERSON><PERSON>", "workflowOrchestration": "Workflow orchestration and AI pipeline management", "workflows": "AI Workflows", "businessAnalysisWorkflows": "Business analysis and recommendation workflows", "mlService": "ML Service", "machineLearningInsights": "Machine learning insights and predictions", "arabicProcessing": "Arabic Processing", "arabicLanguageSupport": "Arabic language processing and cultural context", "overallStatus": "Overall Status", "servicesOnline": "Services Online", "requestCount": "Request Count", "uptime": "Uptime", "serviceComponents": "Service Components", "critical": "Critical", "serviceError": "Service Error", "lastUpdated": "Last Updated", "autoRefresh": "Auto Refresh", "chat": {"title": "AI Chat", "description": "Advanced AI conversation interface for administrators"}, "analysis": {"title": "AI Analysis", "description": "Comprehensive AI analysis and insights dashboard", "businessIdeaRequired": "Business Idea Required", "selectBusinessIdea": "Please select a business idea to run analysis"}, "settings": {"title": "AI Settings", "description": "Configure AI services and preferences", "aiSettings": "AI Settings", "preferredLanguage": "Preferred Language", "autoDetect": "Auto Detect", "serviceManagement": "Service Management", "refreshStatus": "Refresh Status", "availableCapabilities": "Available Capabilities"}, "capabilities": {"advancedAI": "Advanced AI", "advancedAIDesc": "State-of-the-art AI models with enhanced reasoning capabilities", "workflows": "AI Workflows", "workflowsDesc": "Automated multi-step business analysis and recommendation workflows", "mlInsights": "ML Insights", "mlInsightsDesc": "Machine learning powered predictive analytics and insights", "culturalContext": "Cultural Context", "culturalContextDesc": "Arabic language processing with cultural awareness and regional insights"}, "status": {"title": "AI Status", "description": "Monitor AI service health and performance", "connected": "Connected", "disconnected": "Disconnected", "healthy": "Healthy", "degraded": "Degraded", "down": "Down"}, "consolidatedAssistant": "Consolidated AI Assistant", "yourAIAccess": "Your AI Access", "chatLimit": "<PERSON><PERSON>", "analysisLimit": "Analysis Limit", "generationLimit": "Generation Limit", "availableCapabilities": "Available Capabilities", "upgradeAccess": "Upgrade Access", "upgradeDescription": "Contact administrator to upgrade your AI access level", "applyForRole": "Apply for Role", "loginRequired": "<PERSON><PERSON> Required"}, "roles": {"admin": "Administrator", "moderator": "Moderator", "mentor": "Mentor", "investor": "Investor", "user": "User"}, "logs": {"title": "System Logs", "description": "Monitor system activities and troubleshoot issues", "totalLogs": "Total Logs", "errors": "Errors", "warnings": "Warnings", "lastHour": "Last Hour", "filters": "Filters", "recentLogs": "Recent Logs", "allLevels": "All Levels", "allCategories": "All Categories", "level": {"info": "Info", "warning": "Warning", "error": "Error", "success": "Success"}}, "superAdmin": {"systemManagement": "System Management", "aiSystemManagement": "AI System Management", "aiConfiguration": {"title": "AI Configuration", "subtitle": "Configure AI services, models, and system settings"}, "aiAnalytics": {"title": "AI Analytics", "subtitle": "Monitor AI usage, costs, and performance metrics"}, "aiMonitoring": {"title": "AI Monitoring", "subtitle": "Real-time monitoring of AI system health and performance"}, "userImpersonation": "User Impersonation", "systemLogs": "System Logs", "analytics": "Analytics", "security": "Security Center", "performance": "Performance Center", "api": "API Management", "communication": "Communication Center", "backup": "Backup Management", "monitoring": "System Monitoring", "systemHealth": "System Health", "dashboard": {"title": "Super Admin Dashboard", "subtitle": "Complete system control and monitoring"}, "advancedUsers": "Advanced User Management", "backupManagement": "Backup Management", "communicationCenter": "Communication Center", "performanceMonitoring": "Performance Monitoring", "aiSystem": {"title": "AI System Management", "subtitle": "Manage AI models, usage, and configuration"}}, "systemManagement": {"title": "System Management", "description": "Comprehensive system operations management", "operations": "Operations", "executeOperation": "Execute Operation", "confirmExecution": "Confirm Execution", "operationSuccess": "Operation executed successfully", "operationFailed": "Operation execution failed", "databaseBackup": "Database Backup", "databaseRestore": "Database Restore", "cacheClearing": "<PERSON><PERSON>ing", "systemRestart": "System Restart", "securityScan": "Security Scan", "performanceTuning": "Performance Tuning"}, "communication": {"title": "Communication Center", "description": "Manage all platform communications and messaging", "totalMessages": "Total Messages", "unreadMessages": "Unread Messages", "notificationsSent": "Notifications Sent", "emailCampaigns": "Email Campaigns", "activeUsers": "Active Users", "deliveryRate": "Delivery Rate", "broadcastMessage": "Broadcast Message", "exportData": "Export Data"}}, "superAdmin": {"systemManagement": "System Management", "aiSystemManagement": "AI System Management", "aiConfiguration": "AI Configuration", "aiAnalytics": "AI Analytics", "aiMonitoring": "AI Monitoring", "userImpersonation": "User Impersonation", "systemLogs": "System Logs", "analytics": "Analytics", "security": "Security Center", "performance": "Performance Center", "api": "API Management", "communication": "Communication Center", "backup": "Backup Management", "monitoring": "System Monitoring", "systemHealth": "System Health", "advancedUsers": "Advanced User Management", "realtimeDashboard": "Real-Time Dashboard", "userAnalytics": {"title": "Advanced User Analytics", "subtitle": "Comprehensive user behavior and engagement analysis"}, "revenueAnalytics": {"title": "Revenue Analytics", "subtitle": "Financial performance and revenue insights"}, "advancedSecurity": "Advanced Security", "realTimeDashboard": {"title": "Real-Time System Dashboard", "subtitle": "Live system monitoring and performance metrics"}, "securityCenter": {"title": "Advanced Security Center", "subtitle": "Comprehensive security monitoring and threat protection"}}}