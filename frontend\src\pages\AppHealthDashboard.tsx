/**
 * COMPREHENSIVE APPLICATION HEALTH DASHBOARD
 * Complete diagnostic interface for identifying and fixing all app issues
 */

import React, { useState, useEffect } from 'react';
import { AppDiagnostics, DiagnosticResult, AppIssue } from '../utils/comprehensiveAppDiagnostics';
import { Alert<PERSON>riangle, CheckCircle, XCircle, Clock, Zap, Settings, Users, Server, Activity } from 'lucide-react';

const AppHealthDashboard: React.FC = () => {
  const [diagnosticResult, setDiagnosticResult] = useState<DiagnosticResult | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [selectedPhase, setSelectedPhase] = useState<'phase1' | 'phase2' | 'phase3' | 'phase4'>('phase1');

  const runDiagnostics = async () => {
    setIsRunning(true);
    try {
      const diagnostics = new AppDiagnostics();
      const result = await diagnostics.runComprehensiveDiagnostics();
      setDiagnosticResult(result);
    } catch (error) {
      console.error('Diagnostics failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  useEffect(() => {
    // Auto-run diagnostics on page load
    runDiagnostics();
  }, []);

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'excellent': return 'text-green-600 bg-green-50 border-green-200';
      case 'good': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'fair': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'poor': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'critical': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getCategoryIcon = (type: string) => {
    switch (type) {
      case 'infrastructure': return <Server className="w-5 h-5" />;
      case 'frontend': return <Activity className="w-5 h-5" />;
      case 'backend': return <Server className="w-5 h-5" />;
      case 'performance': return <Zap className="w-5 h-5" />;
      case 'ux': return <Users className="w-5 h-5" />;
      case 'config': return <Settings className="w-5 h-5" />;
      default: return <AlertTriangle className="w-5 h-5" />;
    }
  };

  const getPriorityColor = (priority: number) => {
    if (priority >= 9) return 'bg-red-500';
    if (priority >= 7) return 'bg-orange-500';
    if (priority >= 5) return 'bg-yellow-500';
    return 'bg-blue-500';
  };

  const IssueCard: React.FC<{ issue: AppIssue }> = ({ issue }) => (
    <div className="bg-white border rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-2">
          {getCategoryIcon(issue.type)}
          <h3 className="font-semibold text-gray-900">{issue.title}</h3>
        </div>
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${getPriorityColor(issue.priority)}`} title={`Priority: ${issue.priority}/10`}></div>
          <span className="text-xs text-gray-500">{issue.estimatedFixTime}</span>
        </div>
      </div>
      
      <p className="text-sm text-gray-600 mb-2">{issue.description}</p>
      
      <div className="space-y-2">
        <div>
          <span className="text-xs font-medium text-red-600">Impact:</span>
          <p className="text-xs text-red-600">{issue.impact}</p>
        </div>
        <div>
          <span className="text-xs font-medium text-green-600">Solution:</span>
          <p className="text-xs text-green-600">{issue.solution}</p>
        </div>
      </div>
      
      <div className="flex items-center justify-between mt-3 pt-3 border-t">
        <span className={`text-xs px-2 py-1 rounded ${
          issue.category === 'critical' ? 'bg-red-100 text-red-800' :
          issue.category === 'high' ? 'bg-orange-100 text-orange-800' :
          issue.category === 'medium' ? 'bg-yellow-100 text-yellow-800' :
          'bg-blue-100 text-blue-800'
        }`}>
          {issue.category.toUpperCase()}
        </span>
        <span className="text-xs text-gray-500 capitalize">{issue.type}</span>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            🏥 Application Health Dashboard
          </h1>
          <p className="text-gray-600">
            Comprehensive diagnostics and issue tracking for the entire application
          </p>
        </div>

        {/* Controls */}
        <div className="mb-6">
          <button
            onClick={runDiagnostics}
            disabled={isRunning}
            className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
              isRunning
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl'
            }`}
          >
            {isRunning ? (
              <span className="flex items-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Running Diagnostics...
              </span>
            ) : (
              '🔍 Run Complete Diagnostics'
            )}
          </button>
        </div>

        {diagnosticResult && (
          <>
            {/* Health Overview */}
            <div className={`rounded-xl p-6 mb-8 border-2 ${getHealthColor(diagnosticResult.overallHealth)}`}>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  {diagnosticResult.overallHealth === 'critical' ? (
                    <XCircle className="w-8 h-8" />
                  ) : diagnosticResult.overallHealth === 'excellent' ? (
                    <CheckCircle className="w-8 h-8" />
                  ) : (
                    <AlertTriangle className="w-8 h-8" />
                  )}
                  <div>
                    <h2 className="text-2xl font-bold">
                      Overall Health: {diagnosticResult.overallHealth.toUpperCase()}
                    </h2>
                    <p className="text-lg">Score: {diagnosticResult.healthScore}/100</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold">{diagnosticResult.totalIssues}</div>
                  <div className="text-sm">Total Issues</div>
                </div>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{diagnosticResult.criticalIssues}</div>
                  <div className="text-sm">Critical Issues</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {Object.keys(diagnosticResult.issuesByCategory).length}
                  </div>
                  <div className="text-sm">Categories Affected</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{diagnosticResult.fixPlan.phase1.length}</div>
                  <div className="text-sm">Urgent Fixes</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{diagnosticResult.fixPlan.estimatedTotalTime}</div>
                  <div className="text-sm">Est. Fix Time</div>
                </div>
              </div>
            </div>

            {/* Recommendations */}
            {diagnosticResult.recommendations.length > 0 && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                <h3 className="text-lg font-semibold text-blue-900 mb-3">🎯 Immediate Recommendations</h3>
                <ul className="space-y-2">
                  {diagnosticResult.recommendations.map((rec, index) => (
                    <li key={index} className="text-blue-800 flex items-start">
                      <span className="mr-2">•</span>
                      <span>{rec}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Fix Plan Phases */}
            <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">📋 Systematic Fix Plan</h3>
              
              {/* Phase Tabs */}
              <div className="flex space-x-1 mb-6 bg-gray-100 rounded-lg p-1">
                {(['phase1', 'phase2', 'phase3', 'phase4'] as const).map((phase) => (
                  <button
                    key={phase}
                    onClick={() => setSelectedPhase(phase)}
                    className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                      selectedPhase === phase
                        ? 'bg-white text-blue-600 shadow-sm'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    {phase === 'phase1' && `Phase 1: Critical (${diagnosticResult.fixPlan.phase1.length})`}
                    {phase === 'phase2' && `Phase 2: Core (${diagnosticResult.fixPlan.phase2.length})`}
                    {phase === 'phase3' && `Phase 3: UX (${diagnosticResult.fixPlan.phase3.length})`}
                    {phase === 'phase4' && `Phase 4: Optimization (${diagnosticResult.fixPlan.phase4.length})`}
                  </button>
                ))}
              </div>

              {/* Phase Content */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {diagnosticResult.fixPlan[selectedPhase].map((issue, index) => (
                  <IssueCard key={`${selectedPhase}-${index}`} issue={issue} />
                ))}
              </div>

              {diagnosticResult.fixPlan[selectedPhase].length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <CheckCircle className="w-12 h-12 mx-auto mb-2 text-green-500" />
                  <p>No issues in this phase! 🎉</p>
                </div>
              )}
            </div>

            {/* Issues by Category */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">📊 Issues by Category</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {Object.entries(diagnosticResult.issuesByCategory).map(([category, issues]) => (
                  <div key={category} className="border rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      {getCategoryIcon(category)}
                      <h4 className="font-semibold capitalize">{category}</h4>
                      <span className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">
                        {issues.length}
                      </span>
                    </div>
                    
                    <div className="space-y-2">
                      {issues.slice(0, 3).map((issue, index) => (
                        <div key={index} className="text-sm">
                          <div className="flex items-center space-x-2">
                            <div className={`w-2 h-2 rounded-full ${getPriorityColor(issue.priority)}`}></div>
                            <span className="truncate">{issue.title}</span>
                          </div>
                        </div>
                      ))}
                      {issues.length > 3 && (
                        <div className="text-xs text-gray-500">
                          +{issues.length - 3} more issues
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        {!diagnosticResult && !isRunning && (
          <div className="text-center py-12">
            <Activity className="w-16 h-16 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-600">Click "Run Complete Diagnostics" to analyze your application health</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AppHealthDashboard;
