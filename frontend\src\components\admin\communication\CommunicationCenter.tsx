import React, { useState, useEffect } from 'react';
import { Mail, Send, Users, Bell, MessageSquare, Calendar, FileText, Settings } from 'lucide-react';
// DashboardLayout removed - handled by routing system with AuthenticatedLayout
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { getAuthToken } from '../../../services/api';

interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  content: string;
  type: 'welcome' | 'notification' | 'newsletter' | 'reminder' | 'custom';
  variables: string[];
}

interface Campaign {
  id: string;
  name: string;
  type: 'email' | 'notification' | 'sms';
  status: 'draft' | 'scheduled' | 'sent' | 'failed';
  recipients: number;
  sent_at?: string;
  scheduled_at?: string;
  open_rate?: number;
  click_rate?: number;
}

const CommunicationCenter: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  
  // State management
  const [activeTab, setActiveTab] = useState<'compose' | 'templates' | 'campaigns' | 'analytics'>('compose');
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [emailForm, setEmailForm] = useState({
    recipients: 'all',
    subject: '',
    content: '',
    schedule: false,
    scheduledDate: '',
    scheduledTime: ''
  });

  // Load communication data from API
  useEffect(() => {
    const loadCommunicationData = async () => {
      try {
        const response = await fetch('/api/superadmin/system/communication/', {
          headers: {
            'Authorization': `Bearer ${getAuthToken()}`,
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const data = await response.json();
          setTemplates(data.templates || []);
          setCampaigns(data.campaigns || []);
        } else {
          console.error('Failed to fetch communication data:', response.status);
          setTemplates([]);
          setCampaigns([]);
        }
      } catch (error) {
        console.error('Error loading communication data:', error);
        setTemplates([]);
        setCampaigns([]);
      }
    };

    loadCommunicationData();
  }, []);

  // Handle template selection
  const handleTemplateSelect = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (template) {
      setSelectedTemplate(templateId);
      setEmailForm(prev => ({
        ...prev,
        subject: template.subject,
        content: template.content
      }));
    }
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
        return 'bg-green-500/20 text-green-300';
      case 'scheduled':
        return 'bg-blue-500/20 text-blue-300';
      case 'draft':
        return 'bg-yellow-500/20 text-yellow-300';
      case 'failed':
        return 'bg-red-500/20 text-red-300';
      default:
        return 'bg-gray-500/20 text-gray-300';
    }
  };

  // Get type icon
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'email':
        return <Mail size={16} className="text-blue-400" />;
      case 'notification':
        return <Bell size={16} className="text-purple-400" />;
      case 'sms':
        return <MessageSquare size={16} className="text-green-400" />;
      default:
        return <Mail size={16} className="text-gray-400" />;
    }
  };

  // Tabs
  const tabs = [
    { id: 'compose', label: t('communication.compose', 'Compose'), icon: <Send size={16} /> },
    { id: 'templates', label: t('communication.templates', 'Templates'), icon: <FileText size={16} /> },
    { id: 'campaigns', label: t('communication.campaigns', 'Campaigns'), icon: <Calendar size={16} /> },
    { id: 'analytics', label: t('communication.analytics', 'Analytics'), icon: <Settings size={16} /> }
  ];

  return (
    <DashboardLayout currentPage="communication">
      {/* Header */}
      <div className={`mb-8 ${isRTL ? "text-right" : ""}`}>
        <h1 className="text-2xl font-bold text-white">{t('admin.communication.center', 'Communication Center')}</h1>
        <div className="text-gray-400 mt-1">{t('admin.communication.description', 'Manage emails, notifications, and campaigns')}</div>
      </div>

      {/* Tabs */}
      <div className="mb-8">
        <div className={`flex space-x-1 bg-indigo-900/30 p-1 rounded-lg ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                activeTab === tab.id
                  ? 'bg-purple-600 text-white shadow-lg'
                  : 'text-gray-300 hover:text-white hover:bg-indigo-800/50'
              } ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}
            >
              {tab.icon}
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'compose' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Compose Form */}
          <div className="lg:col-span-2">
            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50">
              <h3 className="text-lg font-semibold text-white mb-6">{t('communication.compose.message', 'Compose Message')}</h3>
              
              <div className="space-y-4">
                {/* Recipients */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {t('communication.recipients', 'Recipients')}
                  </label>
                  <select
                    value={emailForm.recipients}
                    onChange={(e) => setEmailForm(prev => ({ ...prev, recipients: e.target.value }))}
                    className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                  >
                    <option value="all">{t('communication.all.users', 'All Users')}</option>
                    <option value="entrepreneurs">{t('communication.entrepreneurs', 'Entrepreneurs')}</option>
                    <option value="mentors">{t('communication.mentors', 'Mentors')}</option>
                    <option value="investors">{t('communication.investors', 'Investors')}</option>
                    <option value="custom">{t('communication.custom.list', 'Custom List')}</option>
                  </select>
                </div>

                {/* Subject */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {t('communication.subject', 'Subject')}
                  </label>
                  <input
                    type="text"
                    value={emailForm.subject}
                    onChange={(e) => setEmailForm(prev => ({ ...prev, subject: e.target.value }))}
                    placeholder={t('communication.enter.subject', 'Enter email subject')}
                    className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                  />
                </div>

                {/* Content */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {t('communication.content', 'Content')}
                  </label>
                  <textarea
                    value={emailForm.content}
                    onChange={(e) => setEmailForm(prev => ({ ...prev, content: e.target.value }))}
                    placeholder={t('communication.enter.content', 'Enter your message content')}
                    rows={8}
                    className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 resize-none"
                  />
                </div>

                {/* Schedule */}
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="schedule"
                    checked={emailForm.schedule}
                    onChange={(e) => setEmailForm(prev => ({ ...prev, schedule: e.target.checked }))}
                    className="w-4 h-4 text-purple-600 bg-indigo-800 border-indigo-600 rounded focus:ring-purple-500"
                  />
                  <label htmlFor="schedule" className="text-gray-300">
                    {t('communication.schedule.send', 'Schedule for later')}
                  </label>
                </div>

                {emailForm.schedule && (
                  <div className="grid grid-cols-2 gap-3">
                    <input
                      type="date"
                      value={emailForm.scheduledDate}
                      onChange={(e) => setEmailForm(prev => ({ ...prev, scheduledDate: e.target.value }))}
                      className="bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                    />
                    <input
                      type="time"
                      value={emailForm.scheduledTime}
                      onChange={(e) => setEmailForm(prev => ({ ...prev, scheduledTime: e.target.value }))}
                      className="bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                    />
                  </div>
                )}

                {/* Actions */}
                <div className={`flex space-x-3 pt-4 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                  <button className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg font-medium transition-colors">
                    {t('communication.save.draft', 'Save Draft')}
                  </button>
                  <button className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg font-medium transition-colors flex items-center space-x-2">
                    <Send size={16} />
                    <span>{emailForm.schedule ? t('communication.schedule', 'Schedule') : t('communication.send.now', 'Send Now')}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Templates Sidebar */}
          <div>
            <div className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50">
              <h3 className="text-lg font-semibold text-white mb-4">{t('communication.quick.templates', 'Quick Templates')}</h3>
              
              <div className="space-y-3">
                {templates.map((template) => (
                  <button
                    key={template.id}
                    onClick={() => handleTemplateSelect(template.id)}
                    className={`w-full p-3 rounded-lg border transition-all duration-300 text-left ${
                      selectedTemplate === template.id
                        ? 'border-purple-500 bg-purple-500/20'
                        : 'border-indigo-700 bg-indigo-800/30 hover:border-indigo-600'
                    }`}
                  >
                    <h4 className="text-white font-medium mb-1">{template.name}</h4>
                    <p className="text-gray-400 text-sm line-clamp-2">{template.subject}</p>
                    <span className={`inline-block mt-2 px-2 py-1 rounded-full text-xs ${
                      template.type === 'welcome' ? 'bg-green-500/20 text-green-300' :
                      template.type === 'notification' ? 'bg-blue-500/20 text-blue-300' :
                      template.type === 'newsletter' ? 'bg-purple-500/20 text-purple-300' :
                      'bg-gray-500/20 text-gray-300'
                    }`}>
                      {template.type}
                    </span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'campaigns' && (
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-xl overflow-hidden border border-indigo-800/50">
          <div className="p-6 border-b border-indigo-800/50">
            <div className={`flex justify-between items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <h3 className="text-lg font-semibold text-white">{t('communication.recent.campaigns', 'Recent Campaigns')}</h3>
              <button className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg font-medium transition-colors">
                {t('communication.new.campaign', 'New Campaign')}
              </button>
            </div>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-indigo-800/50">
                <tr>
                  <th className="p-4 text-left text-gray-300 font-medium">{t('communication.campaign', 'Campaign')}</th>
                  <th className="p-4 text-left text-gray-300 font-medium">{t('communication.type', 'Type')}</th>
                  <th className="p-4 text-left text-gray-300 font-medium">{t('communication.status', 'Status')}</th>
                  <th className="p-4 text-left text-gray-300 font-medium">{t('communication.recipients', 'Recipients')}</th>
                  <th className="p-4 text-left text-gray-300 font-medium">{t('communication.performance', 'Performance')}</th>
                  <th className="p-4 text-left text-gray-300 font-medium">{t('communication.date', 'Date')}</th>
                </tr>
              </thead>
              <tbody>
                {campaigns.map((campaign) => (
                  <tr key={campaign.id} className="border-t border-indigo-800/50 hover:bg-indigo-800/20 transition-colors">
                    <td className="p-4">
                      <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                        {getTypeIcon(campaign.type)}
                        <span className="text-white font-medium">{campaign.name}</span>
                      </div>
                    </td>
                    <td className="p-4">
                      <span className="text-gray-300 capitalize">{campaign.type}</span>
                    </td>
                    <td className="p-4">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(campaign.status)}`}>
                        {campaign.status}
                      </span>
                    </td>
                    <td className="p-4 text-gray-300">
                      {campaign.recipients.toLocaleString()}
                    </td>
                    <td className="p-4">
                      {campaign.open_rate && campaign.click_rate ? (
                        <div className="text-sm">
                          <div className="text-green-400">{campaign.open_rate}% {t('communication.opened', 'opened')}</div>
                          <div className="text-blue-400">{campaign.click_rate}% {t('communication.clicked', 'clicked')}</div>
                        </div>
                      ) : (
                        <span className="text-gray-500">-</span>
                      )}
                    </td>
                    <td className="p-4 text-gray-300 text-sm">
                      {campaign.sent_at 
                        ? new Date(campaign.sent_at).toLocaleDateString()
                        : campaign.scheduled_at 
                        ? new Date(campaign.scheduled_at).toLocaleDateString()
                        : '-'
                      }
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
};

export default CommunicationCenter;
