import {
  useQuery,
  useMutation,
  useInfiniteQuery,
  useQueryClient,
  UseQueryOptions,
  UseMutationOptions,
  UseInfiniteQueryOptions,
  QueryKey
} from '@tanstack/react-query';
import { ApiClient } from '../services/api';
import { PaginatedResponse, QueryParams, SuccessResponse } from '../types/api';
import { ApiError } from '../services/api';

// Create a shared API client instance
const apiClient = new ApiClient();

// Type for mutation variables with ID
interface MutationVariablesWithId<T> {
  id: number | string;
  data?: T;
}

/**
 * Hook for fetching a single item
 */
export function useApiQuery<T>(
  queryKey: QueryKey,
  endpoint: string,
  params?: QueryParams,
  options?: Omit<UseQueryOptions<T, Error, T, QueryKey>, 'queryKey' | 'queryFn'>
) {
  return useQuery<T, Error>({
    queryKey,
    queryFn: ({ signal }) => apiClient.get<T>(endpoint, params, signal),
    ...options
  });
}

/**
 * Hook for fetching a paginated list
 */
export function useApiList<T>(
  queryKey: QueryKey,
  endpoint: string,
  params?: QueryParams,
  options?: Omit<UseQueryOptions<PaginatedResponse<T>, Error, PaginatedResponse<T>, QueryKey>, 'queryKey' | 'queryFn'>
) {
  return useQuery<PaginatedResponse<T>, Error>({
    queryKey,
    queryFn: ({ signal }) => apiClient.getList<T>(endpoint, params, signal),
    ...options
  });
}

/**
 * Hook for infinite scrolling with pagination
 */
export function useApiInfinite<T>(
  queryKey: QueryKey,
  endpoint: string,
  params?: QueryParams,
  options?: Omit<UseInfiniteQueryOptions<PaginatedResponse<T>, Error, PaginatedResponse<T>, PaginatedResponse<T>, QueryKey>, 'queryKey' | 'queryFn' | 'getNextPageParam'>
) {
  return useInfiniteQuery<PaginatedResponse<T>, Error>({
    queryKey,
    queryFn: ({ pageParam = 1, signal }) => {
      const pageParams = {
        ...params,
        page: pageParam
      };
      return apiClient.getList<T>(endpoint, pageParams, signal);
    },
    getNextPageParam: (lastPage) => {
      // If there's a next page URL, extract the page number
      if (lastPage.next) {
        const url = new URL(lastPage.next);
        const nextPage = url.searchParams.get('page');
        return nextPage ? parseInt(nextPage) : undefined;
      }
      return undefined;
    },
    ...options
  });
}

/**
 * Hook for prefetching a single item
 */
export function usePrefetchApiQuery<T>(queryKey: QueryKey, endpoint: string, params?: QueryParams) {
  const queryClient = useQueryClient();

  return async () => {
    await queryClient.prefetchQuery({
      queryKey,
      queryFn: ({ signal }) => apiClient.get<T>(endpoint, params, signal)
    });
  };
}

/**
 * Hook for prefetching a paginated list
 */
export function usePrefetchApiList<T>(queryKey: QueryKey, endpoint: string, params?: QueryParams) {
  const queryClient = useQueryClient();

  return async () => {
    await queryClient.prefetchQuery({
      queryKey,
      queryFn: ({ signal }) => apiClient.getList<T>(endpoint, params, signal)
    });
  };
}

/**
 * Hook for creating an item
 */
export function useApiCreate<T, TData = any>(
  endpoint: string,
  options?: Omit<UseMutationOptions<T, Error, TData>, 'mutationFn'>
) {
  const queryClient = useQueryClient();

  return useMutation<T, Error, TData>({
    mutationFn: (data: TData) => apiClient.post<T>(endpoint, data),
    ...options
  });
}

/**
 * Hook for updating an item
 */
export function useApiUpdate<T, TData = any>(
  endpoint: string,
  options?: Omit<UseMutationOptions<T, Error, TData>, 'mutationFn'>
) {
  const queryClient = useQueryClient();

  return useMutation<T, Error, TData>({
    mutationFn: (data: TData) => apiClient.put<T>(endpoint, data),
    ...options
  });
}

/**
 * Hook for updating an item with ID
 */
export function useApiUpdateById<T, TData = any>(
  endpointTemplate: string,
  options?: Omit<UseMutationOptions<T, Error, MutationVariablesWithId<TData>>, 'mutationFn'>
) {
  const queryClient = useQueryClient();

  return useMutation<T, Error, MutationVariablesWithId<TData>>({
    mutationFn: ({ id, data }) => {
      const endpoint = endpointTemplate.replace(':id', String(id));
      return apiClient.put<T>(endpoint, data);
    },
    ...options
  });
}

/**
 * Hook for partially updating an item
 */
export function useApiPatch<T, TData = any>(
  endpoint: string,
  options?: Omit<UseMutationOptions<T, Error, TData>, 'mutationFn'>
) {
  const queryClient = useQueryClient();

  return useMutation<T, Error, TData>({
    mutationFn: (data: TData) => apiClient.patch<T>(endpoint, data),
    ...options
  });
}

/**
 * Hook for partially updating an item with ID
 */
export function useApiPatchById<T, TData = any>(
  endpointTemplate: string,
  options?: Omit<UseMutationOptions<T, Error, MutationVariablesWithId<TData>>, 'mutationFn'>
) {
  const queryClient = useQueryClient();

  return useMutation<T, Error, MutationVariablesWithId<TData>>({
    mutationFn: ({ id, data }) => {
      const endpoint = endpointTemplate.replace(':id', String(id));
      return apiClient.patch<T>(endpoint, data);
    },
    ...options
  });
}

/**
 * Hook for deleting an item
 */
export function useApiDelete<T>(
  endpoint: string,
  options?: Omit<UseMutationOptions<T, Error, void>, 'mutationFn'>
) {
  const queryClient = useQueryClient();

  return useMutation<T, Error, void>({
    mutationFn: () => apiClient.delete<T>(endpoint),
    ...options
  });
}

/**
 * Hook for deleting an item with ID
 */
export function useApiDeleteById<T>(
  endpointTemplate: string,
  options?: Omit<UseMutationOptions<T, Error, number | string>, 'mutationFn'>
) {
  const queryClient = useQueryClient();

  return useMutation<T, Error, number | string>({
    mutationFn: (id) => {
      const endpoint = endpointTemplate.replace(':id', String(id));
      return apiClient.delete<T>(endpoint);
    },
    ...options
  });
}

/**
 * Hook for uploading files
 */
export function useApiUpload<T>(
  endpoint: string,
  options?: Omit<UseMutationOptions<T, Error, FormData>, 'mutationFn'>
) {
  return useMutation<T, Error, FormData>({
    mutationFn: (formData) => apiClient.upload<T>(endpoint, formData),
    ...options
  });
}
