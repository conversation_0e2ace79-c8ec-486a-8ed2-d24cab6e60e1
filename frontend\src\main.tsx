import { StrictMode, Suspense, lazy } from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { store, persistor } from './store';
import ErrorBoundary from './components/ErrorBoundary';
import LoadingFallback from './components/ui/LoadingFallback';

// Lazy load heavy components to improve startup performance
const AppRoutes = lazy(() => import('./routes/AppRoutes'));
const LanguageProvider = lazy(() => import('./components/LanguageProvider'));
const QueryProvider = lazy(() => import('./providers/QueryProvider'));
const ToastProvider = lazy(() => import('./components/ui/Toast'));
const AIContextSync = lazy(() => import('./components/AIContextSync'));
const SessionManager = lazy(() => import('./components/SessionManager'));
const PerformanceMonitor = lazy(() => import('./components/PerformanceMonitor'));
const PerformanceDashboard = lazy(() => import('./components/PerformanceDashboard'));

// Import analytics and service worker normally (they're utilities, not components)
import { initAnalytics, trackError } from './utils/analytics';
import { register as registerServiceWorker } from './utils/serviceWorkerRegistration';


import './index.css';
import './styles/rtl-consolidated.css';
import './i18n/index'; // Import i18n configuration



// Glass morphism styling applied via Tailwind CSS



// Initialize performance analytics
initAnalytics({
  sampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0, // 10% of users in production, all in development
  apiEndpoint: '/api/analytics/performance',
});


// Global error handler for unhandled errors
window.addEventListener('error', (event) => {
  // Log all errors in development for debugging
  if (import.meta.env.DEV) {
    console.error('GLOBAL ERROR HANDLER:', {
      message: event.error?.message,
      stack: event.error?.stack,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno
    });
  }
});

// Global promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
  // Log all promise rejections in development for debugging
  if (import.meta.env.DEV) {
    console.error('GLOBAL PROMISE REJECTION:', {
      reason: event.reason,
      promise: event.promise
    });
  }
});

// Register service worker for offline capabilities
registerServiceWorker({
  onSuccess: (registration) => {
    console.log('Service worker registration successful with scope:', registration.scope);
  },
  onUpdate: (_registration) => {
    console.log('New content is available; please refresh.');
    // You could show a notification to the user here
  },
  onError: (error) => {
    console.error('Error during service worker registration:', error);
    trackError('ServiceWorkerRegistration', error.message);
  },
  onOffline: () => {
    console.log('Application is offline');
    // You could show an offline indicator here
  },
  onOnline: () => {
    console.log('Application is back online');
    // You could hide the offline indicator here
  }
});

// Expose Redux store to window for testing
if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
  (window as any).__REDUX_STORE__ = store;
  (window as any).store = store;
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <ErrorBoundary>
      <Provider store={store}>
        <PersistGate loading={<LoadingFallback />} persistor={persistor}>
          <BrowserRouter>
            <Suspense fallback={<LoadingFallback />}>
              <LanguageProvider>
                <Suspense fallback={<LoadingFallback />}>
                  <QueryProvider>
                    <Suspense fallback={<LoadingFallback />}>
                      <ToastProvider>
                        <Suspense fallback={<LoadingFallback />}>
                          <SessionManager />
                          <AIContextSync />
                          <AppRoutes />
                          <PerformanceMonitor />
                          <PerformanceDashboard />
                        </Suspense>
                      </ToastProvider>
                    </Suspense>
                  </QueryProvider>
                </Suspense>
              </LanguageProvider>
            </Suspense>
          </BrowserRouter>
        </PersistGate>
      </Provider>
    </ErrorBoundary>
  </StrictMode>
);
