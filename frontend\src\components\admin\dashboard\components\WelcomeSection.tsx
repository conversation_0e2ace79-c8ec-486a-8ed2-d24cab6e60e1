import React, { useState, useEffect } from 'react';
import { ShieldAlert, TrendingUp, Users, Activity } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
import { RTLText } from '../../../common';
import { analyticsAPI } from '../../../../services/analyticsApi';

interface WelcomeSectionProps {
  username: string;
}

interface DashboardStats {
  activeUsers: number;
  todayActivity: number;
  growthRate: number;
  systemStatus: 'online' | 'offline' | 'maintenance';
}

/**
 * WelcomeSection component displays welcome message with real-time stats
 */
const WelcomeSection: React.FC<WelcomeSectionProps> = ({ username  }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [stats, setStats] = useState<DashboardStats>({
    activeUsers: 0,
    todayActivity: 0,
    growthRate: 0,
    systemStatus: 'online'
  });
  const [loading, setLoading] = useState(true);

  // Fetch real data from API
  useEffect(() => {
    const fetchDashboardStats = async () => {
      try {
        setLoading(true);

        // Fetch analytics data
        const analyticsData = await analyticsAPI.getAnalytics('1d');

        // Fetch platform metrics
        const platformMetrics = await analyticsAPI.getPlatformMetrics('1d');

        // Calculate real stats from API data with proper null checking
        setStats({
          activeUsers: analyticsData?.overview?.total_users || 0,
          todayActivity: platformMetrics?.daily_activities || 0,
          growthRate: analyticsData?.growth?.user_growth || 0,
          systemStatus: 'online' // This could come from a system health API
        });
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        // Keep default values on error
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardStats();

    // Set up auto-refresh every 5 minutes
    const interval = setInterval(fetchDashboardStats, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  const getSystemStatusColor = () => {
    switch (stats.systemStatus) {
      case 'online':
        return 'text-green-400 bg-green-400';
      case 'maintenance':
        return 'text-yellow-400 bg-yellow-400';
      case 'offline':
        return 'text-red-400 bg-red-400';
      default:
        return 'text-green-400 bg-green-400';
    }
  };

  const getSystemStatusText = () => {
    switch (stats.systemStatus) {
      case 'online':
        return t('admin.systemOnline', 'System Online');
      case 'maintenance':
        return t('admin.systemMaintenance', 'Under Maintenance');
      case 'offline':
        return t('admin.systemOffline', 'System Offline');
      default:
        return t('admin.systemOnline', 'System Online');
    }
  };

  return (
    <div className="bg-black/30 backdrop-blur-sm border border-purple-500 rounded-2xl p-6 mb-8">
      {/* Welcome Header */}
      <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div>
          <RTLText as="h1" className="text-3xl font-bold text-white mb-2">
            {t('admin.welcomeBack', 'Welcome back')}, {username || 'Admin'}! 👋
          </RTLText>
          <RTLText className="text-gray-300 text-lg">
            {t('admin.communityToday', 'Here\'s what\'s happening in your community today')}
          </RTLText>
        </div>
        <div className="hidden md:flex items-center space-x-4">
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={`w-3 h-3 rounded-full animate-pulse mr-2 ${getSystemStatusColor().split(' ')[1]}`}></div>
            <span className={`text-sm font-medium ${getSystemStatusColor().split(' ')[0]}`}>
              {getSystemStatusText()}
            </span>
          </div>
        </div>
      </div>

      {/* Quick Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
        <div className="bg-black/35 backdrop-blur-sm border border-purple-500 rounded-xl p-4">
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="p-2 bg-blue-500/20 rounded-lg">
              <Users size={20} className="text-blue-400" />
            </div>
            <div className={isRTL ? 'mr-3' : 'ml-3'}>
              <RTLText as="p" className="text-sm text-gray-300">{t('admin.activeUsers', 'Active Users')}</RTLText>
              <RTLText as="p" className="text-xl font-bold text-white">
                {loading ? (
                  <span className="animate-pulse">---</span>
                ) : (
                  stats.activeUsers.toLocaleString()
                )}
              </RTLText>
            </div>
          </div>
        </div>

        <div className="bg-black/35 backdrop-blur-sm border border-purple-500 rounded-xl p-4">
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="p-2 bg-purple-500/20 rounded-lg">
              <Activity size={20} className="text-purple-400" />
            </div>
            <div className={isRTL ? 'mr-3' : 'ml-3'}>
              <RTLText as="p" className="text-sm text-gray-300">{t('admin.todayActivity', 'Today\'s Activity')}</RTLText>
              <RTLText as="p" className="text-xl font-bold text-white">
                {loading ? (
                  <span className="animate-pulse">---</span>
                ) : (
                  stats.todayActivity.toLocaleString()
                )}
              </RTLText>
            </div>
          </div>
        </div>

        <div className="bg-black/35 backdrop-blur-sm border border-purple-500 rounded-xl p-4">
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="p-2 bg-green-500/20 rounded-lg">
              <TrendingUp size={20} className="text-green-400" />
            </div>
            <div className={isRTL ? 'mr-3' : 'ml-3'}>
              <RTLText as="p" className="text-sm text-gray-300">{t('admin.growthRate', 'Growth Rate')}</RTLText>
              <RTLText as="p" className="text-xl font-bold text-white">
                {loading ? (
                  <span className="animate-pulse">---</span>
                ) : (
                  `${stats.growthRate > 0 ? '+' : ''}${stats.growthRate.toFixed(1)}%`
                )}
              </RTLText>
            </div>
          </div>
        </div>
      </div>

      {/* Admin Security Note */}
      <div className="mt-6 p-4 bg-gradient-to-r from-red-900/30 to-orange-900/30 border border-red-500/30 rounded-xl">
        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className="p-2 bg-red-500/20 rounded-lg">
            <ShieldAlert size={20} className="text-red-500" />
          </div>
          <div className={isRTL ? 'mr-3' : 'ml-3'}>
            <RTLText as="h4" className="font-semibold text-red-300 mb-1">
              {t('admin.securityNotice', 'Security Notice')}
            </RTLText>
            <RTLText as="p" className="text-sm text-red-200">
              {t('admin.adminNote', 'Note: You are accessing the admin panel. Handle data with care.')}
            </RTLText>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WelcomeSection;
