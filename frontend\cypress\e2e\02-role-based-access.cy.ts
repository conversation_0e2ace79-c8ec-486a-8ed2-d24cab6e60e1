import { LoginPage } from '../support/page-objects/LoginPage'
import { DashboardPage } from '../support/page-objects/DashboardPage'

describe('Role-Based Access Control (RBAC)', () => {
  let loginPage: LoginPage
  let dashboardPage: DashboardPage

  beforeEach(() => {
    loginPage = new LoginPage()
    dashboardPage = new DashboardPage()
  })

  describe('User Role Access', () => {
    beforeEach(() => {
      cy.intercept('POST', '**/api/auth/login/', { fixture: 'login-response.json' }).as('userLogin')
      cy.intercept('GET', '**/api/auth/user/', { fixture: 'user.json' }).as('getUser')
      
      cy.loginAs('user')
    })

    it('should have access to user dashboard', () => {
      dashboardPage.visit('user')
        .shouldBeVisible()
        .shouldShowCorrectRoleBasedContent('user')
        .shouldHaveCorrectNavigation('user')
    })

    it('should be able to create business plans', () => {
      cy.navigateViaSidebar('Business Plans')
      cy.url().should('include', '/business-plans')
      
      cy.getByTestId('create-business-plan-button').should('be.visible').click()
      cy.url().should('include', '/business-plans/create')
    })

    it('should be able to view mentorship opportunities', () => {
      cy.navigateViaSidebar('Mentorship')
      cy.url().should('include', '/mentorship')
      
      cy.getByTestId('mentorship-list').should('be.visible')
      cy.getByTestId('request-mentor-button').should('be.visible')
    })

    it('should NOT have access to admin routes', () => {
      // Try to access admin dashboard directly
      cy.visit('/admin/dashboard', { failOnStatusCode: false })
      
      // Should be redirected or show access denied
      cy.url().should('not.include', '/admin/dashboard')
      cy.get('body').should('contain.text', 'Access Denied')
        .or('contain.text', '403')
        .or('contain.text', 'Unauthorized')
    })

    it('should NOT have access to super admin routes', () => {
      cy.visit('/superadmin/dashboard', { failOnStatusCode: false })
      cy.url().should('not.include', '/superadmin/dashboard')
    })

    it('should NOT see admin navigation items', () => {
      dashboardPage.visit('user')
      
      // Should not see admin-specific navigation
      cy.getByTestId('nav-user-management').should('not.exist')
      cy.getByTestId('nav-system-settings').should('not.exist')
      cy.getByTestId('nav-analytics').should('not.exist')
    })
  })

  describe('Admin Role Access', () => {
    beforeEach(() => {
      const adminLoginResponse = {
        ...require('../fixtures/login-response.json'),
        user: require('../fixtures/admin.json')
      }
      
      cy.intercept('POST', '**/api/auth/login/', { body: adminLoginResponse }).as('adminLogin')
      cy.intercept('GET', '**/api/auth/user/', { fixture: 'admin.json' }).as('getAdmin')
      
      cy.loginAs('admin')
    })

    it('should have access to admin dashboard', () => {
      dashboardPage.visit('admin')
        .shouldBeVisible()
        .shouldShowCorrectRoleBasedContent('admin')
        .shouldHaveCorrectNavigation('admin')
    })

    it('should be able to manage users', () => {
      cy.navigateViaSidebar('User Management')
      cy.url().should('include', '/admin/users')
      
      cy.getByTestId('users-table').should('be.visible')
      cy.getByTestId('create-user-button').should('be.visible')
      cy.getByTestId('export-users-button').should('be.visible')
    })

    it('should be able to view analytics', () => {
      cy.navigateViaSidebar('Analytics')
      cy.url().should('include', '/admin/analytics')
      
      cy.getByTestId('analytics-dashboard').should('be.visible')
      cy.getByTestId('chart-container').should('be.visible')
    })

    it('should be able to manage business plans', () => {
      cy.navigateViaSidebar('Business Plans')
      cy.url().should('include', '/admin/business-plans')
      
      // Should see all business plans, not just own
      cy.getByTestId('business-plans-table').should('be.visible')
      cy.getByTestId('approve-business-plan').should('be.visible')
      cy.getByTestId('reject-business-plan').should('be.visible')
    })

    it('should have access to user routes', () => {
      // Admin should also be able to access user features
      cy.visit('/dashboard')
      cy.url().should('include', '/dashboard')
      
      cy.visit('/business-plans')
      cy.url().should('include', '/business-plans')
    })

    it('should NOT have access to super admin routes', () => {
      cy.visit('/superadmin/dashboard', { failOnStatusCode: false })
      cy.url().should('not.include', '/superadmin/dashboard')
    })
  })

  describe('Super Admin Role Access', () => {
    beforeEach(() => {
      const superAdminUser = {
        ...require('../fixtures/admin.json'),
        role: 'superadmin',
        is_superuser: true,
        permissions: [
          ...require('../fixtures/admin.json').permissions,
          'manage_admins',
          'system_configuration',
          'global_analytics'
        ]
      }
      
      const superAdminLoginResponse = {
        ...require('../fixtures/login-response.json'),
        user: superAdminUser
      }
      
      cy.intercept('POST', '**/api/auth/login/', { body: superAdminLoginResponse }).as('superAdminLogin')
      cy.intercept('GET', '**/api/auth/user/', { body: superAdminUser }).as('getSuperAdmin')
      
      cy.loginAs('superadmin')
    })

    it('should have access to super admin dashboard', () => {
      dashboardPage.visit('superadmin')
        .shouldBeVisible()
        .shouldShowCorrectRoleBasedContent('superadmin')
        .shouldHaveCorrectNavigation('superadmin')
    })

    it('should be able to manage admins', () => {
      cy.navigateViaSidebar('Admin Management')
      cy.url().should('include', '/superadmin/admins')
      
      cy.getByTestId('admins-table').should('be.visible')
      cy.getByTestId('create-admin-button').should('be.visible')
      cy.getByTestId('promote-user-button').should('be.visible')
    })

    it('should be able to access system settings', () => {
      cy.navigateViaSidebar('System Settings')
      cy.url().should('include', '/superadmin/settings')
      
      cy.getByTestId('system-configuration').should('be.visible')
      cy.getByTestId('feature-flags').should('be.visible')
      cy.getByTestId('maintenance-mode').should('be.visible')
    })

    it('should have access to all lower-level routes', () => {
      // Super admin should access admin routes
      cy.visit('/admin/dashboard')
      cy.url().should('include', '/admin/dashboard')
      
      // Super admin should access user routes
      cy.visit('/dashboard')
      cy.url().should('include', '/dashboard')
    })
  })

  describe('Mentor Role Access', () => {
    beforeEach(() => {
      const mentorUser = {
        ...require('../fixtures/user.json'),
        role: 'mentor',
        permissions: [
          'view_dashboard',
          'manage_mentees',
          'schedule_sessions',
          'view_mentor_resources'
        ]
      }
      
      const mentorLoginResponse = {
        ...require('../fixtures/login-response.json'),
        user: mentorUser
      }
      
      cy.intercept('POST', '**/api/auth/login/', { body: mentorLoginResponse }).as('mentorLogin')
      cy.intercept('GET', '**/api/auth/user/', { body: mentorUser }).as('getMentor')
      
      cy.loginAs('mentor')
    })

    it('should have access to mentor dashboard', () => {
      dashboardPage.visit('mentor')
        .shouldBeVisible()
        .shouldShowCorrectRoleBasedContent('mentor')
        .shouldHaveCorrectNavigation('mentor')
    })

    it('should be able to manage mentees', () => {
      cy.navigateViaSidebar('Mentees')
      cy.url().should('include', '/mentor/mentees')
      
      cy.getByTestId('mentees-list').should('be.visible')
      cy.getByTestId('accept-mentee-request').should('be.visible')
    })

    it('should be able to schedule sessions', () => {
      cy.navigateViaSidebar('Sessions')
      cy.url().should('include', '/mentor/sessions')
      
      cy.getByTestId('sessions-calendar').should('be.visible')
      cy.getByTestId('schedule-session-button').should('be.visible')
    })

    it('should NOT have access to admin routes', () => {
      cy.visit('/admin/dashboard', { failOnStatusCode: false })
      cy.url().should('not.include', '/admin/dashboard')
    })
  })

  describe('Permission-Based Access', () => {
    it('should enforce granular permissions', () => {
      // Login as user with limited permissions
      const limitedUser = {
        ...require('../fixtures/user.json'),
        permissions: ['view_dashboard'] // Only dashboard access
      }
      
      cy.intercept('GET', '**/api/auth/user/', { body: limitedUser }).as('getLimitedUser')
      cy.loginAs('user')
      
      // Should be able to access dashboard
      cy.visit('/dashboard')
      cy.url().should('include', '/dashboard')
      
      // Should NOT be able to create business plans
      cy.visit('/business-plans/create', { failOnStatusCode: false })
      cy.get('body').should('contain.text', 'Access Denied')
    })

    it('should check permissions on API calls', () => {
      cy.loginAs('user')
      
      // Intercept API call with permission check
      cy.intercept('POST', '**/api/business-plans/', (req) => {
        // Simulate permission check
        const hasPermission = req.headers.authorization && 
                             req.body.user_permissions?.includes('create_business_plan')
        
        if (!hasPermission) {
          req.reply({ statusCode: 403, body: { error: 'Permission denied' } })
        } else {
          req.reply({ statusCode: 201, body: { id: 1, title: 'Test Plan' } })
        }
      }).as('createBusinessPlan')
      
      cy.visit('/business-plans/create')
      cy.getByTestId('business-plan-form').within(() => {
        cy.getByTestId('title-input').type('Test Business Plan')
        cy.getByTestId('submit-button').click()
      })
      
      cy.wait('@createBusinessPlan')
      cy.checkToast('Permission denied', 'error')
    })
  })

  describe('Dynamic Role Changes', () => {
    it('should handle role changes during session', () => {
      cy.loginAs('user')
      
      // Simulate role upgrade
      const upgradedUser = {
        ...require('../fixtures/user.json'),
        role: 'admin',
        permissions: require('../fixtures/admin.json').permissions
      }
      
      cy.intercept('GET', '**/api/auth/user/', { body: upgradedUser }).as('getUpgradedUser')
      
      // Trigger user data refresh
      cy.getByTestId('user-profile').click()
      cy.getByTestId('refresh-profile').click()
      
      cy.wait('@getUpgradedUser')
      
      // Should now have admin access
      cy.visit('/admin/dashboard')
      cy.url().should('include', '/admin/dashboard')
    })

    it('should handle role downgrade', () => {
      cy.loginAs('admin')
      
      // Simulate role downgrade
      const downgradedUser = {
        ...require('../fixtures/admin.json'),
        role: 'user',
        permissions: require('../fixtures/user.json').permissions
      }
      
      cy.intercept('GET', '**/api/auth/user/', { body: downgradedUser }).as('getDowngradedUser')
      
      // Trigger user data refresh
      cy.getByTestId('user-profile').click()
      cy.getByTestId('refresh-profile').click()
      
      cy.wait('@getDowngradedUser')
      
      // Should lose admin access
      cy.visit('/admin/dashboard', { failOnStatusCode: false })
      cy.url().should('not.include', '/admin/dashboard')
    })
  })

  describe('Security Tests', () => {
    it('should prevent privilege escalation', () => {
      cy.loginAs('user')
      
      // Try to modify role in localStorage
      cy.window().then((win) => {
        const userData = JSON.parse(win.localStorage.getItem('user') || '{}')
        userData.role = 'admin'
        win.localStorage.setItem('user', JSON.stringify(userData))
      })
      
      // API should still enforce server-side role check
      cy.intercept('GET', '**/api/admin/**', { statusCode: 403 }).as('adminApiCall')
      
      cy.visit('/admin/dashboard', { failOnStatusCode: false })
      cy.url().should('not.include', '/admin/dashboard')
    })

    it('should validate permissions on every request', () => {
      cy.loginAs('admin')
      
      // Intercept all API calls to check authorization header
      cy.intercept('**', (req) => {
        if (req.url.includes('/api/')) {
          expect(req.headers).to.have.property('authorization')
          expect(req.headers.authorization).to.include('Bearer')
        }
      }).as('apiCalls')
      
      dashboardPage.visit('admin')
      cy.navigateViaSidebar('User Management')
      
      // All API calls should have proper authorization
      cy.get('@apiCalls.all').should('have.length.greaterThan', 0)
    })
  })
})
