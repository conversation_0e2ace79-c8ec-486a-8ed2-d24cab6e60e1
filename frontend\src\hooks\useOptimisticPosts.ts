import { useCallback } from 'react';
import { useQueryClient, useMutation } from '@tanstack/react-query';
import { Post, postsAPI } from '../services/api';
import { toast } from '../components/ui/Toast';
import { useTranslation } from 'react-i18next';
import { invalidateRelatedQueries, optimisticListUpdate } from '../utils/queryUtils';

// Query keys for posts
export const PostKeys = {
  all: ['posts'] as const,
  lists: () => [...PostKeys.all, 'list'] as const,
  list: (filters: any) => [...PostKeys.lists(), filters] as const,
  details: () => [...PostKeys.all, 'detail'] as const,
  detail: (id: number) => [...PostKeys.details(), id] as const,
};

/**
 * Hook for creating a post with optimistic updates
 */
export function useCreatePost() {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  
  return useMutation({
    mutationKey: ['posts', 'create'],
    mutationFn: (newPost: Partial<Post>) => postsAPI.createPost(newPost),
    onMutate: async (newPost) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: PostKeys.lists() });
      
      // Snapshot the previous value
      const previousPosts = queryClient.getQueryData(PostKeys.lists());
      
      // Create a temporary ID for the new post
      const tempId = Date.now();
      
      // Create an optimistic post
      const optimisticPost = {
        id: tempId,
        ...newPost,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        author: newPost.author || { id: 0, username: 'unknown' }, // Use provided author or fallback
        likes_count: 0,
        comments_count: 0,
        moderation_status: 'pending',
      };
      
      // Optimistically update the cache
      optimisticListUpdate(
        queryClient,
        PostKeys.list({}),
        optimisticPost as Post,
        'add'
      );
      
      // Return a context object with the snapshot
      return { previousPosts, optimisticPost };
    },
    onError: (err, newPost, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousPosts) {
        queryClient.setQueryData(PostKeys.lists(), context.previousPosts);
      }
      toast.error(t('errors.posts.createFailed'));
    },
    onSuccess: (data, variables, context) => {
      // If we have an optimistic post ID, we need to update references to it
      if (context?.optimisticPost) {
        // Update any components that might be using the optimistic ID
        queryClient.setQueryData(
          PostKeys.detail(context.optimisticPost.id as number),
          data
        );
      }
      
      toast.success(t('success.posts.created'));
    },
    onSettled: () => {
      // Always invalidate related queries after error or success
      invalidateRelatedQueries(queryClient, 'posts');
    },
  });
}

/**
 * Hook for updating a post with optimistic updates
 */
export function useUpdatePost() {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  
  return useMutation({
    mutationKey: ['posts', 'update'],
    mutationFn: ({ id, ...data }: { id: number } & Partial<Post>) => 
      postsAPI.updatePost(id, data),
    onMutate: async ({ id, ...updatedPost }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: PostKeys.detail(id) });
      
      // Snapshot the previous value
      const previousPost = queryClient.getQueryData(PostKeys.detail(id));
      
      // Get the current post data
      const currentPost = previousPost as Post;
      
      // Create an optimistic post update
      const optimisticPost = { 
        ...currentPost,
        ...updatedPost,
        updated_at: new Date().toISOString(),
      };
      
      // Optimistically update the detail cache
      queryClient.setQueryData(PostKeys.detail(id), optimisticPost);
      
      // Also update in the list if it exists
      optimisticListUpdate(
        queryClient,
        PostKeys.list({}),
        optimisticPost as Post,
        'update'
      );
      
      // Return a context with the snapshot
      return { previousPost, optimisticPost };
    },
    onError: (err, { id }, context) => {
      // If the mutation fails, use the context to roll back
      if (context?.previousPost) {
        queryClient.setQueryData(PostKeys.detail(id), context.previousPost);
        
        // Also roll back the list update
        queryClient.invalidateQueries({ queryKey: PostKeys.lists() });
      }
      toast.error(t('errors.posts.updateFailed'));
    },
    onSuccess: () => {
      toast.success(t('success.posts.updated'));
    },
    onSettled: (data, error, { id }) => {
      // Always invalidate related queries after error or success
      invalidateRelatedQueries(queryClient, 'posts', id);
    },
  });
}

/**
 * Hook for deleting a post with optimistic updates
 */
export function useDeletePost() {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  
  return useMutation({
    mutationKey: ['posts', 'delete'],
    mutationFn: (id: number) => postsAPI.deletePost(id),
    onMutate: async (id) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: PostKeys.lists() });
      await queryClient.cancelQueries({ queryKey: PostKeys.detail(id) });
      
      // Snapshot the previous values
      const previousPosts = queryClient.getQueryData(PostKeys.lists());
      const previousPost = queryClient.getQueryData(PostKeys.detail(id));
      
      // Optimistically remove from the list
      optimisticListUpdate(
        queryClient,
        PostKeys.list({}),
        { id } as Post,
        'remove',
        id
      );
      
      // Optimistically remove from the detail cache
      queryClient.removeQueries({ queryKey: PostKeys.detail(id) });
      
      // Return a context with the snapshots
      return { previousPosts, previousPost };
    },
    onError: (err, id, context) => {
      // If the mutation fails, use the context to roll back
      if (context?.previousPosts) {
        queryClient.setQueryData(PostKeys.lists(), context.previousPosts);
      }
      if (context?.previousPost) {
        queryClient.setQueryData(PostKeys.detail(id), context.previousPost);
      }
      toast.error(t('errors.posts.deleteFailed'));
    },
    onSuccess: () => {
      toast.success(t('success.posts.deleted'));
    },
    onSettled: () => {
      // Always invalidate related queries after error or success
      invalidateRelatedQueries(queryClient, 'posts');
    },
  });
}
