/**
 * COMPREHENSIVE RBAC SYSTEM TESTS
 * Tests for the unified role management system
 */

import {
  getUserRole,
  getUserRoles,
  getUserPermissions,
  hasPermission,
  hasAnyRole,
  canAccessRoute,
  getDashboardRoute,
  UserRole,
  PermissionLevel
} from '../unifiedRoleManager';

// Mock user data matching backend UserSerializer structure
const mockUsers = {
  superAdmin: {
    id: 1,
    username: 'superadmin',
    email: '<EMAIL>',
    is_superuser: true,
    is_staff: true,
    user_role: 'super_admin' as UserRole,
    role_permissions: ['read', 'write', 'moderate', 'admin', 'super_admin'] as PermissionLevel[],
    profile: {
      primary_role: { name: 'super_admin' },
      active_roles: []
    }
  },
  admin: {
    id: 2,
    username: 'admin',
    email: '<EMAIL>',
    is_superuser: false,
    is_staff: true,
    user_role: 'admin' as User<PERSON><PERSON>,
    role_permissions: ['read', 'write', 'moderate', 'admin'] as PermissionLevel[],
    profile: {
      primary_role: { name: 'admin' },
      active_roles: []
    }
  },
  mentor: {
    id: 3,
    username: 'mentor',
    email: '<EMAIL>',
    is_superuser: false,
    is_staff: false,
    user_role: 'mentor' as UserRole,
    role_permissions: ['read', 'write'] as PermissionLevel[],
    profile: {
      primary_role: { name: 'mentor' },
      active_roles: [{ name: 'mentor' }]
    }
  },
  investor: {
    id: 4,
    username: 'investor',
    email: '<EMAIL>',
    is_superuser: false,
    is_staff: false,
    user_role: 'investor' as UserRole,
    role_permissions: ['read', 'write'] as PermissionLevel[],
    profile: {
      primary_role: { name: 'investor' },
      active_roles: [{ name: 'investor' }]
    }
  },
  moderator: {
    id: 5,
    username: 'moderator',
    email: '<EMAIL>',
    is_superuser: false,
    is_staff: false,
    user_role: 'moderator' as UserRole,
    role_permissions: ['read', 'write', 'moderate'] as PermissionLevel[],
    profile: {
      primary_role: { name: 'moderator' },
      active_roles: [{ name: 'moderator' }]
    }
  },
  user: {
    id: 6,
    username: 'user',
    email: '<EMAIL>',
    is_superuser: false,
    is_staff: false,
    user_role: 'user' as UserRole,
    role_permissions: ['read'] as PermissionLevel[],
    profile: {
      primary_role: { name: 'user' },
      active_roles: []
    }
  }
};

describe('Unified Role Manager - RBAC System Tests', () => {
  
  describe('getUserRole() - Role Determination', () => {
    test('should return super_admin for superuser', () => {
      expect(getUserRole(mockUsers.superAdmin)).toBe('super_admin');
    });

    test('should return admin for staff user', () => {
      expect(getUserRole(mockUsers.admin)).toBe('admin');
    });

    test('should return mentor for mentor user', () => {
      expect(getUserRole(mockUsers.mentor)).toBe('mentor');
    });

    test('should return investor for investor user', () => {
      expect(getUserRole(mockUsers.investor)).toBe('investor');
    });

    test('should return moderator for moderator user', () => {
      expect(getUserRole(mockUsers.moderator)).toBe('moderator');
    });

    test('should return user for regular user', () => {
      expect(getUserRole(mockUsers.user)).toBe('user');
    });

    test('should return user for null user', () => {
      expect(getUserRole(null)).toBe('user');
    });

    test('should prioritize user_role from backend', () => {
      const userWithBackendRole = {
        ...mockUsers.user,
        user_role: 'mentor' as UserRole
      };
      expect(getUserRole(userWithBackendRole)).toBe('mentor');
    });
  });

  describe('getUserRoles() - Exclusive Role System', () => {
    test('should return single role array for each user type', () => {
      expect(getUserRoles(mockUsers.superAdmin)).toEqual(['super_admin']);
      expect(getUserRoles(mockUsers.admin)).toEqual(['admin']);
      expect(getUserRoles(mockUsers.mentor)).toEqual(['mentor']);
      expect(getUserRoles(mockUsers.investor)).toEqual(['investor']);
      expect(getUserRoles(mockUsers.moderator)).toEqual(['moderator']);
      expect(getUserRoles(mockUsers.user)).toEqual(['user']);
    });

    test('should return single user role for null user', () => {
      expect(getUserRoles(null)).toEqual(['user']);
    });
  });

  describe('getUserPermissions() - Permission System', () => {
    test('should return correct permissions for super_admin', () => {
      const permissions = getUserPermissions(mockUsers.superAdmin);
      expect(permissions).toEqual(['read', 'write', 'moderate', 'admin', 'super_admin']);
    });

    test('should return correct permissions for admin', () => {
      const permissions = getUserPermissions(mockUsers.admin);
      expect(permissions).toEqual(['read', 'write', 'moderate', 'admin']);
    });

    test('should return correct permissions for moderator', () => {
      const permissions = getUserPermissions(mockUsers.moderator);
      expect(permissions).toEqual(['read', 'write', 'moderate']);
    });

    test('should return correct permissions for mentor', () => {
      const permissions = getUserPermissions(mockUsers.mentor);
      expect(permissions).toEqual(['read', 'write']);
    });

    test('should return correct permissions for investor', () => {
      const permissions = getUserPermissions(mockUsers.investor);
      expect(permissions).toEqual(['read', 'write']);
    });

    test('should return read permission for regular user', () => {
      const permissions = getUserPermissions(mockUsers.user);
      expect(permissions).toEqual(['read']);
    });

    test('should return read permission for null user', () => {
      const permissions = getUserPermissions(null);
      expect(permissions).toEqual(['read']);
    });
  });

  describe('hasPermission() - Permission Checking', () => {
    test('super_admin should have all permissions', () => {
      expect(hasPermission(mockUsers.superAdmin, 'read')).toBe(true);
      expect(hasPermission(mockUsers.superAdmin, 'write')).toBe(true);
      expect(hasPermission(mockUsers.superAdmin, 'moderate')).toBe(true);
      expect(hasPermission(mockUsers.superAdmin, 'admin')).toBe(true);
      expect(hasPermission(mockUsers.superAdmin, 'super_admin')).toBe(true);
    });

    test('admin should have admin-level permissions', () => {
      expect(hasPermission(mockUsers.admin, 'read')).toBe(true);
      expect(hasPermission(mockUsers.admin, 'write')).toBe(true);
      expect(hasPermission(mockUsers.admin, 'moderate')).toBe(true);
      expect(hasPermission(mockUsers.admin, 'admin')).toBe(true);
      expect(hasPermission(mockUsers.admin, 'super_admin')).toBe(false);
    });

    test('moderator should have moderate-level permissions', () => {
      expect(hasPermission(mockUsers.moderator, 'read')).toBe(true);
      expect(hasPermission(mockUsers.moderator, 'write')).toBe(true);
      expect(hasPermission(mockUsers.moderator, 'moderate')).toBe(true);
      expect(hasPermission(mockUsers.moderator, 'admin')).toBe(false);
      expect(hasPermission(mockUsers.moderator, 'super_admin')).toBe(false);
    });

    test('mentor should have write-level permissions', () => {
      expect(hasPermission(mockUsers.mentor, 'read')).toBe(true);
      expect(hasPermission(mockUsers.mentor, 'write')).toBe(true);
      expect(hasPermission(mockUsers.mentor, 'moderate')).toBe(false);
      expect(hasPermission(mockUsers.mentor, 'admin')).toBe(false);
      expect(hasPermission(mockUsers.mentor, 'super_admin')).toBe(false);
    });

    test('investor should have write-level permissions', () => {
      expect(hasPermission(mockUsers.investor, 'read')).toBe(true);
      expect(hasPermission(mockUsers.investor, 'write')).toBe(true);
      expect(hasPermission(mockUsers.investor, 'moderate')).toBe(false);
      expect(hasPermission(mockUsers.investor, 'admin')).toBe(false);
      expect(hasPermission(mockUsers.investor, 'super_admin')).toBe(false);
    });

    test('regular user should have only read permission', () => {
      expect(hasPermission(mockUsers.user, 'read')).toBe(true);
      expect(hasPermission(mockUsers.user, 'write')).toBe(false);
      expect(hasPermission(mockUsers.user, 'moderate')).toBe(false);
      expect(hasPermission(mockUsers.user, 'admin')).toBe(false);
      expect(hasPermission(mockUsers.user, 'super_admin')).toBe(false);
    });
  });

  describe('hasAnyRole() - Role Checking', () => {
    test('should correctly identify user roles', () => {
      expect(hasAnyRole(mockUsers.superAdmin, ['super_admin'])).toBe(true);
      expect(hasAnyRole(mockUsers.admin, ['admin', 'super_admin'])).toBe(true);
      expect(hasAnyRole(mockUsers.mentor, ['mentor'])).toBe(true);
      expect(hasAnyRole(mockUsers.investor, ['investor'])).toBe(true);
      expect(hasAnyRole(mockUsers.moderator, ['moderator'])).toBe(true);
      expect(hasAnyRole(mockUsers.user, ['user'])).toBe(true);
    });

    test('should return false for incorrect roles', () => {
      expect(hasAnyRole(mockUsers.user, ['admin', 'super_admin'])).toBe(false);
      expect(hasAnyRole(mockUsers.mentor, ['admin', 'super_admin'])).toBe(false);
      expect(hasAnyRole(mockUsers.investor, ['mentor', 'moderator'])).toBe(false);
    });
  });

  describe('getDashboardRoute() - Role-based Routing', () => {
    test('should return correct dashboard routes for each role', () => {
      expect(getDashboardRoute(mockUsers.superAdmin)).toBe('/super_admin');
      expect(getDashboardRoute(mockUsers.admin)).toBe('/admin');
      expect(getDashboardRoute(mockUsers.moderator)).toBe('/dashboard/moderation');
      expect(getDashboardRoute(mockUsers.mentor)).toBe('/dashboard/mentorship');
      expect(getDashboardRoute(mockUsers.investor)).toBe('/dashboard/investments');
      expect(getDashboardRoute(mockUsers.user)).toBe('/dashboard');
    });

    test('should return default dashboard for null user', () => {
      expect(getDashboardRoute(null)).toBe('/dashboard');
    });
  });

  describe('canAccessRoute() - Route Protection', () => {
    test('super_admin should access all routes', () => {
      expect(canAccessRoute(mockUsers.superAdmin, ['super_admin'], ['super_admin'], true)).toBe(true);
      expect(canAccessRoute(mockUsers.superAdmin, ['admin'], ['admin'], true)).toBe(true);
      expect(canAccessRoute(mockUsers.superAdmin, ['user'], ['read'], true)).toBe(true);
    });

    test('admin should access admin and lower routes', () => {
      expect(canAccessRoute(mockUsers.admin, ['admin', 'super_admin'], ['admin'], true)).toBe(true);
      expect(canAccessRoute(mockUsers.admin, ['user'], ['read'], true)).toBe(true);
      expect(canAccessRoute(mockUsers.admin, ['super_admin'], ['super_admin'], true)).toBe(false);
    });

    test('regular user should only access user routes', () => {
      expect(canAccessRoute(mockUsers.user, ['user'], ['read'], true)).toBe(true);
      expect(canAccessRoute(mockUsers.user, ['admin'], ['admin'], true)).toBe(false);
      expect(canAccessRoute(mockUsers.user, ['super_admin'], ['super_admin'], true)).toBe(false);
    });

    test('should handle public routes correctly', () => {
      expect(canAccessRoute(null, [], [], false)).toBe(true);
      expect(canAccessRoute(mockUsers.user, [], [], false)).toBe(true);
    });
  });
});
