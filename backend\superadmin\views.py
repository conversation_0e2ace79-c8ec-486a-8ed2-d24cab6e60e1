from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.contrib.auth.models import User
from django.db.models import Count, Q, Sum, Avg, F
from django.utils import timezone
from django.core.management import call_command
from django.conf import settings
from django.db import connection
from datetime import timedelta, datetime
import psutil
import os
import subprocess
import json
import shutil
import logging
from .models import (
    SystemHealth, SuperAdminAuditLog, SystemConfiguration,
    SystemAlert, BackupRecord, PerformanceMetric, SecurityEvent
)
from api.super_admin_permissions import (
    IsSuperAdminUser, SuperAdminSystemAccess, SuperAdminAuditMixin,
    get_user_super_admin_capabilities, log_super_admin_action
)
from users.models import UserProfile, UserRole, UserRoleAssignment
from users.serializers import UserSerializer, UserRoleSerializer
from core.models import AIConfiguration
from core.ai_config import get_gemini_config, update_gemini_config, reload_gemini_config

logger = logging.getLogger(__name__)


class SuperAdminSystemViewSet(SuperAdminAuditMixin, viewsets.ViewSet):
    """
    Comprehensive Super Admin System Management ViewSet
    """
    permission_classes = [IsSuperAdminUser]

    def list(self, request):
        """System overview and available endpoints"""
        return Response({
        except Exception as e:
            logger.error(f"Error fetching dashboard data: {str(e)}")
            return Response(
            except:
                network_stats = None

            # Process information
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                try:
                    processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass

            # Sort by CPU usage and get top 10
            top_processes = sorted(processes, key=lambda x: x['cpu_percent'] or 0, reverse=True)[:10]

            health_data = {
                'cpu': {
                    'usage_percent': cpu_percent,
                    'count': cpu_count,
                    'status': self._get_status_from_percentage(cpu_percent)
                },
                'memory': {
                    'total': memory.total,
                    'available': memory.available,
                    'percent': memory.percent,
                    'used': memory.used,
                    'status': self._get_status_from_percentage(memory.percent)
                },
                'disk': {
                    'total': disk.total,
                    'used': disk.used,
                    'free': disk.free,
                    'percent': (disk.used / disk.total) * 100,
                    'status': self._get_status_from_percentage((disk.used / disk.total) * 100)
                },
                'network': network_stats,
                'top_processes': top_processes,
                'overall_status': self._calculate_overall_health_status(cpu_percent, memory.percent, (disk.used / disk.total) * 100),
                'timestamp': timezone.now().isoformat()
            }

            # Save health record
            SystemHealth.objects.create(
                cpu_usage=cpu_percent,
                memory_usage=memory.percent,
                disk_usage=(disk.used / disk.total) * 100,
                network_io=network_stats or {},
                status=health_data['overall_status'],
                details=health_data
            )

            log_super_admin_action(request.user, 'system_health_checked')
            return Response(health_data)

        except Exception as e:
            logger.error(f"Error fetching system health: {str(e)}")
            return Response(
        except Exception:
            return {'daily_growth': 0, 'weekly_growth': 0, 'users_today': 0, 'users_this_week': 0}

    def _get_avg_memory_24h(self):
        """Get average memory usage for last 24 hours"""
        try:
            avg_memory = PerformanceMetric.objects.filter(
                metric_name='memory_usage',
                timestamp__gte=timezone.now() - timedelta(hours=24)
            ).aggregate(avg=Avg('metric_value'))['avg']
            return round(avg_memory, 2) if avg_memory else 0
        except Exception:
            return 0

    def _get_avg_api_response_time(self):
        """Get average API response time"""
        try:
            avg_response = PerformanceMetric.objects.filter(
                metric_name='api_response_time',
                timestamp__gte=timezone.now() - timedelta(hours=24)
            ).aggregate(avg=Avg('metric_value'))['avg']
            return round(avg_response, 2) if avg_response else 0
        except Exception:
            return 0

    def _get_ai_performance_metrics(self):
        """Get AI performance metrics"""
        try:
            ai_metrics = PerformanceMetric.objects.filter(
                category='ai',
                timestamp__gte=timezone.now() - timedelta(hours=24)
            ).aggregate(
                avg_response_time=Avg('metric_value'),
                total_requests=Count('id')
            )
            return {
                'avg_response_time': round(ai_metrics['avg_response_time'], 2) if ai_metrics['avg_response_time'] else 0,
                'total_requests': ai_metrics['total_requests']
            }
        except Exception:
            return {'avg_response_time': 0, 'total_requests': 0}

    def _get_status_from_percentage(self, percentage):
        """Convert percentage to status"""
        if percentage < 70:
            return 'healthy'
        elif percentage < 85:
            return 'warning'
        else:
            return 'critical'

    def _calculate_overall_health_status(self, cpu, memory, disk):
        """Calculate overall system health status"""
        if cpu > 90 or memory > 90 or disk > 95:
            return 'critical'
        elif cpu > 80 or memory > 80 or disk > 85:
            return 'warning'
        else:
            return 'healthy'

    @action(detail=False, methods=['get'])
    def performance(self, request):
        """Performance monitoring and metrics"""
        try:
            # Get performance metrics for different time ranges
            now = timezone.now()
            last_hour = now - timedelta(hours=1)
            last_24h = now - timedelta(hours=24)
            last_week = now - timedelta(days=7)

            # CPU metrics
            cpu_metrics = PerformanceMetric.objects.filter(
                metric_name='cpu_usage',
                timestamp__gte=last_24h
            ).values('timestamp', 'metric_value').order_by('timestamp')

            # Memory metrics
            memory_metrics = PerformanceMetric.objects.filter(
                metric_name='memory_usage',
                timestamp__gte=last_24h
            ).values('timestamp', 'metric_value').order_by('timestamp')

            # API response time metrics
            api_metrics = PerformanceMetric.objects.filter(
                metric_name='api_response_time',
                timestamp__gte=last_24h
            ).values('timestamp', 'metric_value').order_by('timestamp')

            # Database performance
            db_metrics = PerformanceMetric.objects.filter(
                category='database',
                timestamp__gte=last_24h
            ).values('metric_name', 'metric_value', 'timestamp').order_by('timestamp')

            performance_data = {
                'current_metrics': {
                    'cpu_usage': psutil.cpu_percent(),
                    'memory_usage': psutil.virtual_memory().percent,
                    'disk_io': self._get_disk_io_stats(),
                    'network_io': self._get_network_io_stats()
                },
                'historical_data': {
                    'cpu_usage': list(cpu_metrics),
                    'memory_usage': list(memory_metrics),
                    'api_response_time': list(api_metrics),
                    'database_metrics': list(db_metrics)
                },
                'performance_summary': {
                    'avg_cpu_1h': self._get_avg_metric('cpu_usage', last_hour),
                    'avg_memory_1h': self._get_avg_metric('memory_usage', last_hour),
                    'avg_api_response_1h': self._get_avg_metric('api_response_time', last_hour),
                    'peak_cpu_24h': self._get_peak_metric('cpu_usage', last_24h),
                    'peak_memory_24h': self._get_peak_metric('memory_usage', last_24h)
                },
                'timestamp': now.isoformat()
            }

            log_super_admin_action(request.user, 'performance_metrics_viewed')
            return Response(performance_data)

        except Exception as e:
            logger.error(f"Error fetching performance metrics: {str(e)}")
            return Response(
        except Exception as e:
            logger.error(f"Error fetching security data: {str(e)}")
            return Response(
            except Exception as e:
                logger.error(f"Error fetching backup data: {str(e)}")
                return Response(
            except Exception as e:
                logger.error(f"Error initiating backup: {str(e)}")
                return Response(
        except:
            return None

    def _get_network_io_stats(self):
        """Get network I/O statistics"""
        try:
            net_io = psutil.net_io_counters()
            return {
                'bytes_sent': net_io.bytes_sent,
                'bytes_recv': net_io.bytes_recv,
                'packets_sent': net_io.packets_sent,
                'packets_recv': net_io.packets_recv
            }
        except:
            return None

    def _get_avg_metric(self, metric_name, since_time):
        """Get average metric value since specified time"""
        try:
            avg = PerformanceMetric.objects.filter(
                metric_name=metric_name,
                timestamp__gte=since_time
            ).aggregate(avg=Avg('metric_value'))['avg']
            return round(avg, 2) if avg else 0
        except:
            return 0

    def _get_peak_metric(self, metric_name, since_time):
        """Get peak metric value since specified time"""
        try:
            peak = PerformanceMetric.objects.filter(
                metric_name=metric_name,
                timestamp__gte=since_time
            ).aggregate(peak=models.Max('metric_value'))['peak']
            return round(peak, 2) if peak else 0
        except:
            return 0

    def _get_security_recommendations(self):
        """Get security recommendations based on recent events"""
        recommendations = []

        # Check for multiple failed logins
        failed_logins = SecurityEvent.objects.filter(
            event_type='failed_login',
            created_at__gte=timezone.now() - timedelta(hours=24)
        ).count()

        if failed_logins > 10:
            recommendations.append({
                'type': 'warning',
                'title': 'High Failed Login Attempts',
                'description': f'{failed_logins} failed login attempts in the last 24 hours',
                'action': 'Consider implementing rate limiting or IP blocking'
            })

        # Check for unresolved critical events
        critical_events = SecurityEvent.objects.filter(
            severity='critical',
            is_resolved=False
        ).count()

        if critical_events > 0:
            recommendations.append({
                'type': 'critical',
                'title': 'Unresolved Critical Security Events',
                'description': f'{critical_events} critical security events need attention',
                'action': 'Review and resolve critical security events immediately'
            })

        return recommendations

    def _perform_backup(self, backup_record):
        """Perform the actual backup operation"""
        try:
            backup_record.status = 'running'
            backup_record.save()

            # This is a simplified backup implementation
            # In production, this would be more comprehensive
            if backup_record.backup_type == 'database':
                # Database backup
                backup_path = f"/tmp/backup_db_{backup_record.id}_{timezone.now().strftime('%Y%m%d_%H%M%S')}.sql"
                # call_command('dbbackup', '--output-path', backup_path)

            elif backup_record.backup_type == 'files':
                # Files backup
                backup_path = f"/tmp/backup_files_{backup_record.id}_{timezone.now().strftime('%Y%m%d_%H%M%S')}.tar.gz"
                # Implement file backup logic

            else:  # full backup
                backup_path = f"/tmp/backup_full_{backup_record.id}_{timezone.now().strftime('%Y%m%d_%H%M%S')}.tar.gz"
                # Implement full backup logic

            # Simulate backup completion
            backup_record.status = 'completed'
            backup_record.file_path = backup_path
            backup_record.file_size = 1024 * 1024 * 100  # 100MB placeholder
            backup_record.completed_at = timezone.now()
            backup_record.save()

            return True

        except Exception as e:
            backup_record.status = 'failed'
            backup_record.error_message = str(e)
            backup_record.completed_at = timezone.now()
            backup_record.save()
            return False

    @action(detail=False, methods=['get'])
    def communication(self, request):
        """Communication center management"""
        try:
            # Get communication statistics
            from django.contrib.auth.models import User
            from forums.models import Post, Comment

            # Email statistics (mock for now)
            email_stats = {
                'total_sent': 1250,
                'delivery_rate': 98.5,
                'open_rate': 45.2,
                'click_rate': 12.8,
                'bounce_rate': 1.5
            }

            # Notification statistics
            notification_stats = {
                'total_notifications': 5680,
                'delivered': 5432,
                'pending': 248,
                'failed': 0
            }

            # Recent communications
            recent_posts = Post.objects.select_related('author').order_by('-created_at')[:10]
            recent_comments = Comment.objects.select_related('author', 'post').order_by('-created_at')[:10]

            communication_data = {
                'email_stats': email_stats,
                'notification_stats': notification_stats,
                'recent_posts': [
                    {
                        'id': post.id,
                        'title': post.title,
                        'author': post.author.username,
                        'created_at': post.created_at.isoformat(),
                        'views': getattr(post, 'views', 0),
                        'comments_count': post.comments.count()
                    } for post in recent_posts
                ],
                'recent_comments': [
                    {
                        'id': comment.id,
                        'content': comment.content[:100] + '...' if len(comment.content) > 100 else comment.content,
                        'author': comment.author.username,
                        'post_title': comment.post.title,
                        'created_at': comment.created_at.isoformat()
                    } for comment in recent_comments
                ],
                'timestamp': timezone.now().isoformat()
            }

            log_super_admin_action(request.user, 'communication_center_viewed')
            return Response(communication_data)

        except Exception as e:
            logger.error(f"Error fetching communication data: {str(e)}")
            return Response(
        except Exception as e:
            logger.error(f"Error fetching monitoring data: {str(e)}")
            return Response(
            except Exception as e:
                logger.error(f"Error fetching analytics data: {str(e)}")
                return Response(
            except Exception as e:
                logger.error(f"Error exporting analytics data: {str(e)}")
                return Response(
        except Exception as e:
            logger.error(f"Error fetching API management data: {str(e)}")
            return Response(
        except Exception as e:
            logger.error(f"Error creating API key: {str(e)}")
            return Response(
        except Exception as e:
            logger.error(f"Error fetching AI analytics: {str(e)}")
            return Response(
        except Exception as e:
            logger.error(f"Error fetching AI monitoring data: {str(e)}")
            return Response(
            except Exception as e:
                logger.error(f"Error fetching system configuration: {str(e)}")
                return Response(
            except Exception as e:
                logger.error(f"Error updating system configuration: {str(e)}")
                return Response(
class SuperAdminContentViewSet(SuperAdminAuditMixin, viewsets.ViewSet):
    """
    Super Admin Content Management ViewSet
    """
    permission_classes = [IsSuperAdminUser]

    @action(detail=False, methods=['get'])
    def posts_management(self, request):
        """Posts management data"""
        try:
            from forums.models import Post

            # Get posts with statistics
            posts = Post.objects.select_related('author').order_by('-created_at')[:50]

            # Posts statistics
            total_posts = Post.objects.count()
            published_posts = Post.objects.filter(is_published=True).count() if hasattr(Post, 'is_published') else total_posts
            draft_posts = total_posts - published_posts
            posts_today = Post.objects.filter(created_at__date=timezone.now().date()).count()

            posts_data = {
                'posts': [
                    {
                        'id': post.id,
                        'title': post.title,
                        'author': post.author.username,
                        'created_at': post.created_at.isoformat(),
                        'updated_at': post.updated_at.isoformat() if hasattr(post, 'updated_at') else post.created_at.isoformat(),
                        'views': getattr(post, 'views', 0),
                        'comments_count': post.comments.count(),
                        'likes_count': getattr(post, 'likes_count', 0),
                        'is_published': getattr(post, 'is_published', True),
                        'category': getattr(post, 'category', 'General')
                    } for post in posts
                ],
                'statistics': {
                    'total_posts': total_posts,
                    'published_posts': published_posts,
                    'draft_posts': draft_posts,
                    'posts_today': posts_today
                },
                'timestamp': timezone.now().isoformat()
            }

            log_super_admin_action(request.user, 'posts_management_viewed')
            return Response(posts_data)

        except Exception as e:
            logger.error(f"Error fetching posts management data: {str(e)}")
            return Response(
        except Exception as e:
            logger.error(f"Error fetching events management data: {str(e)}")
            return Response(
        except Exception as e:
            logger.error(f"Error fetching resources management data: {str(e)}")
            return Response(
        except Exception as e:
            logger.error(f"Error fetching moderation data: {str(e)}")
            return Response(
class SuperAdminUserViewSet(SuperAdminAuditMixin, viewsets.ViewSet):
    """
    Super Admin User Management ViewSet
    """
    permission_classes = [IsSuperAdminUser]

    @action(detail=False, methods=['get'])
    def users_management(self, request):
        """Users management data"""
        try:
            from django.contrib.auth.models import User
            from users.models import UserProfile, UserRoleAssignment

            # Get users with profiles and roles
            users = User.objects.select_related('profile').prefetch_related(
                'userroleassignment_set__role'
            ).order_by('-date_joined')[:100]

            # User statistics
            total_users = User.objects.count()
            active_users = User.objects.filter(is_active=True).count()
            staff_users = User.objects.filter(is_staff=True).count()
            superuser_count = User.objects.filter(is_superuser=True).count()
            users_today = User.objects.filter(date_joined__date=timezone.now().date()).count()

            users_data = {
                'users': [
                    {
                        'id': user.id,
                        'username': user.username,
                        'email': user.email,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'is_active': user.is_active,
                        'is_staff': user.is_staff,
                        'is_superuser': user.is_superuser,
                        'date_joined': user.date_joined.isoformat(),
                        'last_login': user.last_login.isoformat() if user.last_login else None,
                        'profile': {
                            'phone': getattr(user.profile, 'phone', '') if hasattr(user, 'profile') else '',
                            'bio': getattr(user.profile, 'bio', '') if hasattr(user, 'profile') else '',
                            'location': getattr(user.profile, 'location', '') if hasattr(user, 'profile') else '',
                        } if hasattr(user, 'profile') else None,
                        'roles': [
                            {
                                'name': assignment.role.name,
                                'display_name': assignment.role.display_name,
                                'assigned_at': assignment.assigned_at.isoformat()
                            } for assignment in user.userroleassignment_set.all()
                        ]
                    } for user in users
                ],
                'statistics': {
                    'total_users': total_users,
                    'active_users': active_users,
                    'staff_users': staff_users,
                    'superuser_count': superuser_count,
                    'users_today': users_today
                },
                'timestamp': timezone.now().isoformat()
            }

            log_super_admin_action(request.user, 'users_management_viewed')
            return Response(users_data)

        except Exception as e:
            logger.error(f"Error fetching users management data: {str(e)}")
            return Response(
        except User.DoesNotExist:
            return Response(
                {'error': 'User not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error updating user: {str(e)}")
            return Response(
        except User.DoesNotExist:
            return Response(
                {'error': 'User not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error deleting user: {str(e)}")
            return Response(
        except Exception as e:
            logger.error(f"Error fetching impersonation history: {str(e)}")
            return Response(
        except Exception as e:
            logger.error(f"Error fetching roles: {str(e)}")
            # Fallback to default roles if UserRole model doesn't exist
            default_roles = [
                {
                    'id': 'admin',
                    'name': 'Administrator',
                    'description': 'Full system access',
                    'permissions': ['all'],
                    'color': 'bg-red-500'
                },
                {
                    'id': 'moderator',
                    'name': 'Moderator',
                    'description': 'Content moderation and user management',
                    'permissions': ['moderate_content', 'manage_users', 'view_reports'],
                    'color': 'bg-blue-500'
                },
                {
                    'id': 'mentor',
                    'name': 'Mentor',
                    'description': 'Mentorship and guidance',
                    'permissions': ['mentor_access', 'view_mentees', 'create_sessions'],
                    'color': 'bg-purple-500'
                },
                {
                    'id': 'investor',
                    'name': 'Investor',
                    'description': 'Investment and funding access',
                    'permissions': ['view_pitches', 'fund_projects', 'investor_dashboard'],
                    'color': 'bg-green-500'
                },
                {
                    'id': 'entrepreneur',
                    'name': 'Entrepreneur',
                    'description': 'Standard user with business creation access',
                    'permissions': ['create_business', 'apply_funding', 'join_mentorship'],
                    'color': 'bg-orange-500'
                },
                {
                    'id': 'member',
                    'name': 'Member',
                    'description': 'Basic platform access',
                    'permissions': ['view_content', 'participate_forums'],
                    'color': 'bg-gray-500'
                }
            ]

            return Response({
class SuperAdminForumViewSet(SuperAdminAuditMixin, viewsets.ViewSet):
    """
    Super Admin Forum Management ViewSet
    """
    permission_classes = [IsSuperAdminUser]

    @action(detail=False, methods=['get'])
    def forum_moderation(self, request):
        """Forum moderation data"""
        try:
            from forums.models import Post, Comment

            # Get recent posts and comments for moderation
            recent_posts = Post.objects.select_related('author').order_by('-created_at')[:50]
            recent_comments = Comment.objects.select_related('author', 'post').order_by('-created_at')[:50]

            # Moderation statistics
            total_posts = Post.objects.count()
            total_comments = Comment.objects.count()
            flagged_posts = Post.objects.filter(is_flagged=True).count() if hasattr(Post, 'is_flagged') else 0
            flagged_comments = Comment.objects.filter(is_flagged=True).count() if hasattr(Comment, 'is_flagged') else 0

            forum_data = {
                'recent_posts': [
                    {
                        'id': post.id,
                        'title': post.title,
                        'author': post.author.username,
                        'created_at': post.created_at.isoformat(),
                        'comments_count': post.comments.count(),
                        'is_flagged': getattr(post, 'is_flagged', False),
                        'views': getattr(post, 'views', 0)
                    } for post in recent_posts
                ],
                'recent_comments': [
                    {
                        'id': comment.id,
                        'content': comment.content[:100] + '...' if len(comment.content) > 100 else comment.content,
                        'author': comment.author.username,
                        'post_title': comment.post.title,
                        'created_at': comment.created_at.isoformat(),
                        'is_flagged': getattr(comment, 'is_flagged', False)
                    } for comment in recent_comments
                ],
                'statistics': {
                    'total_posts': total_posts,
                    'total_comments': total_comments,
                    'flagged_posts': flagged_posts,
                    'flagged_comments': flagged_comments
                },
                'timestamp': timezone.now().isoformat()
            }

            log_super_admin_action(request.user, 'forum_moderation_viewed')
            return Response(forum_data)

        except Exception as e:
            logger.error(f"Error fetching forum moderation data: {str(e)}")
            return Response(
        except Exception as e:
            logger.error(f"Error fetching forum analytics: {str(e)}")
            return Response(
class SuperAdminIncubatorViewSet(SuperAdminAuditMixin, viewsets.ViewSet):
    """
    Super Admin Incubator Management ViewSet
    """
    permission_classes = [IsSuperAdminUser]

    @action(detail=False, methods=['get'])
    def business_ideas_management(self, request):
        """Business ideas management data"""
        try:
            from incubator.models import BusinessIdea

            # Get business ideas
            business_ideas = BusinessIdea.objects.select_related('user').order_by('-created_at')[:50]

            # Statistics
            total_ideas = BusinessIdea.objects.count()
            approved_ideas = BusinessIdea.objects.filter(status='approved').count() if hasattr(BusinessIdea, 'status') else 0
            pending_ideas = BusinessIdea.objects.filter(status='pending').count() if hasattr(BusinessIdea, 'status') else 0
            ideas_today = BusinessIdea.objects.filter(created_at__date=timezone.now().date()).count()

            ideas_data = {
                'business_ideas': [
                    {
                        'id': idea.id,
                        'title': idea.title,
                        'description': idea.description[:200] + '...' if len(idea.description) > 200 else idea.description,
                        'user': idea.user.username,
                        'created_at': idea.created_at.isoformat(),
                        'status': getattr(idea, 'status', 'pending'),
                        'category': getattr(idea, 'category', 'General'),
                        'votes': getattr(idea, 'votes', 0)
                    } for idea in business_ideas
                ],
                'statistics': {
                    'total_ideas': total_ideas,
                    'approved_ideas': approved_ideas,
                    'pending_ideas': pending_ideas,
                    'ideas_today': ideas_today
                },
                'timestamp': timezone.now().isoformat()
            }

            log_super_admin_action(request.user, 'business_ideas_management_viewed')
            return Response(ideas_data)

        except Exception as e:
            logger.error(f"Error fetching business ideas data: {str(e)}")
            return Response(
        except Exception as e:
            logger.error(f"Error fetching business plans data: {str(e)}")
            return Response(
        except Exception as e:
            logger.error(f"Error fetching funding data: {str(e)}")
            return Response(
class SuperAdminAIViewSet(SuperAdminAuditMixin, viewsets.ViewSet):
    """
    Super Admin AI Management ViewSet
    """
    permission_classes = [IsSuperAdminUser]

    @action(detail=False, methods=['post'])
    def generate_business_plan_section(self, request):
        """Generate business plan section content using AI"""
        try:
            section_data = request.data

            # Use AI service to generate content
            from core.ai_service import generate_section_content

            content = generate_section_content(
                section_name=section_data.get('section_name', 'Business Plan Section'),
                business_idea=section_data.get('business_idea', ''),
                context=section_data.get('context', {}),
                language=section_data.get('language', 'en'),
                user_id=request.user.id if request.user.is_authenticated else None
            )

            return Response({
                'success': True,
                'content': content.get('response', ''),
                'section_name': section_data.get('section_name')
            })

        except Exception as e:
            return Response({
        except Exception as e:
            return Response({
        except Exception as e:
            logger.error(f"Error generating financial projections: {str(e)}")
            return Response(
        except Exception as e:
            return Response({
        except Exception as e:
            logger.error(f"Error generating SWOT analysis: {str(e)}")
            return Response(
        except Exception as e:
            logger.error(f"Error generating autonomous analysis: {str(e)}")
            return Response(
        except Exception as e:
            logger.error(f"Error generating predictive metrics: {str(e)}")
            return Response(
        except Exception as e:
            logger.error(f"Error fetching analysis dashboard: {str(e)}")
            return Response(
            except ImportError:
                ai_models_available = False

            # Get time range
            time_range = request.GET.get('range', '30d')
            days = int(time_range.replace('d', ''))
            start_date = timezone.now() - timedelta(days=days)

            # Business Ideas metrics
            total_ideas = BusinessIdea.objects.count()
            active_ideas = BusinessIdea.objects.filter(status='active').count() if hasattr(BusinessIdea, 'status') else total_ideas
            funded_ideas = BusinessIdea.objects.filter(status='funded').count() if hasattr(BusinessIdea, 'status') else 0
            ideas_growth = ((BusinessIdea.objects.filter(created_at__gte=start_date).count() / max(total_ideas, 1)) * 100)

            # AI Insights metrics
            if ai_models_available:
                ai_recommendations = 0  # Fallback count
                ai_interactions = 0  # Fallback count
            else:
                ai_recommendations = 47  # Default value
                ai_interactions = 156  # Default value

            implementation_rate = 73.2  # Calculate based on actual data
            avg_relevance_score = 8.4  # Calculate based on actual data

            # Mentorship metrics (placeholder - implement when mentorship models exist)
            active_mentorships = 2
            sessions_completed = 15
            avg_rating = 4.8

            # Community metrics (placeholder - implement when forum models exist)
            forum_posts = 23
            reputation = 1250
            helpful_answers = 18
            network_size = 89

            # Achievements (placeholder - implement when achievement system exists)
            achievements = [
                {
                    'id': '1',
                    'title': 'First Business Idea',
                    'description': 'Created your first business idea',
                    'earnedAt': timezone.now().isoformat(),
                    'icon': 'lightbulb'
                },
                {
                    'id': '2',
                    'title': 'AI Collaborator',
                    'description': 'Used AI recommendations 50+ times',
                    'earnedAt': timezone.now().isoformat(),
                    'icon': 'brain'
                }
            ]

            metrics_data = {
                'metrics': {
                    'businessIdeas': {
                        'total': total_ideas,
                        'active': active_ideas,
                        'funded': funded_ideas,
                        'growth': round(ideas_growth, 1)
                    },
                    'aiInsights': {
                        'recommendationsGenerated': ai_recommendations,
                        'implementationRate': implementation_rate,
                        'avgRelevanceScore': avg_relevance_score,
                        'chatInteractions': ai_interactions
                    },
                    'mentorship': {
                        'activeMentorships': active_mentorships,
                        'sessionsCompleted': sessions_completed,
                        'avgRating': avg_rating,
                        'nextSession': (timezone.now() + timedelta(days=7)).isoformat()
                    },
                    'community': {
                        'forumPosts': forum_posts,
                        'reputation': reputation,
                        'helpfulAnswers': helpful_answers,
                        'networkSize': network_size
                    },
                    'achievements': achievements
                },
                'timestamp': timezone.now().isoformat()
            }

            log_super_admin_action(request.user, 'enhanced_metrics_viewed')
            return Response(metrics_data)

        except Exception as e:
            logger.error(f"Error fetching enhanced metrics: {str(e)}")
            return Response(
        except Exception as e:
            logger.error(f"Error fetching performance data: {str(e)}")
            return Response(
        except Exception as e:
            logger.error(f"Error fetching capabilities: {str(e)}")
            return Response(
        except Exception as e:
            logger.error(f"Error fetching communication data: {str(e)}")
            return Response(
        except Exception as e:
            logger.error(f"Error executing operation {operation_id}: {str(e)}")
            return Response(