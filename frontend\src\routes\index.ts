// Export consolidated route configurations
export {
  publicRoutes,
  userRoutes,
  moderatorRoutes,
  mentorRoutes,
  investorRoutes,
  adminRoutes,
  superAdminRoutes,
  allRoutes
} from './consolidatedRoutes';

// Export route configuration types and utilities
export * from './routeConfig';

// Legacy exports for backward compatibility (deprecated)
export { default as legacyPublicRoutes } from './publicRoutes';
export { default as legacyUserRoutes } from './userRoutes';
export { default as legacyModeratorRoutes } from './moderatorRoutes';
export { default as legacyMentorRoutes } from './mentorRoutes';
export { default as legacyInvestorRoutes } from './investorRoutes';
export { default as legacyAdminRoutes } from './adminRoutes';
export { default as legacySuperAdminRoutes } from './superAdminRoutes';
