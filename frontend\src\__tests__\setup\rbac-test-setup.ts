/**
 * RBAC TEST SETUP
 * Configuration and utilities for RBAC system testing
 */

import '@testing-library/jest-dom';

// Mock localStorage for tests
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock as any;

// Mock sessionStorage for tests
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.sessionStorage = sessionStorageMock as any;

// Mock window.location for navigation tests
delete (window as any).location;
window.location = {
  ...window.location,
  href: 'http://localhost:3000',
  pathname: '/',
  search: '',
  hash: '',
  assign: jest.fn(),
  replace: jest.fn(),
  reload: jest.fn(),
};

// Mock console methods to reduce test noise
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Test utilities for RBAC testing
export const createTestUser = (role: string, overrides = {}) => ({
  id: Math.floor(Math.random() * 1000),
  username: `${role}_user`,
  email: `${role}@test.com`,
  first_name: `Test`,
  last_name: `${role}`,
  is_superuser: role === 'super_admin',
  is_staff: ['super_admin', 'admin'].includes(role),
  user_role: role,
  role_permissions: getRolePermissions(role),
  profile: {
    primary_role: { name: role },
    active_roles: role !== 'user' ? [{ name: role }] : [],
    bio: `Test ${role} user`,
    avatar: null
  },
  date_joined: new Date().toISOString(),
  last_login: new Date().toISOString(),
  is_active: true,
  ...overrides
});

export const getRolePermissions = (role: string): string[] => {
  const permissionMap: Record<string, string[]> = {
    'super_admin': ['read', 'write', 'moderate', 'admin', 'super_admin'],
    'admin': ['read', 'write', 'moderate', 'admin'],
    'moderator': ['read', 'write', 'moderate'],
    'mentor': ['read', 'write'],
    'investor': ['read', 'write'],
    'user': ['read']
  };
  return permissionMap[role] || ['read'];
};

export const createAuthState = (user: any = null, isAuthenticated = false, isLoading = false) => ({
  user,
  isAuthenticated,
  isLoading,
  error: null,
  token: isAuthenticated ? 'mock-token' : null
});

// Mock API responses for testing
export const mockAPIResponses = {
  businessPlans: [
    {
      id: 1,
      title: 'Test Business Plan',
      business_idea_title: 'Test Business Idea',
      status: 'draft',
      completion_percentage: 50,
      template_name: 'Test Template',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  ],
  templates: [
    {
      id: 1,
      name: 'Test Template',
      description: 'Test template description',
      category: 'technology',
      is_public: true,
      usage_count: 10,
      rating: 4.5,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  ],
  analytics: {
    overview: {
      totalUsers: 100,
      activeUsers: 80,
      totalBusinessPlans: 50,
      completedPlans: 20
    },
    chartData: []
  }
};

// Test constants
export const TEST_ROLES = ['super_admin', 'admin', 'moderator', 'mentor', 'investor', 'user'];

export const ROLE_PERMISSIONS = {
  'super_admin': ['read', 'write', 'moderate', 'admin', 'super_admin'],
  'admin': ['read', 'write', 'moderate', 'admin'],
  'moderator': ['read', 'write', 'moderate'],
  'mentor': ['read', 'write'],
  'investor': ['read', 'write'],
  'user': ['read']
};

export const DASHBOARD_ROUTES = {
  'super_admin': '/super_admin',
  'admin': '/admin',
  'moderator': '/dashboard/moderation',
  'mentor': '/dashboard/mentorship',
  'investor': '/dashboard/investments',
  'user': '/dashboard'
};

// Navigation test data
export const CRITICAL_NAV_ITEMS = {
  'super_admin': [
    'super-admin-dashboard',
    'system-monitoring',
    'ai-system-management'
  ],
  'admin': [
    'user-management',
    'admin-analytics',
    'system-settings'
  ],
  'moderator': [
    'content-moderation',
    'user-moderation'
  ],
  'mentor': [
    'mentorship-sessions',
    'mentees'
  ],
  'investor': [
    'investment-opportunities',
    'portfolio'
  ],
  'user': [
    'dashboard',
    'business-ideas',
    'business-plans',
    'profile',
    'settings'
  ]
};

// Security test scenarios
export const SECURITY_TEST_SCENARIOS = [
  {
    name: 'Privilege Escalation Prevention',
    userRole: 'user',
    attemptedAccess: ['admin', 'super_admin'],
    shouldFail: true
  },
  {
    name: 'Role Boundary Enforcement',
    userRole: 'mentor',
    attemptedAccess: ['investment-opportunities', 'portfolio'],
    shouldFail: true
  },
  {
    name: 'Cross-Role Access Prevention',
    userRole: 'investor',
    attemptedAccess: ['mentorship-sessions', 'content-moderation'],
    shouldFail: true
  },
  {
    name: 'Admin Boundary Enforcement',
    userRole: 'admin',
    attemptedAccess: ['super-admin-dashboard', 'system-monitoring'],
    shouldFail: true
  }
];

// Helper functions for testing
export const expectRoleAccess = (userRole: string, allowedItems: string[], deniedItems: string[]) => {
  return {
    userRole,
    allowedItems,
    deniedItems,
    validate: (accessFunction: (role: string, item: string) => boolean) => {
      allowedItems.forEach(item => {
        expect(accessFunction(userRole, item)).toBe(true);
      });
      deniedItems.forEach(item => {
        expect(accessFunction(userRole, item)).toBe(false);
      });
    }
  };
};

export const expectPermissionLevel = (userRole: string, expectedPermissions: string[], deniedPermissions: string[]) => {
  return {
    userRole,
    expectedPermissions,
    deniedPermissions,
    validate: (permissionFunction: (user: any, permission: string) => boolean) => {
      const user = createTestUser(userRole);
      expectedPermissions.forEach(permission => {
        expect(permissionFunction(user, permission)).toBe(true);
      });
      deniedPermissions.forEach(permission => {
        expect(permissionFunction(user, permission)).toBe(false);
      });
    }
  };
};

// Cleanup function for tests
export const cleanupTests = () => {
  jest.clearAllMocks();
  localStorageMock.clear();
  sessionStorageMock.clear();
};

// Export all test utilities
export default {
  createTestUser,
  getRolePermissions,
  createAuthState,
  mockAPIResponses,
  TEST_ROLES,
  ROLE_PERMISSIONS,
  DASHBOARD_ROUTES,
  CRITICAL_NAV_ITEMS,
  SECURITY_TEST_SCENARIOS,
  expectRoleAccess,
  expectPermissionLevel,
  cleanupTests
};
