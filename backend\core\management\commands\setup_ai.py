"""
Django management command to set up and validate AI configuration
"""

from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth.models import User
from core.ai_config_helper import AIConfigurationHelper
from core.ai_config import get_gemini_config
import os


class Command(BaseCommand):
    help = 'Set up and validate AI configuration for Yasmeen AI'

    def add_arguments(self, parser):
        parser.add_argument(
            '--check',
            action='store_true',
            help='Check current AI configuration status',
        )
        parser.add_argument(
            '--setup',
            type=str,
            help='Set up AI configuration with provided API key',
        )
        parser.add_argument(
            '--validate',
            action='store_true',
            help='Validate current AI configuration',
        )
        parser.add_argument(
            '--instructions',
            action='store_true',
            help='Show setup instructions',
        )

    def handle(self, *args, **options):
        if options['instructions']:
            self.show_instructions()
        elif options['check']:
            self.check_configuration()
        elif options['setup']:
            self.setup_configuration(options['setup'])
        elif options['validate']:
            self.validate_configuration()
        else:
            self.stdout.write(
                self.style.WARNING(
                    'Please specify an action: --check, --setup, --validate, or --instructions'
                )
            )
            self.show_help()

    def show_instructions(self):
        """Show setup instructions"""
        instructions = AIConfigurationHelper.get_setup_instructions()
        
        self.stdout.write(
            self.style.SUCCESS(f"\n🤖 {instructions['title']}\n")
        )
        
        for step in instructions['steps']:
            self.stdout.write(
                self.style.HTTP_INFO(f"Step {step['step']}: {step['title']}")
            )
            self.stdout.write(f"  {step['description']}")
            
            if 'url' in step:
                self.stdout.write(f"  URL: {step['url']}")
            
            if 'instructions' in step:
                for instruction in step['instructions']:
                    self.stdout.write(f"    • {instruction}")
            
            if 'options' in step:
                for option in step['options']:
                    self.stdout.write(f"  {option['method']}:")
                    for instruction in option['instructions']:
                        self.stdout.write(f"    • {instruction}")
            
            self.stdout.write("")
        
        self.stdout.write(self.style.WARNING("Troubleshooting:"))
        for issue in instructions['troubleshooting']:
            self.stdout.write(f"  Issue: {issue['issue']}")
            for solution in issue['solutions']:
                self.stdout.write(f"    • {solution}")
            self.stdout.write("")

    def check_configuration(self):
        """Check current configuration status"""
        self.stdout.write("🔍 Checking AI configuration status...\n")
        
        status = AIConfigurationHelper.get_configuration_status()
        
        # Display status
        self.stdout.write(f"Database config exists: {self._format_bool(status['database_config_exists'])}")
        self.stdout.write(f"Environment config exists: {self._format_bool(status['environment_config_exists'])}")
        self.stdout.write(f"API key source: {status['api_key_source'] or 'None'}")
        
        # Display recommendations
        if status['recommendations']:
            self.stdout.write("\n📋 Recommendations:")
            for rec in status['recommendations']:
                style = self.style.ERROR if rec['type'] == 'error' else self.style.WARNING
                self.stdout.write(style(f"  {rec['message']}"))
                self.stdout.write(f"    Action: {rec['action']}")
        
        # Check actual AI service status
        self.stdout.write("\n🤖 AI Service Status:")
        try:
            config = get_gemini_config()
            ai_status = config.get_status()
            
            self.stdout.write(f"  Available: {self._format_bool(ai_status['available'])}")
            self.stdout.write(f"  Model: {ai_status.get('model', 'Unknown')}")
            self.stdout.write(f"  Last test: {ai_status.get('last_test_time', 'Never')}")
            
            if ai_status.get('error'):
                self.stdout.write(self.style.ERROR(f"  Error: {ai_status['error']}"))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"  Error checking AI service: {e}"))

    def setup_configuration(self, api_key):
        """Set up AI configuration with provided API key"""
        self.stdout.write("🔧 Setting up AI configuration...\n")
        
        # Get admin user for configuration
        admin_user = User.objects.filter(is_superuser=True).first()
        user_id = admin_user.id if admin_user else None
        
        # Set up database configuration
        result = AIConfigurationHelper.setup_database_configuration(api_key, user_id)
        
        if result['success']:
            self.stdout.write(
                self.style.SUCCESS(f"✅ {result['message']}")
            )
            
            if result['created']:
                self.stdout.write("  New configuration created")
            else:
                self.stdout.write("  Existing configuration updated")
            
            # Validate the configuration
            self.stdout.write("\n🧪 Validating configuration...")
            self.validate_configuration()
            
        else:
            self.stdout.write(
                self.style.ERROR(f"❌ Setup failed: {result['error']}")
            )
            
            if 'validation' in result:
                validation = result['validation']
                self.stdout.write(f"  Validation error: {validation['error']}")
                self.stdout.write(f"  Suggestion: {validation['suggestion']}")

    def validate_configuration(self):
        """Validate current AI configuration"""
        self.stdout.write("🧪 Validating AI configuration...\n")
        
        try:
            # Get current configuration
            config = get_gemini_config()
            
            if not config.api_key:
                self.stdout.write(
                    self.style.ERROR("❌ No API key configured")
                )
                return
            
            # Validate the API key
            validation = AIConfigurationHelper.validate_gemini_api_key(config.api_key)
            
            if validation['valid']:
                self.stdout.write(
                    self.style.SUCCESS(f"✅ {validation['message']}")
                )
                if 'test_response' in validation:
                    self.stdout.write(f"  Test response: {validation['test_response']}")
            else:
                self.stdout.write(
                    self.style.ERROR(f"❌ {validation['error']}")
                )
                self.stdout.write(f"  Suggestion: {validation['suggestion']}")
                
                if 'details' in validation:
                    self.stdout.write(f"  Details: {validation['details']}")
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Validation failed: {e}")
            )

    def show_help(self):
        """Show help information"""
        self.stdout.write("\nUsage examples:")
        self.stdout.write("  python manage.py setup_ai --instructions")
        self.stdout.write("  python manage.py setup_ai --check")
        self.stdout.write("  python manage.py setup_ai --setup YOUR_API_KEY_HERE")
        self.stdout.write("  python manage.py setup_ai --validate")

    def _format_bool(self, value):
        """Format boolean value with colors"""
        if value:
            return self.style.SUCCESS("✅ Yes")
        else:
            return self.style.ERROR("❌ No")
