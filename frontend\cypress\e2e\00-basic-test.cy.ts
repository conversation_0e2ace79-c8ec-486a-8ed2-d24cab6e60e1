describe('Basic Application Test', () => {
  it('should load the application homepage', () => {
    cy.visit('/')
    // Check that the page loads successfully
    cy.get('body').should('be.visible')
    // Check for React root element
    cy.get('#root').should('exist')
  })

  it('should have a working title', () => {
    cy.visit('/')
    cy.title().should('not.be.empty')
  })

  it('should respond to network requests', () => {
    cy.visit('/')
    // Just verify the page loads without network errors
    cy.url().should('eq', Cypress.config().baseUrl + '/')
  })
})
