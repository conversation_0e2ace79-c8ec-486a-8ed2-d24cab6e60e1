/**
 * Moderation Hooks
 * Custom hooks for moderation functionality
 */

import { useState, useEffect, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { moderationApi, ContentItem, UserAccount, ModerationAction } from '../services/moderationApi';

/**
 * Hook for content moderation
 */
export function useContentModeration() {
  const queryClient = useQueryClient();

  // Fetch content items
  const {
    data: contentItems = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['moderation', 'content'],
    queryFn: moderationApi.content.getContentItems,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Update content status mutation
  const updateStatusMutation = useMutation({
    mutationFn: ({ id, status, reason }: { id: string; status: ContentItem['status']; reason?: string }) =>
      moderationApi.content.updateContentStatus(id, status, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['moderation', 'content'] });
    },
  });

  // Delete content mutation
  const deleteContentMutation = useMutation({
    mutationFn: ({ id, reason }: { id: string; reason: string }) =>
      moderationApi.content.deleteContent(id, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['moderation', 'content'] });
    },
  });

  return {
    contentItems,
    isLoading,
    error,
    refetch,
    updateStatus: updateStatusMutation.mutate,
    deleteContent: deleteContentMutation.mutate,
    isUpdating: updateStatusMutation.isPending || deleteContentMutation.isPending,
  };
}

/**
 * Hook for user moderation
 */
export function useUserModeration() {
  const queryClient = useQueryClient();

  // Fetch users
  const {
    data: users = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['moderation', 'users'],
    queryFn: moderationApi.users.getUsers,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Update user status mutation
  const updateUserStatusMutation = useMutation({
    mutationFn: ({ id, status, reason }: { id: string; status: UserAccount['status']; reason?: string }) =>
      moderationApi.users.updateUserStatus(id, status, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['moderation', 'users'] });
    },
  });

  // Apply moderation action mutation
  const applyActionMutation = useMutation({
    mutationFn: (action: Omit<ModerationAction, 'id' | 'timestamp'>) =>
      moderationApi.users.applyModerationAction(action),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['moderation', 'users'] });
      queryClient.invalidateQueries({ queryKey: ['moderation', 'history'] });
    },
  });

  return {
    users,
    isLoading,
    error,
    refetch,
    updateUserStatus: updateUserStatusMutation.mutate,
    applyAction: applyActionMutation.mutate,
    isUpdating: updateUserStatusMutation.isPending || applyActionMutation.isPending,
  };
}

/**
 * Hook for moderation history
 */
export function useModerationHistory(userId?: string) {
  const {
    data: history = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['moderation', 'history', userId],
    queryFn: () => moderationApi.users.getModerationHistory(userId),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  return {
    history,
    isLoading,
    error,
    refetch,
  };
}

/**
 * Hook for forum moderation
 */
export function useForumModeration() {
  const queryClient = useQueryClient();

  // Fetch forum threads
  const {
    data: threads = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['moderation', 'forum'],
    queryFn: moderationApi.forum.getForumThreads,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Moderate thread mutation
  const moderateThreadMutation = useMutation({
    mutationFn: ({ threadId, action, reason }: { threadId: string; action: string; reason?: string }) =>
      moderationApi.forum.moderateThread(threadId, action, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['moderation', 'forum'] });
    },
  });

  return {
    threads,
    isLoading,
    error,
    refetch,
    moderateThread: moderateThreadMutation.mutate,
    isModerating: moderateThreadMutation.isPending,
  };
}

/**
 * Hook for reports management
 */
export function useReports() {
  const queryClient = useQueryClient();

  // Fetch reports
  const {
    data: reports = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['moderation', 'reports'],
    queryFn: moderationApi.reports.getReports,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Update report status mutation
  const updateReportMutation = useMutation({
    mutationFn: ({ reportId, status, resolution }: { reportId: string; status: string; resolution?: string }) =>
      moderationApi.reports.updateReportStatus(reportId, status, resolution),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['moderation', 'reports'] });
    },
  });

  return {
    reports,
    isLoading,
    error,
    refetch,
    updateReport: updateReportMutation.mutate,
    isUpdating: updateReportMutation.isPending,
  };
}

/**
 * Hook for moderation statistics
 */
export function useModerationStats() {
  const [stats, setStats] = useState({
    totalContent: 0,
    pendingContent: 0,
    flaggedContent: 0,
    totalUsers: 0,
    suspendedUsers: 0,
    totalReports: 0,
    pendingReports: 0,
  });

  const { data: contentItems } = useQuery({
    queryKey: ['moderation', 'content'],
    queryFn: moderationApi.content.getContentItems,
  });

  const { data: users } = useQuery({
    queryKey: ['moderation', 'users'],
    queryFn: moderationApi.users.getUsers,
  });

  const { data: reports } = useQuery({
    queryKey: ['moderation', 'reports'],
    queryFn: moderationApi.reports.getReports,
  });

  useEffect(() => {
    if (contentItems && users && reports) {
      setStats({
        totalContent: contentItems.length,
        pendingContent: contentItems.filter(item => item.status === 'pending').length,
        flaggedContent: contentItems.filter(item => item.status === 'flagged').length,
        totalUsers: users.length,
        suspendedUsers: users.filter(user => user.status === 'suspended').length,
        totalReports: reports.length,
        pendingReports: reports.filter(report => report.status === 'pending').length,
      });
    }
  }, [contentItems, users, reports]);

  return stats;
}

// All hooks are already exported individually above
