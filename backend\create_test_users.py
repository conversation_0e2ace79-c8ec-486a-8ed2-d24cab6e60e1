#!/usr/bin/env python
"""
Create test users with different roles for end-to-end testing
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from django.contrib.auth import get_user_model
from users.models import UserProfile, UserRole, UserRoleAssignment

User = get_user_model()

def create_test_users():
    """Create test users with different roles"""
    
    print("🔧 Creating test users with different roles...")
    
    # Test users data
    test_users = [
        {
            'username': 'testuser',
            'email': '<EMAIL>',
            'password': 'testpass123',
            'first_name': 'Test',
            'last_name': 'User',
            'role': 'user',
            'is_staff': False,
            'is_superuser': False
        },
        {
            'username': 'testmentor',
            'email': '<EMAIL>',
            'password': 'testpass123',
            'first_name': 'Test',
            'last_name': 'Mentor',
            'role': 'mentor',
            'is_staff': False,
            'is_superuser': False
        },
        {
            'username': 'testinvestor',
            'email': '<EMAIL>',
            'password': 'testpass123',
            'first_name': 'Test',
            'last_name': 'Investor',
            'role': 'investor',
            'is_staff': False,
            'is_superuser': False
        },
        {
            'username': 'testmoderator',
            'email': '<EMAIL>',
            'password': 'testpass123',
            'first_name': 'Test',
            'last_name': 'Moderator',
            'role': 'moderator',
            'is_staff': False,
            'is_superuser': False
        },
        {
            'username': 'testadmin',
            'email': '<EMAIL>',
            'password': 'testpass123',
            'first_name': 'Test',
            'last_name': 'Admin',
            'role': 'admin',
            'is_staff': True,
            'is_superuser': False
        },
        {
            'username': 'testsuperadmin',
            'email': '<EMAIL>',
            'password': 'testpass123',
            'first_name': 'Test',
            'last_name': 'SuperAdmin',
            'role': 'super_admin',
            'is_staff': True,
            'is_superuser': True
        }
    ]
    
    created_users = []
    
    for user_data in test_users:
        username = user_data['username']
        
        # Check if user already exists
        if User.objects.filter(username=username).exists():
            print(f"⚠️  User {username} already exists, skipping...")
            user = User.objects.get(username=username)
        else:
            # Create user
            user = User.objects.create_user(
                username=user_data['username'],
                email=user_data['email'],
                password=user_data['password'],
                first_name=user_data['first_name'],
                last_name=user_data['last_name'],
                is_staff=user_data['is_staff'],
                is_superuser=user_data['is_superuser']
            )
            print(f"✅ Created user: {username}")
        
        # Set user role if it has the field
        if hasattr(user, 'user_role'):
            user.user_role = user_data['role']
            user.save()
            print(f"   Set user_role to: {user_data['role']}")
        
        # Create or update user profile
        profile, created = UserProfile.objects.get_or_create(user=user)
        if created:
            print(f"   Created profile for {username}")
        
        # Try to assign role through UserRole system if it exists
        try:
            role_obj, _ = UserRole.objects.get_or_create(
                name=user_data['role'],
                defaults={
                    'display_name': user_data['role'].replace('_', ' ').title(),
                    'permission_level': user_data['role']
                }
            )
            
            assignment, created = UserRoleAssignment.objects.get_or_create(
                user_profile=profile,
                role=role_obj,
                defaults={
                    'is_active': True,
                    'is_approved': True
                }
            )
            
            if created:
                print(f"   Assigned role {user_data['role']} to {username}")
            
        except Exception as e:
            print(f"   Note: Could not assign role through UserRole system: {e}")
        
        created_users.append({
            'username': username,
            'email': user_data['email'],
            'password': user_data['password'],
            'role': user_data['role']
        })
    
    return created_users

def list_users():
    """List all users and their roles"""
    print("\n📋 Current users in database:")
    print("-" * 60)
    
    for user in User.objects.all():
        role = getattr(user, 'user_role', 'N/A')
        print(f"Username: {user.username}")
        print(f"Email: {user.email}")
        print(f"Role: {role}")
        print(f"Staff: {user.is_staff}")
        print(f"Superuser: {user.is_superuser}")
        print("-" * 30)

if __name__ == "__main__":
    print("🧪 TEST USER CREATION SCRIPT")
    print("=" * 50)
    
    # List existing users
    list_users()
    
    # Create test users
    created_users = create_test_users()
    
    # List users again
    list_users()
    
    print("\n🎯 TEST USERS READY FOR E2E TESTING:")
    print("=" * 50)
    for user in created_users:
        print(f"Username: {user['username']}")
        print(f"Password: {user['password']}")
        print(f"Role: {user['role']}")
        print("-" * 30)
    
    print("\n✅ Test users created successfully!")
    print("You can now use these credentials to test role-based authentication.")
