import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { 
  BarChart3, 
  Users, 
  Activity, 
  TrendingUp, 
  Calendar,
  RefreshCw,
  Download,
  Eye,
  MessageSquare,
  FileText,
  Target
} from 'lucide-react';

interface AnalyticsData {
  totalUsers: number;
  activeUsers: number;
  totalPosts: number;
  totalEvents: number;
  userGrowth: number;
  engagementRate: number;
  monthlyStats: Array<{
    month: string;
    users: number;
    posts: number;
    events: number;
  }>;
}

const AdminAnalyticsPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<AnalyticsData>({
    totalUsers: 1247,
    activeUsers: 89,
    totalPosts: 3456,
    totalEvents: 12,
    userGrowth: 15.3,
    engagementRate: 68.5,
    monthlyStats: [
      { month: 'Jan', users: 120, posts: 450, events: 2 },
      { month: 'Feb', users: 145, posts: 520, events: 3 },
      { month: 'Mar', users: 180, posts: 680, events: 4 },
      { month: 'Apr', users: 210, posts: 750, events: 3 },
      { month: 'May', users: 250, posts: 890, events: 5 },
      { month: 'Jun', users: 280, posts: 950, events: 4 }
    ]
  });

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ReactNode;
    change?: number;
    suffix?: string;
  }> = ({ title, value, icon, change, suffix = '' }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {value}{suffix}
          </p>
          {change !== undefined && (
            <p className={`text-sm ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {change >= 0 ? '+' : ''}{change}% from last month
            </p>
          )}
        </div>
        <div className="text-blue-500">
          {icon}
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex items-center space-x-2">
          <RefreshCw className="w-6 h-6 animate-spin text-blue-500" />
          <span className="text-gray-600">Loading analytics...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Analytics Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Monitor platform performance and user engagement
          </p>
        </div>
        <div className="flex space-x-3">
          <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </button>
          <button className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
            <Download className="w-4 h-4 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Users"
          value={data.totalUsers.toLocaleString()}
          icon={<Users className="w-8 h-8" />}
          change={data.userGrowth}
        />
        <StatCard
          title="Active Users"
          value={data.activeUsers}
          icon={<Activity className="w-8 h-8" />}
          change={12.5}
        />
        <StatCard
          title="Total Posts"
          value={data.totalPosts.toLocaleString()}
          icon={<MessageSquare className="w-8 h-8" />}
          change={8.2}
        />
        <StatCard
          title="Events"
          value={data.totalEvents}
          icon={<Calendar className="w-8 h-8" />}
          change={-2.1}
        />
      </div>

      {/* Engagement Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            User Engagement
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-600 dark:text-gray-400">Engagement Rate</span>
              <span className="font-semibold text-green-600">{data.engagementRate}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-green-600 h-2 rounded-full" 
                style={{ width: `${data.engagementRate}%` }}
              ></div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Growth Trends
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-gray-600 dark:text-gray-400">User Growth</span>
              <span className="text-green-600 font-semibold">+{data.userGrowth}%</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600 dark:text-gray-400">Content Growth</span>
              <span className="text-blue-600 font-semibold">+8.2%</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600 dark:text-gray-400">Event Participation</span>
              <span className="text-purple-600 font-semibold">+5.7%</span>
            </div>
          </div>
        </div>
      </div>

      {/* Monthly Statistics Chart */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Monthly Statistics
        </h3>
        <div className="grid grid-cols-6 gap-4">
          {data.monthlyStats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">{stat.month}</div>
              <div className="space-y-1">
                <div className="text-xs text-blue-600">{stat.users} users</div>
                <div className="text-xs text-green-600">{stat.posts} posts</div>
                <div className="text-xs text-purple-600">{stat.events} events</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Quick Actions
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="flex items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors">
            <Eye className="w-5 h-5 text-blue-600 mr-3" />
            <span className="text-blue-600 font-medium">View Detailed Reports</span>
          </button>
          <button className="flex items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors">
            <Target className="w-5 h-5 text-green-600 mr-3" />
            <span className="text-green-600 font-medium">Set Performance Goals</span>
          </button>
          <button className="flex items-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors">
            <FileText className="w-5 h-5 text-purple-600 mr-3" />
            <span className="text-purple-600 font-medium">Generate Report</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default AdminAnalyticsPage;
