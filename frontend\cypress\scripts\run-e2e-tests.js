#!/usr/bin/env node

/**
 * Comprehensive E2E Test Runner for Cypress
 * 
 * This script provides a professional-grade test execution environment
 * with proper reporting, error handling, and CI/CD integration.
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const chalk = require('chalk');

// Configuration
const config = {
  baseUrl: process.env.CYPRESS_BASE_URL || 'http://localhost:3000',
  apiUrl: process.env.CYPRESS_API_URL || 'http://localhost:8000',
  browser: process.env.CYPRESS_BROWSER || 'chrome',
  headless: process.env.CYPRESS_HEADLESS !== 'false',
  record: process.env.CYPRESS_RECORD === 'true',
  parallel: process.env.CYPRESS_PARALLEL === 'true',
  retries: parseInt(process.env.CYPRESS_RETRIES) || 2,
  timeout: parseInt(process.env.CYPRESS_TIMEOUT) || 60000,
  reportDir: path.join(__dirname, '../../cypress-reports'),
  screenshotDir: path.join(__dirname, '../../cypress/screenshots'),
  videoDir: path.join(__dirname, '../../cypress/videos')
};

// Test suites configuration
const testSuites = {
  smoke: {
    name: 'Smoke Tests',
    specs: [
      'cypress/e2e/01-authentication.cy.ts',
      'cypress/e2e/02-role-based-access.cy.ts'
    ],
    description: 'Quick smoke tests for critical functionality'
  },
  full: {
    name: 'Full E2E Suite',
    specs: [
      'cypress/e2e/01-authentication.cy.ts',
      'cypress/e2e/02-role-based-access.cy.ts',
      'cypress/e2e/03-business-plans-crud.cy.ts',
      'cypress/e2e/04-sidebar-navigation.cy.ts',
      'cypress/e2e/05-api-integration.cy.ts',
      'cypress/e2e/06-accessibility-performance.cy.ts'
    ],
    description: 'Complete end-to-end test suite'
  },
  accessibility: {
    name: 'Accessibility Tests',
    specs: ['cypress/e2e/06-accessibility-performance.cy.ts'],
    description: 'Accessibility and performance validation'
  },
  api: {
    name: 'API Integration Tests',
    specs: ['cypress/e2e/05-api-integration.cy.ts'],
    description: 'API endpoint and integration testing'
  },
  ui: {
    name: 'UI Component Tests',
    specs: [
      'cypress/e2e/03-business-plans-crud.cy.ts',
      'cypress/e2e/04-sidebar-navigation.cy.ts'
    ],
    description: 'User interface and interaction testing'
  }
};

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const colors = {
    info: chalk.blue,
    success: chalk.green,
    warning: chalk.yellow,
    error: chalk.red
  };
  
  console.log(`${chalk.gray(timestamp)} ${colors[type](`[${type.toUpperCase()}]`)} ${message}`);
}

function createDirectories() {
  const dirs = [config.reportDir, config.screenshotDir, config.videoDir];
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      log(`Created directory: ${dir}`);
    }
  });
}

function checkPrerequisites() {
  log('Checking prerequisites...');
  
  try {
    // Check if Cypress is installed
    execSync('npx cypress version', { stdio: 'pipe' });
    log('✓ Cypress is installed', 'success');
  } catch (error) {
    log('✗ Cypress is not installed. Run: npm install cypress --save-dev', 'error');
    process.exit(1);
  }
  
  // Check if development server is running
  try {
    const response = execSync(`curl -s -o /dev/null -w "%{http_code}" ${config.baseUrl}`, { 
      stdio: 'pipe',
      timeout: 5000 
    });
    
    if (response.toString().trim() === '200') {
      log('✓ Development server is running', 'success');
    } else {
      log('✗ Development server is not responding', 'warning');
      log('Starting development server...', 'info');
      startDevServer();
    }
  } catch (error) {
    log('✗ Cannot reach development server', 'warning');
    log('Starting development server...', 'info');
    startDevServer();
  }
}

function startDevServer() {
  return new Promise((resolve, reject) => {
    log('Starting development server...');
    
    const devServer = spawn('npm', ['run', 'dev'], {
      stdio: 'pipe',
      detached: true
    });
    
    let serverReady = false;
    
    devServer.stdout.on('data', (data) => {
      const output = data.toString();
      if (output.includes('Local:') && !serverReady) {
        serverReady = true;
        log('✓ Development server started', 'success');
        resolve(devServer);
      }
    });
    
    devServer.stderr.on('data', (data) => {
      log(`Dev server error: ${data}`, 'error');
    });
    
    // Timeout after 30 seconds
    setTimeout(() => {
      if (!serverReady) {
        log('✗ Development server failed to start within 30 seconds', 'error');
        reject(new Error('Server startup timeout'));
      }
    }, 30000);
  });
}

function buildCypressCommand(suite, options = {}) {
  const cmd = ['npx', 'cypress', 'run'];
  
  // Add browser
  cmd.push('--browser', options.browser || config.browser);
  
  // Add headless mode
  if (config.headless) {
    cmd.push('--headless');
  }
  
  // Add specs
  if (suite && testSuites[suite]) {
    cmd.push('--spec', testSuites[suite].specs.join(','));
  }
  
  // Add environment variables
  cmd.push('--env', `baseUrl=${config.baseUrl},apiUrl=${config.apiUrl}`);
  
  // Add reporter
  cmd.push('--reporter', 'cypress-mochawesome-reporter');
  
  // Add record key if recording
  if (config.record && process.env.CYPRESS_RECORD_KEY) {
    cmd.push('--record', '--key', process.env.CYPRESS_RECORD_KEY);
  }
  
  // Add parallel execution
  if (config.parallel) {
    cmd.push('--parallel');
  }
  
  return cmd;
}

function runTests(suite = 'full', options = {}) {
  return new Promise((resolve, reject) => {
    const suiteConfig = testSuites[suite];
    if (!suiteConfig) {
      reject(new Error(`Unknown test suite: ${suite}`));
      return;
    }
    
    log(`Running ${suiteConfig.name}...`, 'info');
    log(`Description: ${suiteConfig.description}`, 'info');
    log(`Specs: ${suiteConfig.specs.length} files`, 'info');
    
    const cmd = buildCypressCommand(suite, options);
    log(`Command: ${cmd.join(' ')}`, 'info');
    
    const startTime = Date.now();
    
    const cypress = spawn(cmd[0], cmd.slice(1), {
      stdio: 'inherit',
      env: {
        ...process.env,
        CYPRESS_BASE_URL: config.baseUrl,
        CYPRESS_API_URL: config.apiUrl
      }
    });
    
    cypress.on('close', (code) => {
      const duration = Date.now() - startTime;
      const durationStr = `${(duration / 1000).toFixed(2)}s`;
      
      if (code === 0) {
        log(`✓ ${suiteConfig.name} completed successfully in ${durationStr}`, 'success');
        resolve({ code, duration, suite: suiteConfig });
      } else {
        log(`✗ ${suiteConfig.name} failed with exit code ${code} after ${durationStr}`, 'error');
        reject({ code, duration, suite: suiteConfig });
      }
    });
    
    cypress.on('error', (error) => {
      log(`✗ Failed to start Cypress: ${error.message}`, 'error');
      reject(error);
    });
  });
}

function generateReport(results) {
  log('Generating test report...', 'info');
  
  const reportPath = path.join(config.reportDir, 'test-summary.json');
  const report = {
    timestamp: new Date().toISOString(),
    config: config,
    results: results,
    summary: {
      total: results.length,
      passed: results.filter(r => r.code === 0).length,
      failed: results.filter(r => r.code !== 0).length,
      totalDuration: results.reduce((sum, r) => sum + r.duration, 0)
    }
  };
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  log(`Report saved to: ${reportPath}`, 'success');
  
  // Print summary
  console.log('\n' + chalk.bold('TEST SUMMARY'));
  console.log('='.repeat(50));
  console.log(`Total Suites: ${report.summary.total}`);
  console.log(`Passed: ${chalk.green(report.summary.passed)}`);
  console.log(`Failed: ${chalk.red(report.summary.failed)}`);
  console.log(`Total Duration: ${(report.summary.totalDuration / 1000).toFixed(2)}s`);
  console.log('='.repeat(50));
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const suite = args[0] || 'full';
  const options = {};
  
  // Parse command line options
  for (let i = 1; i < args.length; i += 2) {
    const key = args[i].replace('--', '');
    const value = args[i + 1];
    options[key] = value;
  }
  
  try {
    log('Starting Cypress E2E Test Runner', 'info');
    log(`Suite: ${suite}`, 'info');
    log(`Browser: ${options.browser || config.browser}`, 'info');
    log(`Base URL: ${config.baseUrl}`, 'info');
    
    createDirectories();
    checkPrerequisites();
    
    const result = await runTests(suite, options);
    generateReport([result]);
    
    log('All tests completed successfully!', 'success');
    process.exit(0);
    
  } catch (error) {
    log(`Test execution failed: ${error.message || error.code}`, 'error');
    
    if (error.suite) {
      generateReport([error]);
    }
    
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  log('Test execution interrupted', 'warning');
  process.exit(1);
});

process.on('SIGTERM', () => {
  log('Test execution terminated', 'warning');
  process.exit(1);
});

// Show help
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
Cypress E2E Test Runner

Usage: node run-e2e-tests.js [suite] [options]

Suites:
  smoke       - Quick smoke tests
  full        - Complete test suite (default)
  accessibility - Accessibility tests only
  api         - API integration tests
  ui          - UI component tests

Options:
  --browser   - Browser to use (chrome, firefox, edge)
  --help      - Show this help message

Examples:
  node run-e2e-tests.js smoke
  node run-e2e-tests.js full --browser firefox
  node run-e2e-tests.js accessibility
`);
  process.exit(0);
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { runTests, testSuites, config };
