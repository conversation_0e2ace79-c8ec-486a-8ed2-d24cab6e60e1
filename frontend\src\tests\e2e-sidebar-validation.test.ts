/**
 * END-TO-END SIDEBAR VALIDATION TESTS
 * Comprehensive E2E testing to identify all issues and bugs in the role-based sidebar system
 */

import { validateRoleBasedSidebar } from '../scripts/validateRoleBasedSidebar';
// Removed import - functionality moved to sidebarDebugger
import { getUserRole, hasAnyRole, canAccessRoute } from '../utils/unifiedRoleManager';
import { NAVIGATION_ITEMS, getNavigationItemsForRole } from '../config/navigationConfig';
import { User } from '../services/api';

interface E2ETestResult {
  testSuite: string;
  testName: string;
  status: 'PASS' | 'FAIL' | 'ERROR';
  message: string;
  error?: any;
  details?: any;
}

class E2ESidebarTester {
  private results: E2ETestResult[] = [];
  private criticalIssues: string[] = [];
  private warnings: string[] = [];

  private addResult(testSuite: string, testName: string, status: 'PASS' | 'FAIL' | 'ERROR', message: string, error?: any, details?: any) {
    this.results.push({ testSuite, testName, status, message, error, details });
    
    if (status === 'FAIL' || status === 'ERROR') {
      this.criticalIssues.push(`${testSuite} - ${testName}: ${message}`);
    }
  }

  /**
   * Test Suite 1: Configuration Integrity
   */
  private async testConfigurationIntegrity() {
    const suite = 'Configuration Integrity';

    try {
      // Test 1.1: Navigation items structure
      const hasValidStructure = NAVIGATION_ITEMS.every(item => 
        item.id && item.name && item.path && item.icon && item.allowedRoles && item.category
      );
      
      if (hasValidStructure) {
        this.addResult(suite, 'Navigation Structure', 'PASS', 'All navigation items have valid structure');
      } else {
        const invalidItems = NAVIGATION_ITEMS.filter(item => 
          !item.id || !item.name || !item.path || !item.icon || !item.allowedRoles || !item.category
        );
        this.addResult(suite, 'Navigation Structure', 'FAIL', 
          `${invalidItems.length} items have invalid structure`, null, invalidItems);
      }

      // Test 1.2: Icon consistency
      const stringIcons = NAVIGATION_ITEMS.filter(item => typeof item.icon === 'string');
      const jsxIcons = NAVIGATION_ITEMS.filter(item => typeof item.icon !== 'string');
      
      if (jsxIcons.length === 0) {
        this.addResult(suite, 'Icon Consistency', 'PASS', 'All icons are string-based');
      } else {
        this.addResult(suite, 'Icon Consistency', 'FAIL', 
          `${jsxIcons.length} items still have JSX icons`, null, jsxIcons.map(i => i.id));
      }

      // Test 1.3: Path uniqueness
      const paths = NAVIGATION_ITEMS.map(item => item.path);
      const duplicatePaths = paths.filter((path, index) => paths.indexOf(path) !== index);
      
      if (duplicatePaths.length === 0) {
        this.addResult(suite, 'Path Uniqueness', 'PASS', 'All paths are unique');
      } else {
        this.addResult(suite, 'Path Uniqueness', 'FAIL', 
          `Duplicate paths found: ${duplicatePaths.join(', ')}`);
      }

      // Test 1.4: Role validity
      const validRoles = ['user', 'mentor', 'investor', 'moderator', 'admin', 'super_admin'];
      const invalidRoleItems = NAVIGATION_ITEMS.filter(item => 
        !item.allowedRoles.every(role => validRoles.includes(role))
      );
      
      if (invalidRoleItems.length === 0) {
        this.addResult(suite, 'Role Validity', 'PASS', 'All roles are valid');
      } else {
        this.addResult(suite, 'Role Validity', 'FAIL', 
          `${invalidRoleItems.length} items have invalid roles`, null, invalidRoleItems);
      }

    } catch (error) {
      this.addResult(suite, 'Configuration Test', 'ERROR', 'Failed to test configuration', error);
    }
  }

  /**
   * Test Suite 2: Role-Based Filtering
   */
  private async testRoleBasedFiltering() {
    const suite = 'Role-Based Filtering';
    const roles = ['user', 'mentor', 'investor', 'moderator', 'admin', 'super_admin'];

    try {
      for (const role of roles) {
        // Test filtering function
        const filteredItems = getNavigationItemsForRole(role as any);
        const shouldNotSee = filteredItems.filter(item => !item.allowedRoles.includes(role as any));
        
        if (shouldNotSee.length === 0) {
          this.addResult(suite, `${role} Filtering`, 'PASS', 
            `${role} correctly sees ${filteredItems.length} items`);
        } else {
          this.addResult(suite, `${role} Filtering`, 'FAIL', 
            `${role} incorrectly sees ${shouldNotSee.length} unauthorized items`, 
            null, shouldNotSee.map(i => i.id));
        }

        // Test role hierarchy
        if (role === 'super_admin') {
          const superAdminItems = filteredItems.length;
          const totalAllowedItems = NAVIGATION_ITEMS.filter(item => 
            item.allowedRoles.includes('super_admin')
          ).length;
          
          if (superAdminItems === totalAllowedItems) {
            this.addResult(suite, 'Super Admin Access', 'PASS', 
              'Super admin has access to all allowed items');
          } else {
            this.addResult(suite, 'Super Admin Access', 'FAIL', 
              `Super admin missing ${totalAllowedItems - superAdminItems} items`);
          }
        }
      }
    } catch (error) {
      this.addResult(suite, 'Role Filtering Test', 'ERROR', 'Failed to test role filtering', error);
    }
  }

  /**
   * Test Suite 3: Component Integration
   */
  private async testComponentIntegration() {
    const suite = 'Component Integration';

    try {
      // Test 3.1: UniversalSidebar import
      try {
        const { default: UniversalSidebar } = await import('../components/layout/UniversalSidebar');
        this.addResult(suite, 'Sidebar Import', 'PASS', 'UniversalSidebar imports successfully');
      } catch (error) {
        this.addResult(suite, 'Sidebar Import', 'FAIL', 'Failed to import UniversalSidebar', error);
      }

      // Test 3.2: Navigation config import
      try {
        const config = await import('../config/navigationConfig');
        if (config.NAVIGATION_ITEMS && config.getNavigationItemsForRole) {
          this.addResult(suite, 'Config Import', 'PASS', 'Navigation config imports successfully');
        } else {
          this.addResult(suite, 'Config Import', 'FAIL', 'Navigation config missing exports');
        }
      } catch (error) {
        this.addResult(suite, 'Config Import', 'FAIL', 'Failed to import navigation config', error);
      }

      // Test 3.3: Role manager integration
      try {
        const roleManager = await import('../utils/unifiedRoleManager');
        if (roleManager.getUserRole && roleManager.hasAnyRole && roleManager.canAccessRoute) {
          this.addResult(suite, 'Role Manager Import', 'PASS', 'Role manager imports successfully');
        } else {
          this.addResult(suite, 'Role Manager Import', 'FAIL', 'Role manager missing exports');
        }
      } catch (error) {
        this.addResult(suite, 'Role Manager Import', 'FAIL', 'Failed to import role manager', error);
      }

    } catch (error) {
      this.addResult(suite, 'Component Integration Test', 'ERROR', 'Failed to test component integration', error);
    }
  }

  /**
   * Test Suite 4: User Role Simulation
   */
  private async testUserRoleSimulation() {
    const suite = 'User Role Simulation';

    const testUsers: Record<string, User> = {
      regularUser: {
        id: 1, username: 'user', email: '<EMAIL>', first_name: 'Regular', last_name: 'User',
        is_admin: false, is_staff: false, is_superuser: false, user_role: 'user'
      },
      mentor: {
        id: 2, username: 'mentor', email: '<EMAIL>', first_name: 'Mentor', last_name: 'User',
        is_admin: false, is_staff: false, is_superuser: false, user_role: 'mentor'
      },
      investor: {
        id: 3, username: 'investor', email: '<EMAIL>', first_name: 'Investor', last_name: 'User',
        is_admin: false, is_staff: false, is_superuser: false, user_role: 'investor'
      },
      moderator: {
        id: 4, username: 'moderator', email: '<EMAIL>', first_name: 'Moderator', last_name: 'User',
        is_admin: false, is_staff: false, is_superuser: false, user_role: 'moderator'
      },
      admin: {
        id: 5, username: 'admin', email: '<EMAIL>', first_name: 'Admin', last_name: 'User',
        is_admin: true, is_staff: true, is_superuser: false, user_role: 'admin'
      },
      superAdmin: {
        id: 6, username: 'superadmin', email: '<EMAIL>', first_name: 'Super', last_name: 'Admin',
        is_admin: true, is_staff: true, is_superuser: true, user_role: 'super_admin'
      }
    };

    try {
      for (const [userType, user] of Object.entries(testUsers)) {
        // Test role detection
        const detectedRole = getUserRole(user);
        const expectedRole = user.user_role;
        
        if (detectedRole === expectedRole) {
          this.addResult(suite, `${userType} Role Detection`, 'PASS', 
            `Correctly detected as ${detectedRole}`);
        } else {
          this.addResult(suite, `${userType} Role Detection`, 'FAIL', 
            `Expected ${expectedRole}, got ${detectedRole}`);
        }

        // Test navigation items
        const navItems = getNavigationItemsForRole(detectedRole);
        if (navItems.length > 0) {
          this.addResult(suite, `${userType} Navigation`, 'PASS', 
            `Has ${navItems.length} navigation items`);
        } else {
          this.addResult(suite, `${userType} Navigation`, 'FAIL', 
            'No navigation items found');
        }

        // Test specific access patterns
        const hasBasicAccess = navItems.some(item => item.id === 'dashboard');
        if (hasBasicAccess) {
          this.addResult(suite, `${userType} Basic Access`, 'PASS', 'Has dashboard access');
        } else {
          this.addResult(suite, `${userType} Basic Access`, 'FAIL', 'Missing dashboard access');
        }
      }
    } catch (error) {
      this.addResult(suite, 'User Simulation Test', 'ERROR', 'Failed to test user simulation', error);
    }
  }

  /**
   * Test Suite 5: Cross-Browser Compatibility
   */
  private async testCrossBrowserCompatibility() {
    const suite = 'Cross-Browser Compatibility';

    try {
      // Test modern JavaScript features
      const hasModernFeatures = !!(
        Array.prototype.includes &&
        Object.entries &&
        Promise.resolve &&
        Map &&
        Set
      );

      if (hasModernFeatures) {
        this.addResult(suite, 'Modern JS Features', 'PASS', 'All required JS features available');
      } else {
        this.addResult(suite, 'Modern JS Features', 'FAIL', 'Missing required JS features');
      }

      // Test ES6 imports
      try {
        const testImport = await import('../utils/unifiedRoleManager');
        this.addResult(suite, 'ES6 Imports', 'PASS', 'ES6 imports working');
      } catch (error) {
        this.addResult(suite, 'ES6 Imports', 'FAIL', 'ES6 imports failing', error);
      }

    } catch (error) {
      this.addResult(suite, 'Browser Compatibility Test', 'ERROR', 'Failed to test browser compatibility', error);
    }
  }

  /**
   * Run all E2E tests
   */
  public async runAllTests(): Promise<E2ETestResult[]> {
    console.log('🚀 Starting Comprehensive E2E Sidebar Tests...\n');

    this.results = [];
    this.criticalIssues = [];
    this.warnings = [];

    await this.testConfigurationIntegrity();
    await this.testRoleBasedFiltering();
    await this.testComponentIntegration();
    await this.testUserRoleSimulation();
    await this.testCrossBrowserCompatibility();

    return this.results;
  }

  /**
   * Generate comprehensive E2E test report
   */
  public generateE2EReport(): string {
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.status === 'PASS').length;
    const failedTests = this.results.filter(r => r.status === 'FAIL').length;
    const errorTests = this.results.filter(r => r.status === 'ERROR').length;

    let report = '🔍 COMPREHENSIVE E2E SIDEBAR TEST REPORT\n';
    report += '=' .repeat(70) + '\n\n';

    // Group by test suite
    const suites = [...new Set(this.results.map(r => r.testSuite))];
    
    suites.forEach(suite => {
      report += `📋 ${suite}\n`;
      report += '-' .repeat(50) + '\n';

      const suiteResults = this.results.filter(r => r.testSuite === suite);
      suiteResults.forEach(result => {
        const icon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '🚨';
        report += `${icon} ${result.testName}: ${result.message}\n`;
        
        if (result.error) {
          report += `   Error: ${result.error.message || result.error}\n`;
        }
        if (result.details) {
          report += `   Details: ${JSON.stringify(result.details, null, 2)}\n`;
        }
      });
      report += '\n';
    });

    // Critical Issues Summary
    if (this.criticalIssues.length > 0) {
      report += '🚨 CRITICAL ISSUES FOUND\n';
      report += '-' .repeat(30) + '\n';
      this.criticalIssues.forEach((issue, index) => {
        report += `${index + 1}. ${issue}\n`;
      });
      report += '\n';
    }

    // Overall Summary
    report += '=' .repeat(70) + '\n';
    report += `📊 OVERALL SUMMARY\n`;
    report += `Total Tests: ${totalTests}\n`;
    report += `✅ Passed: ${passedTests}\n`;
    report += `❌ Failed: ${failedTests}\n`;
    report += `🚨 Errors: ${errorTests}\n`;
    report += `Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%\n\n`;

    if (failedTests === 0 && errorTests === 0) {
      report += '🎉 ALL E2E TESTS PASSED! Sidebar system is working correctly.\n';
    } else {
      report += `⚠️ ${failedTests + errorTests} ISSUES FOUND. System needs fixes before deployment.\n`;
    }

    return report;
  }

  public getCriticalIssues(): string[] {
    return this.criticalIssues;
  }
}

/**
 * Main E2E test runner
 */
export async function runE2ESidebarTests(): Promise<boolean> {
  const tester = new E2ESidebarTester();
  
  try {
    await tester.runAllTests();
    const report = tester.generateE2EReport();
    console.log(report);
    
    const criticalIssues = tester.getCriticalIssues();
    return criticalIssues.length === 0;
    
  } catch (error) {
    console.error('🚨 E2E Test Runner Failed:', error);
    return false;
  }
}

// Export for browser console testing
(window as any).runE2ESidebarTests = runE2ESidebarTests;

export { E2ESidebarTester, E2ETestResult };
