import React from 'react';
import PublicLayout from './PublicLayout';
import AuthenticatedLayout from './AuthenticatedLayout';
import AIFocusedLayout from './AIFocusedLayout';

interface LayoutProps {
  children: React.ReactNode;
  type?: 'public' | 'authenticated' | 'ai-focused';
}

/**
 * Unified Layout Component
 * Routes to the appropriate layout based on type
 */
const Layout: React.FC<LayoutProps> = ({ children, type = 'authenticated' }) => {
  switch (type) {
    case 'public':
      return <PublicLayout>{children}</PublicLayout>;
    case 'ai-focused':
      return <AIFocusedLayout>{children}</AIFocusedLayout>;
    case 'authenticated':
    default:
      return <AuthenticatedLayout>{children}</AuthenticatedLayout>;
  }
};

export default Layout;
