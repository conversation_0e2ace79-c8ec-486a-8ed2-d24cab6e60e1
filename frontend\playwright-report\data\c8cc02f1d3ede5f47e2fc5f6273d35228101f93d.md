# Page snapshot

```yaml
- link "Yasmeen AI":
  - /url: /
  - heading "Yasmeen AI" [level=1]
- text: Sign in to your account
- img
- heading "Welcome Back" [level=2]
- text: Username
- textbox "Username"
- text: Password
- textbox "Password"
- button "Sign In":
  - img
  - text: Sign In
- paragraph:
  - text: Don't have an account?
  - link "Register":
    - /url: /register
- link "Return to Homepage":
  - /url: /
- button "Open Tanstack query devtools":
  - img
- text: "[plugin:vite:react-babel] C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\src\\components\\admin\\dashboard\\Dashboard.tsx: Adjacent JSX elements must be wrapped in an enclosing tag. Did you want a JSX fragment <>...</>? (159:4) 162 | C:/Users/<USER>/Downloads/project-bolt-sb1-wl1xvo5f - Copy/frontend/src/components/admin/dashboard/Dashboard.tsx:159:4 157| </div> 158| </div> 159| </div> | ^ 160| ); 161| }; at constructor (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:362:19) at TypeScriptParserMixin.raise (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:3259:19) at TypeScriptParserMixin.jsxParseElementAt (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:6789:18) at TypeScriptParserMixin.jsxParseElement (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:6796:17) at TypeScriptParserMixin.parseExprAtom (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:6806:19) at TypeScriptParserMixin.parseExprSubscripts (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:10591:23) at TypeScriptParserMixin.parseUpdate (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:10576:21) at TypeScriptParserMixin.parseMaybeUnary (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:10556:23) at TypeScriptParserMixin.parseMaybeUnary (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:9483:18) at TypeScriptParserMixin.parseMaybeUnaryOrPrivate (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:10410:61) at TypeScriptParserMixin.parseExprOps (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:10415:23) at TypeScriptParserMixin.parseMaybeConditional (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:10392:23) at TypeScriptParserMixin.parseMaybeAssign (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:10355:21) at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:9421:39 at TypeScriptParserMixin.tryParse (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:3597:20) at TypeScriptParserMixin.parseMaybeAssign (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:9421:18) at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:10325:39 at TypeScriptParserMixin.allowInAnd (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:11937:12) at TypeScriptParserMixin.parseMaybeAssignAllowIn (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:10325:17) at TypeScriptParserMixin.parseParenAndDistinguishExpression (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:11196:28) at TypeScriptParserMixin.parseExprAtom (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:10849:23) at TypeScriptParserMixin.parseExprAtom (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:6811:20) at TypeScriptParserMixin.parseExprSubscripts (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:10591:23) at TypeScriptParserMixin.parseUpdate (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:10576:21) at TypeScriptParserMixin.parseMaybeUnary (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:10556:23) at TypeScriptParserMixin.parseMaybeUnary (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:9483:18) at TypeScriptParserMixin.parseMaybeUnaryOrPrivate (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:10410:61) at TypeScriptParserMixin.parseExprOps (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:10415:23) at TypeScriptParserMixin.parseMaybeConditional (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:10392:23) at TypeScriptParserMixin.parseMaybeAssign (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:10355:21) at TypeScriptParserMixin.parseMaybeAssign (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:9432:20) at TypeScriptParserMixin.parseExpressionBase (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:10309:23) at C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:10305:39 at TypeScriptParserMixin.allowInAnd (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:11932:16) at TypeScriptParserMixin.parseExpression (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:10305:17) at TypeScriptParserMixin.parseReturnStatement (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:12622:28) at TypeScriptParserMixin.parseStatementContent (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:12274:21) at TypeScriptParserMixin.parseStatementContent (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:9157:18) at TypeScriptParserMixin.parseStatementLike (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:12243:17) at TypeScriptParserMixin.parseStatementListItem (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:12223:17) at TypeScriptParserMixin.parseBlockOrModuleBlockBody (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:12796:61) at TypeScriptParserMixin.parseBlockBody (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:12789:10) at TypeScriptParserMixin.parseBlock (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:12777:10) at TypeScriptParserMixin.parseFunctionBody (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:11620:24) at TypeScriptParserMixin.parseArrowExpression (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:11595:10) at TypeScriptParserMixin.parseParenAndDistinguishExpression (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:11208:12) at TypeScriptParserMixin.parseExprAtom (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:10849:23) at TypeScriptParserMixin.parseExprAtom (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:6811:20) at TypeScriptParserMixin.parseExprSubscripts (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:10591:23) at TypeScriptParserMixin.parseUpdate (C:\\Users\\<USER>\\Downloads\\project-bolt-sb1-wl1xvo5f - Copy\\frontend\\node_modules\\@babel\\parser\\lib\\index.js:10576:21 Click outside, press Esc key, or fix the code to dismiss. You can also disable this overlay by setting"
- code: server.hmr.overlay
- text: to
- code: "false"
- text: in
- code: vite.config.ts
- text: .
```