/**
 * NAVIGATION CONFIGURATION
 * Centralized navigation configuration with role-based access control
 * Eliminates hardcoded userTypes arrays throughout the application
 */

import { UserRole } from '../utils/unifiedRoleManager';
import { 
  Home, Lightbulb, FileText, MessageSquare, Calendar, BookOpen, 
  BarChart3, Users, Settings, Shield, Database, Monitor, Lock, 
  AlertTriangle, Activity, PieChart, UserCheck, Building, Rocket, 
  Star, Gift, Mail, Phone, HelpCircle, Book, Video, Image, Music, 
  Code, Palette, Camera, Headphones, Gamepad2, Coffee, Sparkles, Bot
} from 'lucide-react';

export interface NavItem {
  id: string;
  name: string;
  path: string;
  icon: React.ReactNode;
  allowedRoles: UserRole[];
  category: 'main' | 'content' | 'system' | 'security' | 'super_admin' | 'ai';
  riskLevel?: 'low' | 'medium' | 'high' | 'critical';
  children?: NavItem[];
}

/**
 * AUTHORITATIVE NAVIGATION CONFIGURATION
 * Single source of truth for all navigation items and their role requirements
 */
export const NAVIGATION_ITEMS: NavItem[] = [
  // Main Dashboard - All authenticated users
  {
    id: 'dashboard',
    name: 'dashboard.title',
    path: '/dashboard',
    icon: <Home className="w-5 h-5" />,
    allowedRoles: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'],
    category: 'main'
  },

  // Business & Ideas - Only for business-focused users (NOT admins/moderators)
  {
    id: 'business-ideas',
    name: 'businessIdeas.title',
    path: '/dashboard/business-ideas',
    icon: <Lightbulb className="w-5 h-5" />,
    allowedRoles: ['user', 'mentor', 'investor'],
    category: 'main'
  },
  {
    id: 'business-plans',
    name: 'businessPlans.title',
    path: '/dashboard/business-plans',
    icon: <FileText className="w-5 h-5" />,
    allowedRoles: ['user', 'mentor', 'investor'],
    category: 'main'
  },
  {
    id: 'incubator',
    name: 'incubator.title',
    path: '/dashboard/incubator',
    icon: <Building className="w-5 h-5" />,
    allowedRoles: ['user', 'mentor', 'investor'],
    category: 'main'
  },

  // Content & Resources - Different access levels
  {
    id: 'posts',
    name: 'posts.title',
    path: '/dashboard/posts',
    icon: <MessageSquare className="w-5 h-5" />,
    allowedRoles: ['user', 'mentor', 'investor'],
    category: 'content'
  },
  {
    id: 'events',
    name: 'events.title',
    path: '/dashboard/events',
    icon: <Calendar className="w-5 h-5" />,
    allowedRoles: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'],
    category: 'content'
  },
  {
    id: 'resources',
    name: 'resources.title',
    path: '/dashboard/resources',
    icon: <BookOpen className="w-5 h-5" />,
    allowedRoles: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'],
    category: 'content'
  },
  {
    id: 'templates',
    name: 'templates.title',
    path: '/dashboard/templates',
    icon: <FileText className="w-5 h-5" />,
    allowedRoles: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'],
    category: 'content'
  },

  // Analytics & Insights - Role-specific analytics
  {
    id: 'analytics',
    name: 'analytics.title',
    path: '/dashboard/analytics',
    icon: <BarChart3 className="w-5 h-5" />,
    allowedRoles: ['user', 'mentor', 'investor', 'moderator'],
    category: 'main'
  },

  // Mentor-specific features
  {
    id: 'mentorship-sessions',
    name: 'mentorship.sessions.title',
    path: '/dashboard/mentorship/sessions',
    icon: <Users className="w-5 h-5" />,
    allowedRoles: ['mentor'],
    category: 'main'
  },
  {
    id: 'mentees',
    name: 'mentorship.mentees.title',
    path: '/dashboard/mentorship/mentees',
    icon: <UserCheck className="w-5 h-5" />,
    allowedRoles: ['mentor'],
    category: 'main'
  },

  // Investor-specific features
  {
    id: 'investment-opportunities',
    name: 'investments.opportunities.title',
    path: '/dashboard/investments/opportunities',
    icon: <Star className="w-5 h-5" />,
    allowedRoles: ['investor'],
    category: 'main'
  },
  {
    id: 'portfolio',
    name: 'investments.portfolio.title',
    path: '/dashboard/investments/portfolio',
    icon: <PieChart className="w-5 h-5" />,
    allowedRoles: ['investor'],
    category: 'main'
  },

  // Moderator-specific features
  {
    id: 'content-moderation',
    name: 'moderation.content.title',
    path: '/dashboard/moderation/content',
    icon: <Shield className="w-5 h-5" />,
    allowedRoles: ['moderator'],
    category: 'main'
  },
  {
    id: 'user-moderation',
    name: 'moderation.users.title',
    path: '/dashboard/moderation/users',
    icon: <Users className="w-5 h-5" />,
    allowedRoles: ['moderator'],
    category: 'main'
  },

  // Forums - Public forum access for all users
  {
    id: 'forums',
    name: 'forums.title',
    path: '/forum',
    icon: <MessageSquare className="w-5 h-5" />,
    allowedRoles: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'],
    category: 'content'
  },

  // Chat - Direct messaging and communication
  {
    id: 'chat',
    name: 'chat.title',
    path: '/chat',
    icon: <MessageSquare className="w-5 h-5" />,
    allowedRoles: ['user', 'mentor', 'investor'],
    category: 'content'
  },

  // AI Features - Available to business-focused users
  {
    id: 'ai-assistant',
    name: 'ai.assistant.title',
    path: '/chat/enhanced',
    icon: <Bot className="w-5 h-5" />,
    allowedRoles: ['user', 'mentor', 'investor'],
    category: 'ai'
  },

  // Admin Features - Only for admins and super_admins
  {
    id: 'user-management',
    name: 'admin.users.title',
    path: '/admin/users',
    icon: <Users className="w-5 h-5" />,
    allowedRoles: ['admin', 'super_admin'],
    category: 'system'
  },
  {
    id: 'admin-analytics',
    name: 'admin.analytics.title',
    path: '/admin/analytics',
    icon: <BarChart3 className="w-5 h-5" />,
    allowedRoles: ['admin', 'super_admin'],
    category: 'system'
  },
  {
    id: 'system-settings',
    name: 'admin.settings.title',
    path: '/admin/settings',
    icon: <Settings className="w-5 h-5" />,
    allowedRoles: ['admin', 'super_admin'],
    category: 'system'
  },

  // Super Admin Features - CRITICAL ACCESS
  {
    id: 'super-admin-dashboard',
    name: 'superAdmin.dashboard.title',
    path: '/super_admin',
    icon: <Shield className="w-5 h-5" />,
    allowedRoles: ['super_admin'],
    category: 'super_admin',
    riskLevel: 'critical'
  },
  {
    id: 'system-monitoring',
    name: 'superAdmin.monitoring.title',
    path: '/super_admin/monitoring',
    icon: <Monitor className="w-5 h-5" />,
    allowedRoles: ['super_admin'],
    category: 'super_admin',
    riskLevel: 'critical'
  },
  {
    id: 'ai-system-management',
    name: 'superAdmin.ai.title',
    path: '/super_admin/ai-system-management',
    icon: <Bot className="w-5 h-5" />,
    allowedRoles: ['super_admin'],
    category: 'system',
    riskLevel: 'high'
  },

  // User Profile & Settings - Available to all roles
  {
    id: 'profile',
    name: 'profile.title',
    path: '/profile',
    icon: <Users className="w-5 h-5" />,
    allowedRoles: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'],
    category: 'main'
  },
  {
    id: 'settings',
    name: 'settings.title',
    path: '/settings',
    icon: <Settings className="w-5 h-5" />,
    allowedRoles: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'moderator'],
    category: 'main'
  }
];

/**
 * Get navigation items filtered by user role
 */
export function getNavigationItemsForRole(userRole: UserRole): NavItem[] {
  return NAVIGATION_ITEMS.filter(item => item.allowedRoles.includes(userRole));
}

/**
 * Check if a user role can access a specific navigation item
 */
export function canAccessNavItem(userRole: UserRole, itemId: string): boolean {
  const item = NAVIGATION_ITEMS.find(item => item.id === itemId);
  return item ? item.allowedRoles.includes(userRole) : false;
}
