import React, { Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAppSelector } from '../store/hooks';
import { allRoutes } from './consolidatedRoutes';
import { RouteConfig } from './routeConfig';
import RoleRoute from '../components/routing/RoleRoute';
import Layout from '../components/layout/Layout';
import NotFoundPage from '../pages/NotFoundPage';
import AccessDeniedPage from '../pages/AccessDeniedPage';

/**
 * Main Application Routes Component
 * Renders all routes from the consolidated route configuration
 */
const AppRoutes: React.FC = () => {
  const { isAuthenticated, isLoading } = useAppSelector((state) => state.auth);

  // Show loading state while authentication is being checked
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  /**
   * Render a single route with appropriate layout and role protection
   */
  const renderRoute = (routeConfig: RouteConfig) => {
    const { path, component: Component, layout, loadingMessage, requireAuth } = routeConfig;

    // Create the route element with suspense
    const routeElement = (
      <Suspense 
        fallback={
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-600">{loadingMessage || 'Loading...'}</p>
            </div>
          </div>
        }
      >
        <Component />
      </Suspense>
    );

    // Wrap with role protection if needed
    const protectedElement = requireAuth !== false ? (
      <RoleRoute config={routeConfig}>
        {routeElement}
      </RoleRoute>
    ) : routeElement;

    // Wrap with layout if specified
    const layoutWrappedElement = layout ? (
      <Layout type={layout}>
        {protectedElement}
      </Layout>
    ) : protectedElement;

    return (
      <Route
        key={path}
        path={path}
        element={layoutWrappedElement}
      />
    );
  };

  return (
    <Routes>
      {/* Render all consolidated routes */}
      {allRoutes.map(renderRoute)}
      
      {/* Access denied route */}
      <Route path="/access-denied" element={<AccessDeniedPage />} />
      
      {/* Catch-all route for 404 */}
      <Route path="*" element={<NotFoundPage />} />
    </Routes>
  );
};

export default AppRoutes;
