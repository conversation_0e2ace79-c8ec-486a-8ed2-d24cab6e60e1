import React, { useState } from 'react';
import { RefreshCw } from 'lucide-react';
// DashboardLayout removed - handled by routing system with AuthenticatedLayout
import { AdvancedFilter, BulkActions } from '../../common';
import { getPostBulkActions } from '../../common/BulkActions';
import { PostSkeleton, SkeletonList } from '../../../ui/Skeleton';
import PostItem from './components/PostItem';
import PostCreateModal from './components/PostCreateModal';
import PostEditModal from './components/PostEditModal';
import PostDeleteModal from './components/PostDeleteModal';
import PostsHeader from './components/PostsHeader';
import usePostsManagement from './hooks/usePostsManagement';
import { FilterValue } from '../../common/AdvancedFilter';
import { Post, postsAPI } from '../../../../services/api';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../../hooks/useLanguage';
const PostsManagementPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const {
    posts,
    filteredPosts,
    loading,
    error,
    hasMore,
    lastElementRef,
    refreshPosts,
    loadMore,
    searchTerm,
    setSearchTerm,
    selectedPost,
    isDeleteModalOpen,
    setIsDeleteModalOpen,
    isCreateModalOpen,
    setIsCreateModalOpen,
    isEditModalOpen,
    setIsEditModalOpen,
    expandedPost,
    setExpandedPost,
    formError,
    formSuccess,
    formSubmitting,
    allPosts,
    setAllPosts,
    activeFilters,
    setActiveFilters,
    formData,
    handleInputChange,
    resetForm,
    openCreateModal,
    handleEditPost,
    handleDeletePost,
    createPost,
    updatePost
  } = usePostsManagement();

  // Bulk actions state
  const [selectedItems, setSelectedItems] = useState<number[]>([]);

  // Toggle expanded post
  const toggleExpandPost = (postId: number) => {
    setExpandedPost(expandedPost === postId ? null : postId);
  };

  // Handle filter change
  const handleFilterChange = (filters: FilterValue[]) => {
    setActiveFilters(filters);
    refreshPosts(); // Refresh posts when filters change
  };

  // Handle bulk selection
  const handleSelectAll = () => {
    if (selectedItems.length === filteredPosts.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(filteredPosts.map(post => post.id));
    }
  };

  const handleDeselectAll = () => {
    setSelectedItems([]);
  };

  const toggleItemSelection = (postId: number) => {
    if (selectedItems.includes(postId)) {
      setSelectedItems(selectedItems.filter(id => id !== postId));
    } else {
      setSelectedItems([...selectedItems, postId]);
    }
  };

  // Bulk action handlers
  const handleBulkApprove = async (ids: number[]): Promise<void> => {
    try {
      // Process posts in batches of 5 for better performance
      const batchSize = 5;
      const batches = [];

      for (let i = 0; i < ids.length; i += batchSize) {
        batches.push(ids.slice(i, i + batchSize));
      }

      // Process each batch sequentially
      for (const batch of batches) {
        await Promise.all(
          batch.map(id => postsAPI.moderatePost(id, 'approved', 'Bulk approved by admin'))
        );
      }

      // Refresh posts after bulk action
      refreshPosts();
      setSelectedItems([]);
      return Promise.resolve();
    } catch (error) {
      console.error('Error approving posts:', error);
      return Promise.reject(error);
    }
  };

  const handleBulkReject = async (ids: number[]): Promise<void> => {
    try {
      // Process posts in batches of 5 for better performance
      const batchSize = 5;
      const batches = [];

      for (let i = 0; i < ids.length; i += batchSize) {
        batches.push(ids.slice(i, i + batchSize));
      }

      // Process each batch sequentially
      for (const batch of batches) {
        await Promise.all(
          batch.map(id => postsAPI.moderatePost(id, 'rejected', 'Bulk rejected by admin'))
        );
      }

      // Refresh posts after bulk action
      refreshPosts();
      setSelectedItems([]);
      return Promise.resolve();
    } catch (error) {
      console.error('Error rejecting posts:', error);
      return Promise.reject(error);
    }
  };

  const handleBulkDelete = async (ids: number[]): Promise<void> => {
    try {
      // Process posts in batches of 5 for better performance
      const batchSize = 5;
      const batches = [];

      for (let i = 0; i < ids.length; i += batchSize) {
        batches.push(ids.slice(i, i + batchSize));
      }

      // Process each batch sequentially
      for (const batch of batches) {
        await Promise.all(
          batch.map(id => postsAPI.deletePost(id))
        );
      }

      // Update local state
      setAllPosts(allPosts.filter(post => !ids.includes(post.id)));
      refreshPosts();
      setSelectedItems([]);
      return Promise.resolve();
    } catch (error) {
      console.error('Error deleting posts:', error);
      return Promise.reject(error);
    }
  };

  // Prevent default form submission
  const preventFormSubmission = (e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation();
    return false;
  };

  // Filter options for the advanced filter
  const postFilterOptions = [
    { label: t("admin.title.value.title", "Title"), value: 'title', type: 'text' },
    { label: t("admin.content.value.content", "Content"), value: 'content', type: 'text' },
    { label: t("admin.author.value.author", "Author"), value: 'author', type: 'text' },
    {
      label: t("common.status", "Status"),
      value: 'moderation_status',
      type: 'select',
      options: [
        { label: t("admin.approved.value.approved", "Approved"), value: 'approved' },
        { label: t("admin.pending.value.pending", "Pending"), value: 'pending' },
        { label: t("admin.rejected.value.rejected", "Rejected"), value: 'rejected' }
      ]
    },
    { label: t("admin.created.date", "Created Date"), value: 'created_at', type: 'date' }
  ];

  // Load empty data as fallback
  const loadMockData = () => {
    setPosts([]);
    setLoading(false);
  };

  return (
    <div className="p-6">
      <PostsHeader onCreatePost={openCreateModal} />

      {/* Advanced Filter Component */}
      <AdvancedFilter
        filterOptions={postFilterOptions}
        onFilterChange={handleFilterChange}
        activeFilters={activeFilters}
      />

      {/* Search and Bulk Actions */}
      <div className={`mb-6 flex flex-col sm:flex-row justify-between items-start gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
        <div className="relative w-full sm:w-64">
          <input
            type="text"
            placeholder={t("admin.search.posts", "Search posts...")}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-indigo-900/30 border border-indigo-800 rounded-lg text-white focus:outline-none focus:border-purple-500"
          />
          <div className="absolute left-3 top-2.5 text-gray-400">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="11" cy="11" r="8"></circle>
              <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            </svg>
          </div>
        </div>

        {selectedItems.length > 0 && (
          <BulkActions
            selectedCount={selectedItems.length}
            actions={getPostBulkActions(selectedItems, handleBulkApprove, handleBulkReject, handleBulkDelete, t)}
            onActionSelect={(action: string) => {
              switch (action) {
                case 'approve':
                  handleBulkApprove(selectedItems);
                  break;
                case 'reject':
                  handleBulkReject(selectedItems);
                  break;
                case 'delete':
                  handleBulkDelete(selectedItems);
                  break;
              }
            }}
          />
        )}
      </div>

      <div className="bg-indigo-900/20 rounded-xl overflow-hidden shadow-lg">
        {error && (
          <div className="p-4 bg-red-900/50 border border-red-800 text-white rounded-lg mb-4">
            <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={`mr-2 text-red-400 ${isRTL ? "space-x-reverse" : ""}`}>
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
              {error}
            </div>
            <button
              onClick={loadMockData}
              className="mt-2 px-4 py-2 bg-indigo-700 hover:bg-indigo-600 rounded-lg text-white"
            >
              Show Sample Data Instead
            </button>
          </div>
        )}

        {loading && posts.length === 0 ? (
          <SkeletonList count={5} SkeletonComponent={PostSkeleton} />
        ) : (
          <div className="divide-y divide-indigo-800/30">
            {filteredPosts.length === 0 ? (
              <div className="text-center py-10">
                <div className="text-gray-400 text-lg">{t("admin.no.posts.found", "No posts found matching your search.")}</div>
              </div>
            ) : (
              <>
                {filteredPosts.map((post, index) => (
                  <PostItem
                    key={post.id}
                    post={post}
                    isExpanded={expandedPost === post.id}
                    isSelected={selectedItems.includes(post.id)}
                    onToggleExpand={toggleExpandPost}
                    onToggleSelect={toggleItemSelection}
                    onEdit={handleEditPost}
                    onDelete={handleDeletePost}
                    ref={index === filteredPosts.length - 1 ? lastElementRef : undefined}
                  />
                ))}

                {/* Loading indicator at the bottom for infinite scroll */}
                {loading && posts.length > 0 && (
                  <div className={`flex justify-center py-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <div className="w-8 h-8 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
                  </div>
                )}

                {/* Load more button as fallback */}
                {hasMore && !loading && (
                  <div className={`flex justify-center py-6 ${isRTL ? "flex-row-reverse" : ""}`}>
                    <button
                      onClick={loadMore}
                      className={`px-4 py-2 bg-indigo-700 hover:bg-indigo-600 rounded-lg text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                    >
                      <RefreshCw size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                      Load More
                    </button>
                  </div>
                )}
              </>
            )}
          </div>
        )}
      </div>

      {/* Modals */}
      <PostCreateModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        formData={formData}
        formError={formError}
        formSuccess={formSuccess}
        formSubmitting={formSubmitting}
        onInputChange={handleInputChange}
        onSubmit={createPost}
      />

      {selectedPost && (
        <>
          <PostEditModal
            isOpen={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            post={selectedPost}
            formData={formData}
            formError={formError}
            formSuccess={formSuccess}
            formSubmitting={formSubmitting}
            onInputChange={handleInputChange}
            onSubmit={updatePost}
          />

          <PostDeleteModal
            isOpen={isDeleteModalOpen}
            onClose={() => setIsDeleteModalOpen(false)}
            post={selectedPost}
            formError={formError}
            formSubmitting={formSubmitting}
            onConfirmDelete={() => {
              // Implement delete confirmation logic
              const confirmDelete = async () => {
                try {
                  await postsAPI.deletePost(selectedPost.id);
                  setAllPosts(allPosts.filter(post => post.id !== selectedPost.id));
                  refreshPosts();
                  setIsDeleteModalOpen(false);
                } catch (error) {
                  console.error('Error deleting post:', error);
                }
              };
              confirmDelete();
            }}
          />
        </>
      )}
    </div>
  );
};

export default PostsManagementPage;
