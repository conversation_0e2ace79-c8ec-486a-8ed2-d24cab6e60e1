from django.db import models
from django.contrib.auth.models import User
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from api.storage import OptimizedImageStorage


class UserRole(models.Model):
    """Defines available roles in the system with their permissions"""

    ROLE_CHOICES = (
        ('super_admin', 'Super Administrator'),
        ('admin', 'Administrator'),
        ('moderator', 'Moderator'),
        ('mentor', 'Mentor'),
        ('investor', 'Investor'),
        ('user', 'Regular User'),
    )

    PERMISSION_CHOICES = (
        ('read', 'Read Only'),
        ('write', 'Read & Write'),
        ('moderate', 'Moderate Content'),
        ('admin', 'Full Admin Access'),
        ('super_admin', 'Super Admin System Control'),
    )

    name = models.CharField(max_length=50, choices=ROLE_CHOICES, unique=True)
    display_name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    permission_level = models.CharField(max_length=20, choices=PERMISSION_CHOICES, default='read')
    is_active = models.BooleanField(default=True)
    requires_approval = models.BooleanField(default=True, help_text="Whether this role requires admin approval")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.display_name

    class Meta:
        ordering = ['name']
        verbose_name = 'User Role'
        verbose_name_plural = 'User Roles'


class RoleApplication(models.Model):
    """Applications for specific roles submitted by users"""

    STATUS_CHOICES = (
        ('pending', 'Pending Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('withdrawn', 'Withdrawn'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='role_applications')
    requested_role = models.ForeignKey(UserRole, on_delete=models.CASCADE)
    motivation = models.TextField(help_text="Why do you want this role?")
    qualifications = models.TextField(help_text="What qualifies you for this role?")
    experience = models.TextField(blank=True, help_text="Relevant experience")
    portfolio_url = models.URLField(blank=True, help_text="Portfolio or work samples")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    reviewed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='role_reviews_made')
    reviewed_at = models.DateTimeField(null=True, blank=True)
    admin_notes = models.TextField(blank=True, help_text="Internal notes for admins")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} - {self.requested_role.display_name} ({self.status})"

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Role Application'
        verbose_name_plural = 'Role Applications'


class UserRoleAssignment(models.Model):
    """Assigns roles to users with approval workflow and expiration"""

    user_profile = models.ForeignKey('UserProfile', on_delete=models.CASCADE)
    role = models.ForeignKey(UserRole, on_delete=models.CASCADE)
    assigned_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='role_assignments_made')
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='role_approvals_made')
    assigned_at = models.DateTimeField(auto_now_add=True)
    approved_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True, help_text="Optional expiration date for temporary roles")
    is_active = models.BooleanField(default=True)
    is_approved = models.BooleanField(default=True)
    notes = models.TextField(blank=True, help_text="Notes about this role assignment")

    def __str__(self):
        return f"{self.user_profile.user.username} - {self.role.display_name}"

    @property
    def is_expired(self):
        """Check if the role assignment has expired"""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False

    class Meta:
        ordering = ['-assigned_at']
        unique_together = ['user_profile', 'role']
        verbose_name = 'User Role Assignment'
        verbose_name_plural = 'User Role Assignments'

class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    bio = models.TextField(max_length=500, blank=True)
    location = models.CharField(max_length=100, blank=True)
    expertise = models.CharField(max_length=100, blank=True)
    profile_image = models.ImageField(upload_to='profile_images/', storage=OptimizedImageStorage(), blank=True, null=True)
    website = models.URLField(blank=True)
    github = models.URLField(blank=True)
    linkedin = models.URLField(blank=True)
    twitter = models.URLField(blank=True)

    # Role-related fields
    primary_role = models.ForeignKey(UserRole, on_delete=models.SET_NULL, null=True, blank=True, related_name='primary_users')
    roles = models.ManyToManyField(UserRole, through='UserRoleAssignment', blank=True)

    # Additional profile fields
    company = models.CharField(max_length=100, blank=True)
    is_active = models.BooleanField(default=True)
    years_of_experience = models.PositiveIntegerField(default=0, validators=[MinValueValidator(0), MaxValueValidator(50)])

    # Language preference
    language = models.CharField(
        max_length=10,
        choices=[
            ('en', 'English'),
            ('ar', 'Arabic'),
        ],
        default='en',
        help_text="User's preferred language for the interface"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}'s profile"

    def get_active_roles(self):
        """Get all active roles for this user"""
        return self.roles.filter(
            userroleassignment__is_active=True,
            userroleassignment__is_approved=True
        )

    def has_role(self, role_name):
        """Check if user has a specific role"""
        return self.get_active_roles().filter(name=role_name).exists()

    def get_highest_permission_level(self):
        """Get the highest permission level from all active roles"""
        # Check Django user fields first for highest permissions
        if self.user.is_superuser:
            return 'super_admin'
        elif self.user.is_staff:
            return 'admin'

        active_roles = self.get_active_roles()
        if not active_roles.exists():
            return 'read'

        # Permission hierarchy: super_admin > admin > moderate > write > read
        permission_hierarchy = {'super_admin': 5, 'admin': 4, 'moderate': 3, 'write': 2, 'read': 1}
        highest_level = 'read'
        highest_value = 1

        for role in active_roles:
            level_value = permission_hierarchy.get(role.permission_level, 1)
            if level_value > highest_value:
                highest_value = level_value
                highest_level = role.permission_level

        return highest_level

    def calculate_completion_percentage(self):
        """Calculate the profile completion percentage based on filled fields."""
        # Define fields to check and their weights (importance)
        fields_to_check = {
            'bio': 20,           # Bio is very important
            'location': 10,      # Location is somewhat important
            'expertise': 15,     # Expertise is important
            'profile_image': 20, # Profile image is very important
            'website': 5,        # Social links are less important
            'github': 10,
            'linkedin': 10,
            'twitter': 5,
            'user.first_name': 2.5,  # Basic user info
            'user.last_name': 2.5,
        }

        total_weight = sum(fields_to_check.values())
        completed_weight = 0

        # Check each field
        for field, weight in fields_to_check.items():
            if '.' in field:  # Handle nested fields (user.first_name, etc.)
                parts = field.split('.')
                obj = self
                for part in parts:
                    obj = getattr(obj, part, None)
                if obj:
                    completed_weight += weight
            else:
                value = getattr(self, field, None)
                if value:
                    completed_weight += weight

        # Calculate percentage
        completion_percentage = (completed_weight / total_weight) * 100
        return round(completion_percentage, 1)

@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    if created:
        UserProfile.objects.create(user=instance)

@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    instance.profile.save()
