/**
 * AI-Powered Business Plan Generator
 * Advanced business plan creation with AI assistance and templates
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  FileText, Brain, Zap, Target, TrendingUp, Users, DollarSign,
  Calendar, CheckCircle, AlertCircle, Download, Share2, Edit3,
  Lightbulb, BarChart3, PieChart, LineChart, Sparkles
} from 'lucide-react';
import { useLanguage } from '../../hooks/useLanguage';
import { getAuthToken } from '../../services/api';

interface BusinessPlanSection {
  id: string;
  title: string;
  content: string;
  isCompleted: boolean;
  aiGenerated: boolean;
  lastUpdated: Date;
}

interface BusinessPlanData {
  id?: string;
  title: string;
  description: string;
  industry: string;
  targetMarket: string;
  sections: BusinessPlanSection[];
  financialProjections: {
    revenue: number[];
    expenses: number[];
    profit: number[];
    years: string[];
  };
  aiInsights: {
    strengths: string[];
    weaknesses: string[];
    opportunities: string[];
    threats: string[];
    recommendations: string[];
  };
}

const AIBusinessPlanGenerator: React.FC<{
  businessIdeaId?: number;
  onSave?: (plan: BusinessPlanData) => void;
  initialData?: Partial<BusinessPlanData>;
}> = ({ businessIdeaId, onSave, initialData }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [businessPlan, setBusinessPlan] = useState<BusinessPlanData>({
    title: '',
    description: '',
    industry: '',
    targetMarket: '',
    sections: [],
    financialProjections: {
      revenue: [0, 0, 0, 0, 0],
      expenses: [0, 0, 0, 0, 0],
      profit: [0, 0, 0, 0, 0],
      years: ['Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5']
    },
    aiInsights: {
      strengths: [],
      weaknesses: [],
      opportunities: [],
      threats: [],
      recommendations: []
    },
    ...initialData
  });

  const [currentStep, setCurrentStep] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [aiSuggestions, setAiSuggestions] = useState<string[]>([]);

  const planSteps = [
    { id: 'basic', title: t('businessPlan.basicInfo'), icon: FileText },
    { id: 'market', title: t('businessPlan.marketAnalysis'), icon: TrendingUp },
    { id: 'product', title: t('businessPlan.productService'), icon: Lightbulb },
    { id: 'marketing', title: t('businessPlan.marketingStrategy'), icon: Users },
    { id: 'financial', title: t('businessPlan.financialProjections'), icon: DollarSign },
    { id: 'review', title: t('businessPlan.review'), icon: CheckCircle }
  ];

  // ✅ REAL DATA: Fetch templates from API instead of hardcoded definitions
  const [businessPlanTemplates, setBusinessPlanTemplates] = useState<any[]>([]);
  const [templatesLoading, setTemplatesLoading] = useState(true);

  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        setTemplatesLoading(true);
        const response = await fetch('/api/incubator/business-plan-templates/');
        const data = await response.json();

        if (response.ok) {
          setBusinessPlanTemplates(data.results || data || []);
        } else {
          throw new Error(data.message || 'Failed to fetch templates');
        }
      } catch (error) {
        console.error('Error fetching business plan templates:', error);
        setBusinessPlanTemplates([]);
      } finally {
        setTemplatesLoading(false);
      }
    };

    fetchTemplates();
  }, []);
    {
      id: 'lean',
      title: t('businessPlan.templates.lean'),
      description: t('businessPlan.templates.leanDesc'),
      sections: [
        t("ai.problem", "Problem"),
        t("ai.solution", "Solution"),
        t("ai.unique.value.proposition", "Unique Value Proposition"),
        t("ai.unfair.advantage", "Unfair Advantage"),
        t("ai.customer.segments", "Customer Segments"),
        t("ai.key.metrics", "Key Metrics"),
        t("ai.channels", "Channels"),
        t("ai.cost.structure", "Cost Structure"),
        'Revenue Streams'
      ]
    },
    {
      id: 'saas',
      title: t('businessPlan.templates.saas'),
      description: t('businessPlan.templates.saasDesc'),
      sections: [
        'Executive Summary',
        'Product Overview',
        'Technology Stack',
        'User Personas',
        'Product Roadmap',
        'SaaS Metrics',
        'Pricing Strategy',
        'Go-to-Market Strategy'
      ]
    },
    {
      id: 'ecommerce',
      title: t('businessPlan.templates.ecommerce'),
      description: t('businessPlan.templates.ecommerceDesc'),
      sections: [
        'Executive Summary',
        'Product Catalog',
        'Target Market Analysis',
        'E-commerce Platform Strategy',
        'Digital Marketing Strategy',
        'Logistics & Fulfillment',
        t("ai.customer.service.strategy", "Customer Service Strategy"),
        'Financial Projections'
      ]
    },
    {
      id: 'restaurant',
      title: t('businessPlan.templates.restaurant'),
      description: t('businessPlan.templates.restaurantDesc'),
      sections: [
        t("ai.restaurant.concept", "Restaurant Concept"),
        t("ai.menu.development", "Menu Development"),
        t("ai.location.analysis", "Location Analysis"),
        t("ai.restaurant.operations", "Restaurant Operations"),
        t("ai.staffing.plan", "Staffing Plan"),
        'Marketing & Promotion',
        'Financial Projections'
      ]
    },
    {
      id: 'consulting',
      title: t('businessPlan.templates.consulting'),
      description: t('businessPlan.templates.consultingDesc'),
      sections: [
        t("ai.consulting.services.overview", "Consulting Services Overview"),
        t("ai.target.client.analysis", "Target Client Analysis"),
        t("ai.service.delivery.model", "Service Delivery Model"),
        t("ai.pricing.strategy", "Pricing Strategy"),
        'Business Development Strategy'
      ]
    },
    {
      id: 'mobile_app',
      title: t('businessPlan.templates.mobileApp'),
      description: t('businessPlan.templates.mobileAppDesc'),
      sections: [
        t("ai.app.concept", "App Concept"),
        t("ai.user.experience.design", "User Experience Design"),
        t("ai.technical.architecture", "Technical Architecture"),
        t("ai.monetization.strategy", "Monetization Strategy"),
        t("ai.user.acquisition.strategy", "User Acquisition Strategy"),
        'Development Roadmap'
      ]
    },
    {
      id: 'nonprofit',
      title: t('businessPlan.templates.nonprofit'),
      description: t('businessPlan.templates.nonprofitDesc'),
      sections: [
        'Mission & Vision',
        'Programs & Services',
        t("ai.target.beneficiaries", "Target Beneficiaries"),
        t("ai.fundraising.strategy", "Fundraising Strategy"),
        'Impact Measurement'
      ]
    },
    {
      id: 'service_business',
      title: t('businessPlan.templates.service'),
      description: t('businessPlan.templates.serviceDesc'),
      sections: [
        t("ai.executive.summary.service", "Executive Summary"),
        t("ai.service.description", "Service Description"),
        t("ai.market.analysis", "Market Analysis"),
        t("ai.competitive.analysis", "Competitive Analysis"),
        t("ai.marketing.strategy", "Marketing Strategy"),
        t("ai.service.delivery", "Service Delivery"),
        'Team & Management',
        t("ai.financial.projections", "Financial Projections"),
        'Growth Strategy'
      ]
    }
  ];

  useEffect(() => {
    if (businessIdeaId) {
      loadBusinessIdea();
    }
    initializeDefaultSections();
  }, [businessIdeaId]);

  const loadBusinessIdea = async () => {
    try {
      // Load business idea data and populate initial plan
      // This would typically come from an API call
    } catch (error) {
      console.error('Error loading business idea:', error);
    }
  };

  const initializeDefaultSections = () => {
    if (businessPlan.sections.length === 0) {
      const defaultSections: BusinessPlanSection[] = [
        {
          id: 'executive_summary',
          title: t('businessPlan.sections.executiveSummary'),
          content: '',
          isCompleted: false,
          aiGenerated: false,
          lastUpdated: new Date()
        },
        {
          id: 'company_description',
          title: t('businessPlan.sections.companyDescription'),
          content: '',
          isCompleted: false,
          aiGenerated: false,
          lastUpdated: new Date()
        },
        {
          id: 'market_analysis',
          title: t('businessPlan.sections.marketAnalysis'),
          content: '',
          isCompleted: false,
          aiGenerated: false,
          lastUpdated: new Date()
        },
        {
          id: 'products_services',
          title: t('businessPlan.sections.productsServices'),
          content: '',
          isCompleted: false,
          aiGenerated: false,
          lastUpdated: new Date()
        },
        {
          id: 'marketing_strategy',
          title: t('businessPlan.sections.marketingStrategy'),
          content: '',
          isCompleted: false,
          aiGenerated: false,
          lastUpdated: new Date()
        },
        {
          id: 'financial_projections',
          title: t('businessPlan.sections.financialProjections'),
          content: '',
          isCompleted: false,
          aiGenerated: false,
          lastUpdated: new Date()
        }
      ];

      setBusinessPlan(prev => ({ ...prev, sections: defaultSections }));
    }
  };

  const generateAIContent = async (sectionId: string) => {
    setIsGenerating(true);
    try {
      const section = businessPlan.sections.find(s => s.id === sectionId);
      if (!section) return;

      // Use real AI API to generate content
      const response = await fetch('/api/ai/generate-business-plan-section/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section_id: sectionId,
          section_title: section.title,
          business_title: businessPlan.title,
          industry: businessPlan.industry,
          target_market: businessPlan.targetMarket,
          description: businessPlan.description
        })
      });

      const data = await response.json();
      const generatedContent = data.content || `Generated content for ${section.title} section.`;

      updateSectionContent(sectionId, generatedContent, true);
    } catch (error) {
      console.error('Error generating AI content:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const updateSectionContent = (sectionId: string, content: string, aiGenerated: boolean = false) => {
    setBusinessPlan(prev => ({
      ...prev,
      sections: prev.sections.map(section =>
        section.id === sectionId
          ? {
              ...section,
              content,
              aiGenerated,
              isCompleted: content.trim().length > 0,
              lastUpdated: new Date()
            }
          : section
      )
    }));
  };

  const generateFinancialProjections = async () => {
    setIsGenerating(true);
    try {
      // Use real AI API to generate financial projections
      const response = await fetch('/api/ai/generate-financial-projections/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          business_title: businessPlan.title,
          industry: businessPlan.industry,
          target_market: businessPlan.targetMarket,
          description: businessPlan.description
        })
      });

      const data = await response.json();
      const projections = data.projections || {
        revenue: [0, 0, 0, 0, 0],
        expenses: [0, 0, 0, 0, 0],
        profit: [0, 0, 0, 0, 0],
        years: ['Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5']
      };

      setBusinessPlan(prev => ({
        ...prev,
        financialProjections: projections
      }));

    } catch (error) {
      console.error('Error generating financial projections:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const generateSWOTAnalysis = async () => {
    setIsGenerating(true);
    try {
      // Use real AI API to generate SWOT analysis
      const response = await fetch('/api/ai/generate-swot-analysis/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          business_title: businessPlan.title,
          industry: businessPlan.industry,
          target_market: businessPlan.targetMarket,
          description: businessPlan.description
        })
      });

      const data = await response.json();
      const swotAnalysis = data.swot || {
        strengths: [],
        weaknesses: [],
        opportunities: [],
        threats: [],
        recommendations: []
      };

      setBusinessPlan(prev => ({
        ...prev,
        aiInsights: swotAnalysis
      }));

    } catch (error) {
      console.error('Error generating SWOT analysis:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const applyTemplate = (templateId: string) => {
    const template = businessPlanTemplates.find(t => t.id === templateId);
    if (!template) return;

    const templateSections: BusinessPlanSection[] = template.sections.map((title, index) => ({
      id: title.toLowerCase().replace(/\s+/g, '_'),
      title,
      content: '',
      isCompleted: false,
      aiGenerated: false,
      lastUpdated: new Date()
    }));

    setBusinessPlan(prev => ({
      ...prev,
      sections: templateSections
    }));
    setSelectedTemplate(templateId);
  };

  const exportBusinessPlan = async () => {
    try {
      // Import the proper export function
      const { exportBusinessPlanToPDF } = await import('../../utils/exportUtils');

      // Convert the business plan format to match the expected format
      const formattedBusinessPlan = {
        id: null, // This is a generated plan, no ID
        title: businessPlan.title || 'Generated Business Plan',
        business_idea_title: businessPlan.description || '',
        completion_percentage: Math.round(
          (businessPlan.sections.filter(s => s.isCompleted).length / businessPlan.sections.length) * 100
        )
      };

      const formattedSections = businessPlan.sections.map((section, index) => ({
        id: section.id,
        title: section.title,
        content: section.content,
        is_completed: section.isCompleted,
        order: index + 1
      }));

      await exportBusinessPlanToPDF(formattedBusinessPlan, formattedSections);
    } catch (error) {
      console.error('Export failed:', error);
      // Fallback to simple text export
      const planContent = businessPlan.sections
        .map(section => `${section.title}\n\n${section.content}\n\n`)
        .join('');

      const blob = new Blob([planContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${businessPlan.title || 'business-plan'}.txt`;
      a.click();
      URL.revokeObjectURL(url);
    }
  };

  const savePlan = () => {
    if (onSave) {
      onSave(businessPlan);
    }
  };

  const completionPercentage = Math.round(
    (businessPlan.sections.filter(s => s.isCompleted).length / businessPlan.sections.length) * 100
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-900/30 to-purple-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50">
        <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
          <div>
            <h2 className={`text-2xl font-bold text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <Brain className={`mr-3 text-purple-400 ${isRTL ? "space-x-reverse" : ""}`} size={28} />
              {t('businessPlan.aiGenerator')}
            </h2>
            <p className="text-gray-300 mt-2">{t('businessPlan.aiGeneratorDescription')}</p>
          </div>

          <div className={`flex items-center space-x-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-400">{completionPercentage}%</div>
              <div className="text-sm text-gray-400">{t('businessPlan.completed')}</div>
            </div>

            <div className={`flex space-x-2 ${isRTL ? "flex-row-reverse" : ""}`}>
              <button
                onClick={savePlan}
                className={`bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <CheckCircle size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                {t('businessPlan.save')}
              </button>

              <button
                onClick={exportBusinessPlan}
                className={`bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
              >
                <Download size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                {t('businessPlan.export')}
              </button>
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mt-4">
          <div className="bg-gray-700 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${completionPercentage}%` }}
            />
          </div>
        </div>
      </div>

      {/* Template Selection */}
      {!selectedTemplate && (
        <div className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50">
          <h3 className={`text-xl font-semibold text-white mb-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <FileText className={`mr-2 text-blue-400 ${isRTL ? "space-x-reverse" : ""}`} size={20} />
            {t('businessPlan.selectTemplate')}
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {businessPlanTemplates.map(template => (
              <div
                key={template.id}
                onClick={() => applyTemplate(template.id)}
                className="bg-indigo-800/30 border border-indigo-700/50 rounded-lg p-4 cursor-pointer hover:border-purple-500/50 transition-colors"
              >
                <h4 className="text-white font-medium mb-2">{template.title}</h4>
                <p className="text-gray-300 text-sm mb-3">{template.description}</p>
                <div className="text-xs text-gray-400">
                  {template.sections.length} {t('businessPlan.sections')}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Basic Information */}
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50">
        <h3 className={`text-xl font-semibold text-white mb-4 flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
          <Target className={`mr-2 text-green-400 ${isRTL ? "space-x-reverse" : ""}`} size={20} />
          {t('businessPlan.basicInformation')}
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('businessPlan.businessTitle')}
            </label>
            <input
              type="text"
              value={businessPlan.title}
              onChange={(e) => setBusinessPlan(prev => ({ ...prev, title: e.target.value }))}
              className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white placeholder-gray-400"
              placeholder={t('businessPlan.businessTitlePlaceholder')}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('businessPlan.industry')}
            </label>
            <select
              value={businessPlan.industry}
              onChange={(e) => setBusinessPlan(prev => ({ ...prev, industry: e.target.value }))}
              className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white"
            >
              <option value="">{t('businessPlan.selectIndustry')}</option>
              <option value="technology">{t('businessPlan.industries.technology')}</option>
              <option value="healthcare">{t('businessPlan.industries.healthcare')}</option>
              <option value="education">{t('businessPlan.industries.education')}</option>
              <option value="retail">{t('businessPlan.industries.retail')}</option>
              <option value="manufacturing">{t('businessPlan.industries.manufacturing')}</option>
              <option value="services">{t('businessPlan.industries.services')}</option>
            </select>
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {t('businessPlan.description')}
            </label>
            <textarea
              value={businessPlan.description}
              onChange={(e) => setBusinessPlan(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white placeholder-gray-400"
              placeholder={t('businessPlan.descriptionPlaceholder')}
            />
          </div>
        </div>
      </div>

      {/* Business Plan Sections */}
      <div className="space-y-4">
        {businessPlan.sections.map((section, index) => (
          <div key={section.id} className="bg-indigo-900/30 backdrop-blur-sm rounded-xl border border-indigo-800/50 overflow-hidden">
            <div className="bg-indigo-800/50 p-4 border-b border-indigo-700/50">
              <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse" : ""}`}>
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    section.isCompleted ? 'bg-green-500' : 'bg-gray-600'}
                  }`}>
                    {section.isCompleted ? (
                      <CheckCircle size={16} className="text-white" />
                    ) : (
                      <span className="text-white text-sm">{index + 1}</span>
                    )}
                  </div>
                  <h4 className="text-white font-medium">{section.title}</h4>
                  {section.aiGenerated && (
                    <div className={`flex items-center space-x-1 bg-purple-500/20 px-2 py-1 rounded-full ${isRTL ? "flex-row-reverse" : ""}`}>
                      <Sparkles size={12} className="text-purple-400" />
                      <span className="text-purple-400 text-xs">{t('businessPlan.aiGenerated')}</span>
                    </div>
                  )}
                </div>

                <button
                  onClick={() => generateAIContent(section.id)}
                  disabled={isGenerating}
                  className={`bg-purple-500 hover:bg-purple-600 disabled:bg-gray-600 text-white px-3 py-1 rounded-lg flex items-center text-sm transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
                >
                  {isGenerating ? (
                    <div className={`animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
                  ) : (
                    <Zap size={14} className={`mr-1 ${isRTL ? "space-x-reverse" : ""}`} />
                  )}
                  {t('businessPlan.generateWithAI')}
                </button>
              </div>
            </div>

            <div className="p-4">
              <textarea
                value={section.content}
                onChange={(e) => updateSectionContent(section.id, e.target.value)}
                rows={6}
                className="w-full bg-indigo-800/50 border border-indigo-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 resize-none"
                placeholder={t('businessPlan.sectionPlaceholder', { section: section.title })}
              />

              {section.lastUpdated && (
                <div className="text-xs text-gray-400 mt-2">
                  {t('businessPlan.lastUpdated')}: {section.lastUpdated.toLocaleString()}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* AI Insights Panel */}
      <div className="bg-indigo-900/30 backdrop-blur-sm rounded-xl p-6 border border-indigo-800/50">
        <div className={`flex items-center justify-between mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          <h3 className={`text-xl font-semibold text-white flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
            <BarChart3 className={`mr-2 text-yellow-400 ${isRTL ? "space-x-reverse" : ""}`} size={20} />
            {t('businessPlan.aiInsights')}
          </h3>

          <button
            onClick={generateSWOTAnalysis}
            disabled={isGenerating}
            className={`bg-yellow-500 hover:bg-yellow-600 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors ${isRTL ? "flex-row-reverse" : ""}`}
          >
            {isGenerating ? (
              <div className={`animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 ${isRTL ? "space-x-reverse" : ""}`}></div>
            ) : (
              <Brain size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
            )}
            {t('businessPlan.generateSWOT')}
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-4">
            <h4 className="text-green-400 font-medium mb-2">{t('businessPlan.strengths')}</h4>
            <ul className="text-sm text-gray-300 space-y-1">
              {businessPlan.aiInsights.strengths.map((strength, index) => (
                <li key={index}>• {strength}</li>
              ))}
            </ul>
          </div>

          <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4">
            <h4 className="text-red-400 font-medium mb-2">{t('businessPlan.weaknesses')}</h4>
            <ul className="text-sm text-gray-300 space-y-1">
              {businessPlan.aiInsights.weaknesses.map((weakness, index) => (
                <li key={index}>• {weakness}</li>
              ))}
            </ul>
          </div>

          <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-4">
            <h4 className="text-blue-400 font-medium mb-2">{t('businessPlan.opportunities')}</h4>
            <ul className="text-sm text-gray-300 space-y-1">
              {businessPlan.aiInsights.opportunities.map((opportunity, index) => (
                <li key={index}>• {opportunity}</li>
              ))}
            </ul>
          </div>

          <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4">
            <h4 className="text-yellow-400 font-medium mb-2">{t('businessPlan.threats')}</h4>
            <ul className="text-sm text-gray-300 space-y-1">
              {businessPlan.aiInsights.threats.map((threat, index) => (
                <li key={index}>• {threat}</li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIBusinessPlanGenerator;
