/**
 * Reports API Service
 * Handles all reporting and report management API calls
 */

import { apiRequest } from './api';

// Report Types
export interface Report {
  id: string;
  type: 'post' | 'comment' | 'user' | 'business_idea' | 'business_plan' | 'resource';
  reportedItemId: string;
  reportedItemTitle: string;
  reportedBy: {
    id: string;
    name: string;
    email: string;
  };
  reportedUser: {
    id: string;
    name: string;
    email: string;
  };
  reason: string;
  category: 'spam' | 'harassment' | 'inappropriate' | 'copyright' | 'misinformation' | 'other';
  description: string;
  status: 'pending' | 'under_review' | 'resolved' | 'dismissed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
  resolvedBy?: {
    id: string;
    name: string;
  };
  resolution?: string;
  evidence?: Array<{
    type: 'screenshot' | 'link' | 'text';
    content: string;
    uploadedAt: string;
  }>;
}

export interface ReportFilters {
  status?: string;
  category?: string;
  priority?: string;
  type?: string;
  dateFrom?: string;
  dateTo?: string;
  reportedBy?: string;
  reportedUser?: string;
}

export interface ReportStats {
  total: number;
  pending: number;
  underReview: number;
  resolved: number;
  dismissed: number;
  byCategory: Record<string, number>;
  byPriority: Record<string, number>;
  byType: Record<string, number>;
  averageResolutionTime: number; // in hours
  topReporters: Array<{
    userId: string;
    userName: string;
    reportCount: number;
  }>;
  topReportedUsers: Array<{
    userId: string;
    userName: string;
    reportCount: number;
  }>;
}

/**
 * Reports Management API
 */
export const reportsApi = {
  // Get all reports with filters
  async getReports(filters?: ReportFilters): Promise<Report[]> {
    try {
      const params = new URLSearchParams();
      if (filters?.status) params.append('status', filters.status);
      if (filters?.category) params.append('category', filters.category);
      if (filters?.priority) params.append('priority', filters.priority);
      if (filters?.type) params.append('type', filters.type);
      if (filters?.dateFrom) params.append('date_from', filters.dateFrom);
      if (filters?.dateTo) params.append('date_to', filters.dateTo);
      if (filters?.reportedBy) params.append('reported_by', filters.reportedBy);
      if (filters?.reportedUser) params.append('reported_user', filters.reportedUser);

      const queryString = params.toString();
      const endpoint = `/api/reports/${queryString ? `?${queryString}` : ''}`;
      
      const response = await apiRequest<{ results: Report[] }>(endpoint, 'GET');
      return response.results || [];
    } catch (error) {
      console.error('Failed to fetch reports:', error);
      throw error;
    }
  },

  // Get specific report
  async getReport(id: string): Promise<Report> {
    try {
      return await apiRequest<Report>(`/api/reports/${id}/`, 'GET');
    } catch (error) {
      console.error('Failed to fetch report:', error);
      throw error;
    }
  },

  // Create new report
  async createReport(reportData: {
    type: Report['type'];
    reportedItemId: string;
    reportedUserId: string;
    reason: string;
    category: Report['category'];
    description: string;
    evidence?: Array<{
      type: 'screenshot' | 'link' | 'text';
      content: string;
    }>;
  }): Promise<Report> {
    try {
      return await apiRequest<Report>('/api/reports/', 'POST', reportData);
    } catch (error) {
      console.error('Failed to create report:', error);
      throw error;
    }
  },

  // Update report status
  async updateReportStatus(
    id: string, 
    status: Report['status'], 
    resolution?: string
  ): Promise<Report> {
    try {
      return await apiRequest<Report>(`/api/reports/${id}/`, 'PATCH', {
        status,
        resolution
      });
    } catch (error) {
      console.error('Failed to update report status:', error);
      throw error;
    }
  },

  // Update report priority
  async updateReportPriority(id: string, priority: Report['priority']): Promise<Report> {
    try {
      return await apiRequest<Report>(`/api/reports/${id}/`, 'PATCH', {
        priority
      });
    } catch (error) {
      console.error('Failed to update report priority:', error);
      throw error;
    }
  },

  // Assign report to moderator
  async assignReport(id: string, moderatorId: string): Promise<Report> {
    try {
      return await apiRequest<Report>(`/api/reports/${id}/assign/`, 'POST', {
        moderator_id: moderatorId
      });
    } catch (error) {
      console.error('Failed to assign report:', error);
      throw error;
    }
  },

  // Add evidence to report
  async addEvidence(id: string, evidence: {
    type: 'screenshot' | 'link' | 'text';
    content: string;
  }): Promise<Report> {
    try {
      return await apiRequest<Report>(`/api/reports/${id}/evidence/`, 'POST', evidence);
    } catch (error) {
      console.error('Failed to add evidence:', error);
      throw error;
    }
  },

  // Delete report
  async deleteReport(id: string): Promise<void> {
    try {
      await apiRequest(`/api/reports/${id}/`, 'DELETE');
    } catch (error) {
      console.error('Failed to delete report:', error);
      throw error;
    }
  }
};

/**
 * Report Statistics API
 */
export const reportStatsApi = {
  // Get report statistics
  async getStats(timeRange?: 'week' | 'month' | 'quarter' | 'year'): Promise<ReportStats> {
    try {
      const params = timeRange ? `?range=${timeRange}` : '';
      return await apiRequest<ReportStats>(`/api/reports/stats/${params}`, 'GET');
    } catch (error) {
      console.error('Failed to fetch report stats:', error);
      throw error;
    }
  },

  // Get trending reports
  async getTrendingReports(): Promise<Report[]> {
    try {
      const response = await apiRequest<{ results: Report[] }>('/api/reports/trending/', 'GET');
      return response.results || [];
    } catch (error) {
      console.error('Failed to fetch trending reports:', error);
      throw error;
    }
  },

  // Get report analytics
  async getAnalytics(): Promise<any> {
    try {
      return await apiRequest('/api/reports/analytics/', 'GET');
    } catch (error) {
      console.error('Failed to fetch report analytics:', error);
      throw error;
    }
  }
};

/**
 * Report Categories API
 */
export const reportCategoriesApi = {
  // Get all report categories
  async getCategories(): Promise<Array<{
    id: string;
    name: string;
    description: string;
    severity: 'low' | 'medium' | 'high';
  }>> {
    try {
      const response = await apiRequest<{ results: any[] }>('/api/reports/categories/', 'GET');
      return response.results || [];
    } catch (error) {
      console.error('Failed to fetch report categories:', error);
      return [
        { id: 'spam', name: 'Spam', description: 'Unwanted promotional content', severity: 'medium' },
        { id: 'harassment', name: 'Harassment', description: 'Bullying or harassment', severity: 'high' },
        { id: 'inappropriate', name: 'Inappropriate Content', description: 'Content not suitable for platform', severity: 'medium' },
        { id: 'copyright', name: 'Copyright Violation', description: 'Unauthorized use of copyrighted material', severity: 'high' },
        { id: 'misinformation', name: 'Misinformation', description: 'False or misleading information', severity: 'high' },
        { id: 'other', name: 'Other', description: 'Other issues not covered above', severity: 'low' }
      ];
    }
  }
};

// Export all report APIs
export const reportApi = {
  reports: reportsApi,
  stats: reportStatsApi,
  categories: reportCategoriesApi
};

export default reportApi;
