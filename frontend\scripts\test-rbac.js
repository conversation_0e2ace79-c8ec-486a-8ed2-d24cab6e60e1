#!/usr/bin/env node

/**
 * RBAC TESTING SCRIPT
 * Comprehensive test runner for RBAC system validation
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 RBAC System Testing Suite');
console.log('============================\n');

// Test configuration
const testConfig = {
  testDir: path.join(__dirname, '../src'),
  coverageThreshold: {
    statements: 80,
    branches: 80,
    functions: 80,
    lines: 80
  },
  testPatterns: [
    '__tests__/rbac-integration.test.ts',
    'utils/__tests__/unifiedRoleManager.test.ts',
    'config/__tests__/navigationConfig.test.ts',
    'components/auth/__tests__/RoleProtectedRoute.test.tsx'
  ]
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✅${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}❌${colors.reset} ${msg}`),
  header: (msg) => console.log(`\n${colors.bright}${colors.cyan}${msg}${colors.reset}\n`)
};

// Test phases
const testPhases = [
  {
    name: 'Unit Tests - Unified Role Manager',
    command: 'npm test -- utils/__tests__/unifiedRoleManager.test.ts --verbose',
    description: 'Testing core RBAC functionality'
  },
  {
    name: 'Unit Tests - Navigation Configuration',
    command: 'npm test -- config/__tests__/navigationConfig.test.ts --verbose',
    description: 'Testing navigation role-based access'
  },
  {
    name: 'Component Tests - Route Protection',
    command: 'npm test -- components/auth/__tests__/RoleProtectedRoute.test.tsx --verbose',
    description: 'Testing route protection components'
  },
  {
    name: 'Integration Tests - Complete RBAC System',
    command: 'npm test -- __tests__/rbac-integration.test.ts --verbose',
    description: 'Testing end-to-end RBAC integration'
  },
  {
    name: 'Coverage Report',
    command: 'npm test -- --coverage --coverageDirectory=coverage/rbac',
    description: 'Generating test coverage report'
  }
];

// Security validation checks
const securityChecks = [
  {
    name: 'Mock Data Elimination',
    check: () => {
      log.info('Scanning for remaining mock data patterns...');
      
      const mockDataPatterns = [
        /setTimeout.*setLoading\(false\)/g,
        /setTimeout.*setData/g,
        /setTimeout.*mockData/g,
        /setTimeout.*demoData/g,
        /const\s+mock\w+\s*=\s*\[/g,
        /const\s+demo\w+\s*=\s*\[/g
      ];
      
      const scanDirectory = (dir) => {
        const files = fs.readdirSync(dir);
        let violations = [];
        
        files.forEach(file => {
          const filePath = path.join(dir, file);
          const stat = fs.statSync(filePath);
          
          if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
            violations = violations.concat(scanDirectory(filePath));
          } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
            const content = fs.readFileSync(filePath, 'utf8');
            
            mockDataPatterns.forEach((pattern, index) => {
              const matches = content.match(pattern);
              if (matches) {
                violations.push({
                  file: filePath,
                  pattern: pattern.toString(),
                  matches: matches.length
                });
              }
            });
          }
        });
        
        return violations;
      };
      
      const violations = scanDirectory(path.join(__dirname, '../src/pages'));
      
      if (violations.length === 0) {
        log.success('No mock data patterns found');
        return true;
      } else {
        log.warning(`Found ${violations.length} potential mock data violations:`);
        violations.forEach(v => {
          console.log(`  - ${v.file}: ${v.matches} matches for ${v.pattern}`);
        });
        return false;
      }
    }
  },
  {
    name: 'Role Consistency Validation',
    check: () => {
      log.info('Validating role consistency across files...');
      
      try {
        // Check if all role definitions are consistent
        const unifiedRoleManager = path.join(__dirname, '../src/utils/unifiedRoleManager.ts');
        const navigationConfig = path.join(__dirname, '../src/config/navigationConfig.ts');
        
        if (!fs.existsSync(unifiedRoleManager)) {
          log.error('unifiedRoleManager.ts not found');
          return false;
        }
        
        if (!fs.existsSync(navigationConfig)) {
          log.error('navigationConfig.ts not found');
          return false;
        }
        
        log.success('Role definition files exist and are accessible');
        return true;
      } catch (error) {
        log.error(`Role consistency check failed: ${error.message}`);
        return false;
      }
    }
  },
  {
    name: 'Duplicate Code Detection',
    check: () => {
      log.info('Checking for duplicate role management code...');
      
      const deprecatedFiles = [
        path.join(__dirname, '../src/utils/roleUtils.ts'),
        path.join(__dirname, '../src/utils/roleBasedRouting_deprecated.ts')
      ];
      
      let foundDuplicates = false;
      deprecatedFiles.forEach(file => {
        if (fs.existsSync(file)) {
          log.warning(`Found deprecated file: ${file}`);
          foundDuplicates = true;
        }
      });
      
      if (!foundDuplicates) {
        log.success('No duplicate role management files found');
        return true;
      } else {
        log.error('Duplicate role management files detected');
        return false;
      }
    }
  }
];

// Main test execution
async function runTests() {
  log.header('🚀 Starting RBAC System Validation');
  
  let allTestsPassed = true;
  let totalTests = 0;
  let passedTests = 0;
  
  // Run security checks first
  log.header('🔒 Security Validation Checks');
  
  for (const check of securityChecks) {
    log.info(`Running: ${check.name}`);
    const passed = check.check();
    
    if (passed) {
      passedTests++;
      log.success(`${check.name} - PASSED`);
    } else {
      allTestsPassed = false;
      log.error(`${check.name} - FAILED`);
    }
    totalTests++;
  }
  
  // Run test phases
  log.header('🧪 Test Execution Phases');
  
  for (const phase of testPhases) {
    log.info(`Running: ${phase.name}`);
    log.info(`Description: ${phase.description}`);
    
    try {
      execSync(phase.command, { 
        stdio: 'inherit',
        cwd: path.join(__dirname, '..')
      });
      
      passedTests++;
      log.success(`${phase.name} - PASSED`);
    } catch (error) {
      allTestsPassed = false;
      log.error(`${phase.name} - FAILED`);
      console.error(error.message);
    }
    totalTests++;
  }
  
  // Final results
  log.header('📊 Test Results Summary');
  
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${colors.green}${passedTests}${colors.reset}`);
  console.log(`Failed: ${colors.red}${totalTests - passedTests}${colors.reset}`);
  console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
  
  if (allTestsPassed) {
    log.success('🎉 ALL RBAC TESTS PASSED!');
    log.success('✅ RBAC System is production-ready');
    process.exit(0);
  } else {
    log.error('❌ Some tests failed');
    log.error('🔧 Please fix the issues before deploying');
    process.exit(1);
  }
}

// Error handling
process.on('uncaughtException', (error) => {
  log.error(`Uncaught Exception: ${error.message}`);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  log.error(`Unhandled Rejection at: ${promise}, reason: ${reason}`);
  process.exit(1);
});

// Run the tests
if (require.main === module) {
  runTests().catch(error => {
    log.error(`Test execution failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { runTests, testConfig, securityChecks };
