// ***********************************************************
// This example support/component.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands'

// Import React and testing utilities
import React from 'react'
import { mount } from 'cypress/react18'
import { Provider } from 'react-redux'
import { BrowserRouter } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { I18nextProvider } from 'react-i18next'

// Import your app's store and i18n configuration
import { store } from '../../src/store/store'
import i18n from '../../src/i18n/config'

// Import global styles
import '../../src/index.css'

// Augment the Cypress namespace to include type definitions for
// your custom command.
// Alternatively, can be defined in cypress/support/component.d.ts
// with a <reference path="./component" /> at the top of your spec.
declare global {
  namespace Cypress {
    interface Chainable {
      mount(
        component: React.ReactNode,
        options?: {
          reduxStore?: any
          routerProps?: any
          queryClient?: QueryClient
          i18nInstance?: any
        }
      ): Chainable<any>
    }
  }
}

// Create a custom mount command that wraps components with necessary providers
Cypress.Commands.add('mount', (component, options = {}) => {
  const {
    reduxStore = store,
    routerProps = {},
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    }),
    i18nInstance = i18n,
  } = options

  const wrapped = (
    <Provider store={reduxStore}>
      <QueryClientProvider client={queryClient}>
        <I18nextProvider i18n={i18nInstance}>
          <BrowserRouter {...routerProps}>
            {component}
          </BrowserRouter>
        </I18nextProvider>
      </QueryClientProvider>
    </Provider>
  )

  return mount(wrapped)
})

// Example use:
// cy.mount(<MyComponent />)

// Global configuration for component testing
beforeEach(() => {
  // Reset any global state before each test
  cy.window().then((win) => {
    win.localStorage.clear()
    win.sessionStorage.clear()
  })
})

// Add custom assertions for React components
chai.use((chai, utils) => {
  chai.Assertion.addMethod('toHaveText', function (expectedText) {
    const obj = this._obj
    this.assert(
      obj.text().includes(expectedText),
      `expected element to contain text "${expectedText}"`,
      `expected element not to contain text "${expectedText}"`
    )
  })

  chai.Assertion.addMethod('toBeVisible', function () {
    const obj = this._obj
    this.assert(
      obj.is(':visible'),
      'expected element to be visible',
      'expected element not to be visible'
    )
  })

  chai.Assertion.addMethod('toHaveClass', function (className) {
    const obj = this._obj
    this.assert(
      obj.hasClass(className),
      `expected element to have class "${className}"`,
      `expected element not to have class "${className}"`
    )
  })
})

// Helper functions for component testing
export const createMockStore = (initialState = {}) => {
  return {
    ...store,
    getState: () => ({
      auth: {
        user: null,
        token: null,
        isAuthenticated: false,
        loading: false,
        error: null,
      },
      ui: {
        sidebarCollapsed: false,
        theme: 'light',
        language: 'en',
      },
      ...initialState,
    }),
  }
}

export const createMockQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        cacheTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  })
}

// Mock implementations for common hooks and services
export const mockHooks = {
  useAuth: (overrides = {}) => ({
    user: null,
    isAuthenticated: false,
    login: cy.stub(),
    logout: cy.stub(),
    loading: false,
    error: null,
    ...overrides,
  }),

  useBusinessPlans: (overrides = {}) => ({
    businessPlans: [],
    loading: false,
    error: null,
    createBusinessPlan: cy.stub(),
    updateBusinessPlan: cy.stub(),
    deleteBusinessPlan: cy.stub(),
    ...overrides,
  }),

  useMentorship: (overrides = {}) => ({
    mentors: [],
    sessions: [],
    loading: false,
    error: null,
    requestMentor: cy.stub(),
    scheduleSession: cy.stub(),
    ...overrides,
  }),
}

// Component testing utilities
export const componentTestUtils = {
  // Render component with all providers
  renderWithProviders: (component: React.ReactNode, options = {}) => {
    return cy.mount(component, options)
  },

  // Render component with mock data
  renderWithMocks: (component: React.ReactNode, mocks = {}) => {
    const mockStore = createMockStore(mocks.storeState)
    const mockQueryClient = createMockQueryClient()

    return cy.mount(component, {
      reduxStore: mockStore,
      queryClient: mockQueryClient,
      ...mocks,
    })
  },

  // Test component accessibility
  testAccessibility: (component: React.ReactNode) => {
    cy.mount(component)
    cy.injectAxe()
    cy.checkA11y()
  },

  // Test component responsiveness
  testResponsiveness: (component: React.ReactNode) => {
    const viewports = [
      { width: 320, height: 568 },  // Mobile
      { width: 768, height: 1024 }, // Tablet
      { width: 1920, height: 1080 } // Desktop
    ]

    viewports.forEach((viewport) => {
      cy.viewport(viewport.width, viewport.height)
      cy.mount(component)
      cy.get('[data-cy-root]').should('be.visible')
    })
  },
}

// Export utilities for use in tests
export { mockHooks, componentTestUtils }
