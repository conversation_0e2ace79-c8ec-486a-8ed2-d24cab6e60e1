import { TFunction } from 'i18next';

/**
 * Translation utilities for managing and validating translations
 */

export interface TranslationValidationResult {
  isValid: boolean;
  missingKeys: string[];
  duplicateKeys: string[];
  unusedKeys: string[];
  totalKeys: number;
  coverage: number;
}

export interface TranslationKey {
  key: string;
  value: string;
  namespace?: string;
  context?: string;
}

/**
 * Extract all translation keys from a nested object
 */
export const extractTranslationKeys = (
  obj: any,
  prefix: string = '',
  keys: string[] = []
): string[] => {
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const fullKey = prefix ? `${prefix}.${key}` : key;
      
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        extractTranslationKeys(obj[key], fullKey, keys);
      } else {
        keys.push(fullKey);
      }
    }
  }
  return keys;
};

/**
 * Find missing translation keys between two language objects
 */
export const findMissingKeys = (
  sourceTranslations: any,
  targetTranslations: any
): string[] => {
  const sourceKeys = extractTranslationKeys(sourceTranslations);
  const targetKeys = extractTranslationKeys(targetTranslations);
  
  return sourceKeys.filter(key => !targetKeys.includes(key));
};

/**
 * Find duplicate keys in translation object
 */
export const findDuplicateKeys = (translations: any): string[] => {
  const keys = extractTranslationKeys(translations);
  const duplicates: string[] = [];
  const seen = new Set<string>();
  
  keys.forEach(key => {
    if (seen.has(key)) {
      duplicates.push(key);
    } else {
      seen.add(key);
    }
  });
  
  return duplicates;
};

/**
 * Validate translation completeness between languages
 */
export const validateTranslations = (
  enTranslations: any,
  arTranslations: any
): TranslationValidationResult => {
  const enKeys = extractTranslationKeys(enTranslations);
  const arKeys = extractTranslationKeys(arTranslations);
  
  const missingInArabic = enKeys.filter(key => !arKeys.includes(key));
  const missingInEnglish = arKeys.filter(key => !enKeys.includes(key));
  const duplicateEnKeys = findDuplicateKeys(enTranslations);
  const duplicateArKeys = findDuplicateKeys(arTranslations);
  
  const totalKeys = Math.max(enKeys.length, arKeys.length);
  const coverage = totalKeys > 0 ? ((totalKeys - missingInArabic.length) / totalKeys) * 100 : 100;
  
  return {
    isValid: missingInArabic.length === 0 && missingInEnglish.length === 0,
    missingKeys: [...missingInArabic, ...missingInEnglish],
    duplicateKeys: [...duplicateEnKeys, ...duplicateArKeys],
    unusedKeys: [], // This would require code analysis to determine
    totalKeys,
    coverage
  };
};

/**
 * Get nested value from object using dot notation
 */
export const getNestedValue = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
};

/**
 * Set nested value in object using dot notation
 */
export const setNestedValue = (obj: any, path: string, value: any): void => {
  const keys = path.split('.');
  const lastKey = keys.pop();
  
  if (!lastKey) return;
  
  const target = keys.reduce((current, key) => {
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {};
    }
    return current[key];
  }, obj);
  
  target[lastKey] = value;
};

/**
 * Flatten nested translation object to dot notation
 */
export const flattenTranslations = (
  obj: any,
  prefix: string = ''
): Record<string, string> => {
  const flattened: Record<string, string> = {};
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const fullKey = prefix ? `${prefix}.${key}` : key;
      
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        Object.assign(flattened, flattenTranslations(obj[key], fullKey));
      } else {
        flattened[fullKey] = obj[key];
      }
    }
  }
  
  return flattened;
};

/**
 * Unflatten dot notation object to nested structure
 */
export const unflattenTranslations = (
  flattened: Record<string, string>
): any => {
  const result = {};
  
  for (const key in flattened) {
    if (flattened.hasOwnProperty(key)) {
      setNestedValue(result, key, flattened[key]);
    }
  }
  
  return result;
};

/**
 * Safe translation function with fallback
 */
export const safeTranslate = (
  t: TFunction,
  key: string,
  fallback?: string,
  options?: any
): string => {
  try {
    const translation = t(key, options);

    // Check if translation returned an object (nested structure)
    if (typeof translation === 'object' && translation !== null) {
      console.warn(`Translation key "${key}" returned an object. Use specific nested key instead.`);
      return fallback || key;
    }

    // Check if translation returned the key itself (meaning it wasn't found)
    if (translation === key) {
      return fallback || key;
    }

    return String(translation);
  } catch (error) {
    console.warn(`Translation error for key "${key}":`, error);
    return fallback || key;
  }
};

/**
 * Get nested translation with proper key path
 */
export const getNestedTranslation = (
  t: TFunction,
  baseKey: string,
  nestedKey: string,
  fallback?: string
): string => {
  const fullKey = `${baseKey}.${nestedKey}`;
  return safeTranslate(t, fullKey, fallback);
};

/**
 * Generate translation template for missing keys
 */
export const generateTranslationTemplate = (
  missingKeys: string[],
  defaultValue: string = ''
): any => {
  const template = {};
  
  missingKeys.forEach(key => {
    setNestedValue(template, key, defaultValue || `[Missing: ${key}]`);
  });
  
  return template;
};

/**
 * Merge translation objects with conflict resolution
 */
export const mergeTranslations = (
  target: any,
  source: any,
  conflictResolution: 'target' | 'source' | 'merge' = 'source'
): any => {
  if (!target || typeof target !== 'object') return source || {};
  if (!source || typeof source !== 'object') return target || {};

  const result = { ...target };
  
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      if (typeof source[key] === 'object' && source[key] !== null) {
        if (typeof result[key] === 'object' && result[key] !== null) {
          result[key] = mergeTranslations(result[key], source[key], conflictResolution);
        } else {
          result[key] = source[key];
        }
      } else {
        if (conflictResolution === 'source' || !result.hasOwnProperty(key)) {
          result[key] = source[key];
        } else if (conflictResolution === 'merge' && result.hasOwnProperty(key)) {
          // For merge strategy, you could implement custom logic here
          result[key] = source[key];
        }
        // For 'target' strategy, keep existing value
      }
    }
  }
  
  return result;
};

/**
 * Export translations to different formats
 */
export const exportTranslations = (
  translations: any,
  format: 'json' | 'csv' | 'xlsx' = 'json'
): string => {
  switch (format) {
    case 'json':
      return JSON.stringify(translations, null, 2);
    
    case 'csv':
      const flattened = flattenTranslations(translations);
      const csvRows = ['Key,Value'];
      
      for (const [key, value] of Object.entries(flattened)) {
        csvRows.push(`"${key}","${value}"`);
      }
      
      return csvRows.join('\n');
    
    default:
      return JSON.stringify(translations, null, 2);
  }
};

/**
 * Import translations from different formats
 */
export const importTranslations = (
  data: string,
  format: 'json' | 'csv' = 'json'
): any => {
  switch (format) {
    case 'json':
      try {
        return JSON.parse(data);
      } catch (error) {
        throw new Error('Invalid JSON format');
      }
    
    case 'csv':
      const lines = data.split('\n');
      const headers = lines[0].split(',');
      
      if (headers.length < 2) {
        throw new Error('CSV must have at least Key and Value columns');
      }
      
      const flattened: Record<string, string> = {};
      
      for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(',');
        if (values.length >= 2) {
          const key = values[0].replace(/"/g, '');
          const value = values[1].replace(/"/g, '');
          flattened[key] = value;
        }
      }
      
      return unflattenTranslations(flattened);
    
    default:
      throw new Error('Unsupported format');
  }
};
